services:
  payload:
    image: node-app:v0.0.1
    build:
      context: .
      dockerfile: dev.Dockerfile
    ports:
      - '3000:3000'
    volumes:
      - ./src:/app/src
    depends_on:
      - postgres
    env_file:
      - .env.development

  postgres:
    restart: always
    image: postgres:latest
    volumes:
      - pgdata-dev:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    env_file:
      - .env.development
    environment:
      POSTGRES_USER: ${PG_USERNAME}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
      POSTGRES_DB: ${PG_DATABASE}

volumes:
  pgdata-dev:
