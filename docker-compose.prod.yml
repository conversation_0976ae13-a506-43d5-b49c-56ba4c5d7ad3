secrets:
  cloudflare-token:
    file: "./traefik/secrets/cloudflare-token.secret"
  cloudflare-email:
    file: "./traefik/secrets/cloudflare-email.secret"

services:
  traefik:
    image: traefik:v3.4
    container_name: traefik
    restart: unless-stopped
    command:
      # - --log.level=DEBUG
      #- --log.filePath=/letsencrypt/traefik.log
      - --providers.docker=true
      - --api.dashboard=true
      - --providers.docker.exposedbydefault=false
      # Set up LetsEncrypt certificate resolver
      - --certificatesresolvers.letsencrypt.acme.dnschallenge=true
      - --certificatesresolvers.letsencrypt.acme.dnschallenge.provider=cloudflare
      - --certificatesResolvers.letsencrypt.acme.dnschallenge.resolvers=1.1.1.1:53,*******:53
      - --certificatesResolvers.letsencrypt.acme.dnschallenge.delayBeforeCheck=20
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      # staging environment of LE, remove for real certs
      #- --certificatesresolvers.letsencrypt.acme.caserver=https://acme-staging-v02.api.letsencrypt.org/directory

      # Set up an insecure listener that redirects all traffic to TLS
      - --entrypoints.http.address=:80
      - --entrypoints.http.http.redirections.entrypoint.to=https
      - --entrypoints.http.http.redirections.entrypoint.scheme=https
      - --entryPoints.https.address=:443

      # Set up the TLS configuration for our https listener
      - --entrypoints.https.http.tls=true
      - --entrypoints.https.http.tls.certResolver=letsencrypt
      - --entrypoints.https.http.tls.domains[0].main=coloraria.com
      - --entrypoints.https.http.tls.domains[0].sans=*.coloraria.com

      # Enable access logs
      # - "--accesslog=true"
      # - "--accesslog.filePath=/logs/access.log"
      # - "--metrics.otlp=true"
      # - "--tracing.otlp=true"

    security_opt:
      - no-new-privileges:true
    networks:
      - traefik-net
    ports:
      - 80:80
      - 443:443
    secrets:
      - "cloudflare-token"
      - "cloudflare-email"
    environment:
      CF_DNS_API_TOKEN_FILE: /run/secrets/cloudflare-token
      CF_API_EMAIL_FILE: /run/secrets/cloudflare-email
    env_file: .env.production
    volumes:
      - ./letsencrypt:/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock:ro
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik-secure.rule=Host(`traefik.localhost`)
      - traefik.http.routers.traefik-secure.entrypoints=https
      - traefik.http.routers.traefik-secure.service=api@internal
      - traefik.http.routers.traefik-secure.middlewares=traefik-auth
      - "traefik.http.middlewares.traefik-auth.basicauth.users=${TRAEFIK_DASHBOARD_AUTH}"

  # otel-collector:
  #   image: otel/opentelemetry-collector-contrib:latest
  #   ports:
  #     # - "4317:4317" # OTLP gRPC
  #     - "4318:4318" # OTLP HTTP
  #   volumes:
  #     - "./otel-collector-config.yaml:/etc/otel-collector-config.yaml"
  #   networks:
  #     - traefik-net

  web-app:
    # user: "1001:1001"
    image: node-app:v0.0.1
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - data:/app/generated-images

    env_file:
      - .env.production
    labels:
      - traefik.enable=true
      - traefik.http.routers.web-app.rule=Host(`www.coloraria.com`)
      - traefik.http.routers.web-app.entrypoints=https
      - traefik.http.routers.web-app.tls=true
      - traefik.http.routers.web-app.tls.certresolver=letsencrypt
      - traefik.http.services.web-app.loadbalancer.server.port=80
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true

volumes:
  data:
