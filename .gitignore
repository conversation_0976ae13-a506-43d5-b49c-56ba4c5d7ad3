# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
.pnpm-store
/.pnp
.pnp.js
.yarn/install-state.gz

/.idea/*
!/.idea/runConfigurations

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/traefik
/letsencrypt

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.env

/media

# python
.venv
model_deployment/__pycache__

.obsidian

ignored/
