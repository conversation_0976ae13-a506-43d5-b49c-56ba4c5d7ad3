Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "default" instance using docker driver

#1 [web-app internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.19kB done
#1 DONE 0.0s

#2 [web-app internal] load metadata for docker.io/library/node:22-alpine
#2 ...

#3 [web-app auth] library/node:pull token for registry-1.docker.io
#3 DONE 0.0s

#2 [web-app internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 2.0s

#4 [web-app internal] load .dockerignore
#4 transferring context: 2B done
#4 DONE 0.0s

#5 [web-app base 1/1] FROM docker.io/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59
#5 resolve docker.io/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59 0.0s done
#5 DONE 0.0s

#6 [web-app internal] load build context
#6 transferring context: 35.68kB 1.5s done
#6 DONE 1.5s

#7 [web-app builder  4/11] COPY assets assets
#7 CACHED

#8 [web-app builder  6/11] COPY package.json package.json
#8 CACHED

#9 [web-app deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#9 CACHED

#10 [web-app deps 1/4] RUN apk add --no-cache libc6-compat
#10 CACHED

#11 [web-app builder  3/11] COPY public public
#11 CACHED

#12 [web-app builder  2/11] COPY --from=deps /app/node_modules ./node_modules
#12 CACHED

#13 [web-app builder  8/11] COPY next.config.mjs next.config.mjs
#13 CACHED

#14 [web-app builder  1/11] WORKDIR /app
#14 CACHED

#15 [web-app deps 2/4] WORKDIR /app
#15 CACHED

#16 [web-app builder  9/11] COPY postcss.config.js postcss.config.js
#16 CACHED

#17 [web-app builder  5/11] COPY src src
#17 CACHED

#18 [web-app builder  7/11] COPY tsconfig.json tsconfig.json
#18 CACHED

#19 [web-app deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#19 CACHED

#20 [web-app builder 10/11] COPY pnpm-lock.yaml pnpm-lock.yaml
#20 CACHED

#21 [web-app builder 11/11] RUN   if [ -f yarn.lock ]; then yarn run build;   elif [ -f package-lock.json ]; then npm run build;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build;   else echo "Lockfile not found." && exit 1;   fi
#21 0.571 ! Corepack is about to download https://registry.npmjs.org/pnpm/-/pnpm-9.14.3.tgz
#21 1.757 
#21 1.757 > line-art-generator-v2@1.0.0 build /app
#21 1.757 > cross-env NODE_OPTIONS=--no-deprecation next build
#21 1.757 
#21 2.578    ▲ Next.js 15.2.3
#21 2.578 
#21 2.605    Creating an optimized production build ...
#21 89.54  ✓ Compiled successfully
#21 89.55    Linting and checking validity of types ...
#21 104.7    Collecting page data ...
#21 111.3    Generating static pages (0/46) ...
#21 112.3    Generating static pages (11/46) 
#21 112.3    Generating static pages (22/46) 
#21 112.4    Generating static pages (34/46) 
#21 112.5 null
#21 112.5 is user logged in? false
#21 112.7 null
#21 112.7 is user logged in? false
#21 112.7 null
#21 112.7 is user logged in? false
#21 112.9 null
#21 112.9 is user logged in? false
#21 116.4 [11:17:42] [31mERROR[39m: [36mError: cannot connect to Postgres. Details: getaddrinfo ENOTFOUND postgres[39m
#21 116.4     err: {
#21 116.4       "type": "Error",
#21 116.4       "message": "getaddrinfo ENOTFOUND postgres",
#21 116.4       "stack":
#21 116.4           Error: getaddrinfo ENOTFOUND postgres
#21 116.4               at /app/.next/server/chunks/516.js:1:26867
#21 116.4               at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
#21 116.4               at async lb (/app/.next/server/chunks/516.js:224:19543)
#21 116.4               at async Object.lw [as connect] (/app/.next/server/chunks/516.js:224:19874)
#21 116.4               at async tK.init (/app/.next/server/chunks/516.js:256:17269)
#21 116.4       "errno": -3008,
#21 116.4       "code": "ENOTFOUND",
#21 116.4       "syscall": "getaddrinfo",
#21 116.4       "hostname": "postgres"
#21 116.4     }
#21 116.5  ⨯ Next.js build worker exited with code: 1 and signal: null
#21 116.6  ELIFECYCLE  Command failed with exit code 1.
#21 ERROR: process "/bin/sh -c if [ -f yarn.lock ]; then yarn run build;   elif [ -f package-lock.json ]; then npm run build;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build;   else echo \"Lockfile not found.\" && exit 1;   fi" did not complete successfully: exit code: 1
------
 > [web-app builder 11/11] RUN   if [ -f yarn.lock ]; then yarn run build;   elif [ -f package-lock.json ]; then npm run build;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build;   else echo "Lockfile not found." && exit 1;   fi:
116.4               at async lb (/app/.next/server/chunks/516.js:224:19543)
116.4               at async Object.lw [as connect] (/app/.next/server/chunks/516.js:224:19874)
116.4               at async tK.init (/app/.next/server/chunks/516.js:256:17269)
116.4       "errno": -3008,
116.4       "code": "ENOTFOUND",
116.4       "syscall": "getaddrinfo",
116.4       "hostname": "postgres"
116.4     }
116.5  ⨯ Next.js build worker exited with code: 1 and signal: null
116.6  ELIFECYCLE  Command failed with exit code 1.
------
