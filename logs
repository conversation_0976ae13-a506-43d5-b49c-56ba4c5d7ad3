Compose can now delegate builds to bake for better performance.
 To do so, set COMPOSE_BAKE=true.
#0 building with "default" instance using docker driver

#1 [web-app internal] load build definition from Dockerfile
#1 transferring dockerfile: 3.18kB done
#1 DONE 0.0s

#2 [web-app internal] load metadata for docker.io/library/node:22-alpine
#2 DONE 0.9s

#3 [web-app internal] load .dockerignore
#3 transferring context: 2B done
#3 DONE 0.0s

#4 [web-app base 1/1] FROM docker.io/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59
#4 resolve docker.io/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59 0.0s done
#4 DONE 0.0s

#5 [web-app internal] load build context
#5 transferring context: 35.68kB 0.5s done
#5 DONE 0.6s

#6 [web-app builder  8/12] COPY next.config.mjs next.config.mjs
#6 CACHED

#7 [web-app builder  6/12] COPY package.json package.json
#7 CACHED

#8 [web-app deps 2/4] WORKDIR /app
#8 CACHED

#9 [web-app deps 1/4] RUN apk add --no-cache libc6-compat
#9 CACHED

#10 [web-app deps 4/4] RUN   if [ -f yarn.lock ]; then yarn --frozen-lockfile;   elif [ -f package-lock.json ]; then npm ci;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm i --frozen-lockfile;   else echo "Lockfile not found." && exit 1;   fi
#10 CACHED

#11 [web-app builder  5/12] COPY src src
#11 CACHED

#12 [web-app builder 10/12] COPY pnpm-lock.yaml pnpm-lock.yaml
#12 CACHED

#13 [web-app builder  1/12] WORKDIR /app
#13 CACHED

#14 [web-app builder  3/12] COPY public public
#14 CACHED

#15 [web-app builder  7/12] COPY tsconfig.json tsconfig.json
#15 CACHED

#16 [web-app builder  2/12] COPY --from=deps /app/node_modules ./node_modules
#16 CACHED

#17 [web-app deps 3/4] COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
#17 CACHED

#18 [web-app builder  4/12] COPY assets assets
#18 CACHED

#19 [web-app builder  9/12] COPY postcss.config.js postcss.config.js
#19 CACHED

#20 [web-app builder 11/12] RUN echo *******************************************/line-art-generator
#20 CACHED

#21 [web-app builder 12/12] RUN   if [ -f yarn.lock ]; then yarn run build;   elif [ -f package-lock.json ]; then npm run build;   elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm run build;   else echo "Lockfile not found." && exit 1;   fi
#21 0.174 ! Corepack is about to download https://registry.npmjs.org/pnpm/-/pnpm-9.14.3.tgz
#21 1.257 
#21 1.257 > line-art-generator-v2@1.0.0 build /app
#21 1.257 > cross-env NODE_OPTIONS=--no-deprecation next build
#21 1.257 
#21 1.971    ▲ Next.js 15.2.3
#21 1.972 
#21 1.994    Creating an optimized production build ...
