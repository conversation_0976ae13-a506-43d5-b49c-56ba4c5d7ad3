# Line Art Generator App

# Currently looking at approach 2 for deployment

There are currently some issues with running approach 2.

### Running the app:

On Windows:
```bash
start.bat
```

```bash
docker compose up -d --build && docker image prune -f
```

Make sure to install pnpm before running the app.
Refer: https://pnpm.io/
Then run docker for postgres.

We can also take a look at running coolify locally to manage the hetzner server.

### Backing up DB in docker

https://stackoverflow.com/questions/24718706/backup-restore-a-dockerized-postgresql-database

### Setup

Some more cool inspiration
https://www.beehiiv.com/i-want-to/influence-public-opinion?utm_medium=footer&utm_source=Designer%27s%20Coffee%20Newsletter

Cool CSS HIGHLIGHT:
https://www.coding-dude.com/wp/css/highlight-text-css/

Illustrations
https://undraw.co/illustrations

Grass Network Design:
https://www.getgrass.io/

General Design:
https://medium.com/@sepidy/how-can-i-design-in-the-neo-brutalism-style-d85c458042de

Color palette:
https://cdn.dribbble.com/users/747449/screenshots/21739202/downloads/%F0%9F%8C%88Snowball-Colors.png

We'll be using a combination of tailwind and the following component library:
https://www.neobrutalism.dev/react/components/toggle-switch

A neobrutalism look

Combine some color combination ideas as well:
https://github.com/marieooq/neo-brutalism-ui-library
https://github.com/marieooq/neo-brutalism-ui-library/blob/main/tailwind.config.js

LLM:
https://getimg.ai/tools/api#pricing
https://docs.getimg.ai/reference/poststablediffusioncontrolnet
https://docs.getimg.ai/reference/poststablediffusioncontrolnet

Background Remover:
https://www.npmjs.com/package/@imgly/background-removal

Reference:
https://kristi.digital/?utm_source=dribbble&utm_medium=kristi-website-case-study&utm_campaign=2024

Making line art:
https://www.reddit.com/r/StableDiffusion/comments/11m80bj/how_to_turn_any_image_into_lineart_with_img2img/#lightbox

More design stuff:
https://www.snowball.xyz/

## Creating Migrations

Step 1: Run the create_migration.bat / bash to create a properly formatted file
Note that for migration sql, it cannot be multi-transactional. Need to split into multiple functions.

```ts
Eg: const { rows: posts } = await db.execute(sql`SELECT * from posts`)
const { rows: users } = await db.execute(sql`SELECT * from user`)
```

Refer here for more information: https://payloadcms.com/docs/database/migrations
Underlying mechanism is DribbleORM. Can refer there for additional documentation if needed.

### Sitemap implementation
Refer to: https://www.npmjs.com/package/next-sitemap#generating-dynamicserver-side-sitemaps
and: https://github.com/payloadcms/payload/discussions/4920#discussioncomment-9097440
to ensure the sitemap is dynamic.

### MVS

- Conversion from image to line art (should be step-based)
  - Conversion Page
  - Generation Page
- Allow a style guide (Photorealistic, cartoon, fantasy, anime etc)
- Blogging system: For Google content marketing purposes
  - Related Articles sidebar
- Google OAUTH
- Payment Structure (Subscriptions)
  - Stripe
- Image Search Library (Available on higher subscription tier: Unsplash)
  - Directly send to conversion page
  - Whatever requirements unsplash needs
- User Profile Page
- Credit-based System (Cheap model conversion is 1 credit, anything above will cost more)

### Dashboard style:

https://dribbble.com/shots/21714992-Snowball-Customer-Dashboard

Some more inspiration
https://meui.dev/core-concepts/colors

### Subsequent - addition to higher tiers (by order of importance)

- Background Removal Service (Available on ALL subscription tier - free vs paid) - cost 1 more credit
- Upscaler - cost 1 more credit
- Inpainting + Outpainting (Available on higher subscriptions ONLY) [Regeneration counts as additional image generation]
- Fingerprinting (Priority once all features are completed)
- E-Commerce: Sell non-AI generated coloring art
- Using better models (Only if the app is doing well)
- Print-on-Demand (only if the app is doing well)

### Current prompt and settings (on getimg.ai)

#### For standard lineart (generation)

black and white image, coloring book style, thick black outline.
Guidance scale: 12
Step 30

#### For standard lineart (conversion using controlnet)

PROMPT: black and white image, coloring book style.
Negative prompt: shading, jpeg artifacts
image reference strength 200
step 30
guidance scale 12

### Payment Structure

Only three tiers

Trial: Generate/Convert 5 free images at fixed 512x512 (once fingerprinting is completed)
Basic: 250 credits + background removal service ($5.99)
Premium: Convert + Generate 300 images + ALL features ($14.99)
Master: Convert + Generate 700 images + ALL features ($29.99)

## Attributes

- **Database**: postgres
- **Storage Adapter**: localDisk
