Currently using strict browser + network fingerprint both needs to match before rejecting user's registration.

One weird caveat: User signing up either using Google or normal registration will have different network fingerprints. But so far from testing it appears to be consistent. Haven't extensively tested yet.

More investigation needed in the future to refine...

## More details to be added in the future...