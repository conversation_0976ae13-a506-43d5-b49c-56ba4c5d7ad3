# Payments Module

## Requirements

- Intgeration with Stripe
    - UI Components
        - Subscription Page / Buttons (triggers api call defined under apis)
        - Use Checkout Page provided by Stripe
    - APIs
        - Create Subscription (call Stripe API and update DB)
        - Create Checkout Session (call Stripe API and redirect to Stripe Checkout Page)
        - Create Webhook to handle Stripe events


## Database Schema

Our business user will be using PublicUsers table.

Option 1:
Keep a customer_id (stripe customer_id created before Checkout creation) in PublicUsers table.

Option 2:
Maintain user subscription on our side, use webhook event to update SubscriptionRecord table.


Stripe Objects

We want to manage payment using Stripe Subscriptions.
We will need to manage the following objects:

- Customers



### Customers
- `id`: Primary key
- `user_id`: Foreign key to your users table
- `stripe_customer_id`: Stripe's customer ID
- `email`: Customer email
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Subscriptions
- `id`: Primary key
- `user_id`: Foreign key to your users table
- `stripe_subscription_id`: Stripe's subscription ID
- `stripe_price_id`: Stripe's price ID
- `status`: Current status (active, canceled, past_due, etc.)
- `current_period_start`: Start of current billing period
- `current_period_end`: End of current billing period
- `cancel_at`: When subscription is scheduled to cancel (if applicable)
- `canceled_at`: When subscription was canceled (if applicable)
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Products
- `id`: Primary key
- `stripe_product_id`: Stripe's product ID
- `name`: Product name
- `description`: Product description
- `active`: Whether the product is active
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Prices
- `id`: Primary key
- `product_id`: Foreign key to products table
- `stripe_price_id`: Stripe's price ID
- `currency`: Currency code (USD, EUR, etc.)
- `amount`: Price amount in cents
- `interval`: Billing interval (month, year, etc.)
- `active`: Whether the price is active
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Payment Methods
- `id`: Primary key
- `user_id`: Foreign key to users table
- `stripe_payment_method_id`: Stripe's payment method ID
- `type`: Payment method type (card, bank_account, etc.)
- `last4`: Last 4 digits of card/account
- `exp_month`: Expiration month (for cards)
- `exp_year`: Expiration year (for cards)
- `is_default`: Whether this is the default payment method
- `created_at`: Timestamp
- `updated_at`: Timestamp

### Invoices
- `id`: Primary key
- `user_id`: Foreign key to users table
- `stripe_invoice_id`: Stripe's invoice ID
- `stripe_subscription_id`: Related subscription ID
- `amount_due`: Amount due in cents
- `amount_paid`: Amount paid in cents
- `currency`: Currency code
- `status`: Invoice status (paid, open, void, etc.)
- `invoice_pdf`: URL to PDF invoice
- `created_at`: Timestamp
- `updated_at`: Timestamp

## Flow

UI Component -> API call

### Subscription Flow

1. **User Selects Subscription Plan**
   - User navigates to subscription page
   - Views available plans and selects one
   - Clicks "Subscribe" button

2. **Create Customer (if new)**
   - Backend checks if user already has a Stripe customer ID
   - If not, creates a new customer in Stripe
   - Stores customer ID in database

3. **Create Checkout Session**
   - Backend creates a Stripe checkout session
   - Includes selected price ID, customer ID, success/cancel URLs
   - Returns checkout session URL to frontend

4. **Redirect to Stripe Checkout**
   - Frontend redirects user to Stripe-hosted checkout page
   - User enters payment information on Stripe's secure page

5. **Payment Processing**
   - Stripe processes payment
   - Redirects user to success URL with session ID

6. **Webhook Handling**
   - Stripe sends webhook events (checkout.session.completed)
   - Backend verifies webhook signature
   - Updates subscription status in database
   - Provisions access to paid features

7. **Subscription Management**
   - User can view subscription details
   - Cancel or update subscription
   - Backend syncs changes with Stripe

### Subscription Lifecycle Events

- **Creation**: checkout.session.completed → customer.subscription.created
- **Renewal**: invoice.payment_succeeded
- **Payment Failure**: invoice.payment_failed
- **Cancellation**: customer.subscription.deleted


## APIs





## Stripe APIs

here are the Stripe APIs that we need to call:






