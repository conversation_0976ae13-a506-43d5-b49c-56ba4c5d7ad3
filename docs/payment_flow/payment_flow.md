## Payment Flow

Sequence diagram for happy flow:
![payment-happy-path](payment_sequence_diagram_success.png)

During the session creation, we pass two additional objects to the metadata so we know which subscription plan we're internally referring to.

```ts
	metadata: {
        subscriptionRecordId,
        stripePriceId: subscriptionPlan.stripePriceId,
      },
```

First is subscription record id, we create an incomplete record at the start to signify user intent.
Next is the subscription plan itself, which currently we're identifying with the stripe price id.

### On completion:
We once again retrieve the incomplete subscription record and the subscription plan, which include:
- Monthly Credit Stipend

We then update the subscription record with the following:

```ts
await subscriptionRecordService.updateSubscription(
        subscriptionRecordId,
        stripeSubscriptionId,
        clientReferenceId, #User ID
        subscriptionPlan.id,
        customer ? customer : clientReferenceId, #Customer ID from stripe
        'active',
        startDate, #currentt_period_start from Stripe
        endDate, #current_period_end from Stripe
        '',
        cancelAt, #Should be null
        canceledAt, #Should be null
      )
```

### On Failure:
I realize it's an unlikely case as Stripe Checkout Element will catch all failures on its page. But just in case somehow the payment passes through Stripe Checkout Element, we can check with:

```ts
session.payment_status === 'unpaid'
```

From here, we will retrieve the latest invoice from stripe, and check the payment intent for the possible error with the code below:

```ts
	const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
        expand: ['latest_invoice.payments.data.payment_intent'],
      })

      if (subscription.latest_invoice && typeof subscription.latest_invoice !== 'string') {
        const invoice = subscription.latest_invoice

        if (invoice.payments && invoice.payments.data.length > 0) {
          const payment = invoice.payments.data[0]
          if (payment.payment && payment.payment.type === 'payment_intent') {
            const paymentIntent = payment.payment.payment_intent
            if (typeof paymentIntent !== 'string' && paymentIntent != undefined) {
              const paymentIntentStatus = paymentIntent.status
              const paymentIntentErrorDeclineCode = paymentIntent.last_payment_error?.decline_code
              const paymentIntentErrorDeclineMessage = paymentIntent.last_payment_error?.message
              const paymentIntentErrorDeclineType = paymentIntent.last_payment_error?.type
              const paymentIntentErrorDeclineDocUrl = paymentIntent.last_payment_error?.doc_url
              const resStr = `

                Status: ${paymentIntentStatus}

                Decline Code: ${paymentIntentErrorDeclineCode}

                Decline Message: ${paymentIntentErrorDeclineMessage}

                Decline Type: ${paymentIntentErrorDeclineType}

                Decline Doc Url: ${paymentIntentErrorDeclineDocUrl}

              `
              await subscriptionRecordService.updateSubscription(
                subscriptionRecordId,
                stripeSubscriptionId,
                clientReferenceId,
                subscriptionPlan.id,
                customer ? customer : clientReferenceId,
                'unpaid',
                startDate,
                endDate,
                resStr,
                cancelAt,
                canceledAt,
              )
            }
          }
        } else {
          console.log('No payments found on the latest invoice')
        }
      } else {
        console.log('Latest invoice not available or not expanded')
      }
```

We will then record all these decline codes, messages etc, on our payload, so when customers asks us, we have a reference to check.

Though technically, we can also check for this information on the Stripe Dashboard. But it's probably good reference for us as well, as we can reuse this logic when we're implementing the webhooks, as we won't be relying on Stripe Checkout Elements to do the checking for us.

### Subscription / Scopes / Features

Features are subset of scopes, and each subscription plan can only have one scope. You can assign credit cost to features so we can reference the cost on the server side for validation. But you can also set to 0, then it's just a permission check.

After creating subscription, you can use the SubscriptionButton component to trigger the payment flow. 

Make sure the subscription name is the exact match as the name you set on payloadcms:

```ts
<SubscriptionButton
                          subscriptionName="Hobby Plan"
                          subscriptionDuration="month"
                        />
```

