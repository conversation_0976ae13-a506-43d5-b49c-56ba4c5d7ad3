shape: sequence_diagram

User Agent
Pricing UI
User Service
Checkout Session API
Session Confirmation API
Subscription Plans Service
Stripe Checkout Element
Subscription Records Service
Success Page
Stripe API

User Agent -> Pricing UI: Goes to Pricing UI
Pricing UI -> User Service: Checks if user is logged in
User Service -> Pricing UI: Returns user auth
Pricing UI -> Checkout Session API: User click Subscribe
Checkout Session API -> Subscription Records Service: Creates an incomplete subscription record
Checkout Session API -> Subscription Plans Service: Retrieve user's selected plan
Subscription Plans Service -> Checkout Session API: Returns Subscription Plan Object
Checkout Session API -> Stripe API: Creates Checkout Session Object
Stripe API -> Checkout Session API: Returns Session Url
Susbcription Plans Service -> Checkout Session API: Returns Subscription Plan Pricing ID
Checkout Session API -> Stripe Checkout Element: Redirect user to Stripe Checkout Page
Stripe Checkout Element -> Session Confirmation API: Calls session confirmation API
Session Confirmation API -> Stripe API: Retrieve complete Checkout Session Object
Stripe API -> Session Confirmation API: Returns Stripe Checkout Session Object
Session Confirmation API -> Stripe API: Retrieve subscription object from Stripe
Stripe API -> Session Confirmation API: Returns subscription object from Stripe
Session Confirmation API -> Subscription Plans Service: Retrieve subscription Plan from pricing ID in checkout session object
Subscription Plans Service -> Session Confirmation API: Returns checkout session object
Session Confirmation API -> Subscription Records Service: Retrieve incomplete subscription record
Subscription Records Service -> Session Confirmation API: Return incomplete subscription record
Session Confirmation API -> Subscription Records Service: Submit completed subscription record
Session Confirmation API -> User Service: Update user's new subscription record and credits
Session Confirmation API -> Success Page: Redirect to Success Page is user succeeds