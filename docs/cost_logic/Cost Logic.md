
## Backend
### Backend Feature Cost:

Each Feature comes with a credit cost field. This credit cost can be of any value.

![feature_schema](feature_schema.PNG)

How you plan to use them is up to you.
- If using Features purely for permission purposes, you may set credit cost to 0
- The credit cost can be applied as either an addition or multiplier, the business logic is up to you.
## Frontend

### Lock Component

In the frontend, a generic 'LockComponent' is used. To apply permission lock, you can apply the LockComponent over the children component like so:

```tsx
<LockComponent
    feature={FeatureTypes.TEXT_TO_COLOR_IMAGE_REPETITION}
    userFeatures={userFeatures}
    chosenPlanFilter = {['PRELUDE_PLAN', 'MUSE_PLAN', 'ARIA_PLAN']},
    creditCost={imageRepetitionCreditCost}
    mode="disable"
    costMode="onMount"
    className="flex flex-row gap-4 w-full col-span-12 items-center"
    multiplier={imageRepetition * (highQuality ? highNativeQualityCreditCost : 1)}
>
    ...
</LockComponent>
```

There are a couple props you can utilize to lock a feature

- feature: This is compulsory, and should match the exact string as the backend.
- userFeatures: A list of permissible features for user. It is used to check if the user has access to this particular feature.
- chosenPlanFilter: Certain features are only available from a certain plan onwards, this allows you to only show the relevant plans so users won't be confused.
- creditCost: The credit cost is the amount that is tied to the feature.
- mode: 'disable' | 'hide' -> You can choose to either 'disable' the child components or hide them completely.
- costMode: 'onMount' | 'none' -> You can choose to either apply the cost the moment the component is mounted, or do nothing. This is to support either on component mount or interaction-only (such as using buttons / switch) mode.
- className: anything
- multiplier: The multiplier you are using to update the actual cost. In the example above, we're using a combination of features (high_native_quality) as well as the number of images users want to generate as the multiplier. This will multiply the base cost and return the overall amount.

For the above example, we won't be registering the highNativeQualityCreditCost to the store, as it's used as a multipler.

#### Rule of Thumb:
- Multiplier Cost: Apply directly to the base cost
- Addition Cost: Register to the store

### useCreditStore Global Context:

The LockComponent will tabulate the credits and features which will be updated here:

```ts
type CreditUsage = Record<string, number>

type CreditStore = {
  usage: CreditUsage
  register: (featureKey: string, cost: number) => void
  deregister: (featureKey: string) => void
  reset: () => void
  getTotal: () => number
  getCost: (key: string) => number
}
```

Type Explanation:
- usage keeps an object of KV pairs of (Feature - Cost)
- register: This method registers the feature and cost used
- deregister: Removes the feature and cost used
- reset: Removes all features
- getTotal: Aggregates the total cost from usage
- getCost: Get the cost of an already registered featureKey

To register or deregister, you can use the hooks specifically designed to help you reuse the function:

```ts
export function useCreditCharge(featureKey: string, cost: number) {
  const register = useCreditStore((s) => s.register)
  return () => register(featureKey, cost)
}

export function useCreditDeregisterCharge(featureKey: string) {
  const deregister = useCreditStore((s) => s.deregister)
  return () => deregister(featureKey)
}

//Define inside a client component, then call to register:
const nativeQualityCharge = useCreditCharge('nativeQuality', cost: 1)
nativeQualityCharge()
```

In general:
- Use getTotal to show the total cost
- Register 'additive' costing

### Frontend - Backend Interaction Pattern - Costing Logic:

There is no frontend-backend interaction for costing, and this is by design to ensure that our API cannot be influenced/abused in anyway.
Backend will have its own logic, and specify its own required features rather than receiving it from API from the frontend.

### Backend Cost Logic

You will need to define the appropriate scopes for the particular service.
All scopes are in FeatureType Enum. For Dev, you'll need to manually add these into the features.

Maybe we can have a seed to automatically initialize the features.

```ts
    const mandatoryFeatures = [FeaturesType.TEXT_TO_COLOR, FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION]
    const optionalFeatures = [FeaturesType.HIGH_NATIVE_QUALITY]
```

Once you have your features, you'll need to put them into the costTabulation function
from FeatureScopeService. Inside the costTabulaiton, the calculation of cost as well
as the checking of scopes will be done automatically.

If it passes, perform the credit deduction inside userService

```ts
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const applicableFeatures = highQuality
      ? [...mandatoryFeatures, ...optionalFeatures]
      : mandatoryFeatures

    const costTabulation = await featureScopeService.costTabulation(user.id, applicableFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits
```

### TODO?
Perform a credit refund if the service were to fail for any reason.
