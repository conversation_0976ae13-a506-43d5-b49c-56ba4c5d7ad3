<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-d2-version="v0.7.0" preserveAspectRatio="xMinYMin meet" viewBox="0 0 879 778"><svg class="d2-907817201 d2-svg" width="879" height="778" viewBox="-89 -49 879 778"><rect x="-89.000000" y="-49.000000" width="879.000000" height="778.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-907817201 .text {
	font-family: "d2-907817201-font-regular";
}
@font-face {
	font-family: d2-907817201-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.d2-907817201 .text-italic {
	font-family: "d2-907817201-font-italic";
}
@font-face {
	font-family: d2-907817201-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-907817201 .fill-N1{fill:#0A0F25;}
		.d2-907817201 .fill-N2{fill:#676C7E;}
		.d2-907817201 .fill-N3{fill:#9499AB;}
		.d2-907817201 .fill-N4{fill:#CFD2DD;}
		.d2-907817201 .fill-N5{fill:#DEE1EB;}
		.d2-907817201 .fill-N6{fill:#EEF1F8;}
		.d2-907817201 .fill-N7{fill:#FFFFFF;}
		.d2-907817201 .fill-B1{fill:#0D32B2;}
		.d2-907817201 .fill-B2{fill:#0D32B2;}
		.d2-907817201 .fill-B3{fill:#E3E9FD;}
		.d2-907817201 .fill-B4{fill:#E3E9FD;}
		.d2-907817201 .fill-B5{fill:#EDF0FD;}
		.d2-907817201 .fill-B6{fill:#F7F8FE;}
		.d2-907817201 .fill-AA2{fill:#4A6FF3;}
		.d2-907817201 .fill-AA4{fill:#EDF0FD;}
		.d2-907817201 .fill-AA5{fill:#F7F8FE;}
		.d2-907817201 .fill-AB4{fill:#EDF0FD;}
		.d2-907817201 .fill-AB5{fill:#F7F8FE;}
		.d2-907817201 .stroke-N1{stroke:#0A0F25;}
		.d2-907817201 .stroke-N2{stroke:#676C7E;}
		.d2-907817201 .stroke-N3{stroke:#9499AB;}
		.d2-907817201 .stroke-N4{stroke:#CFD2DD;}
		.d2-907817201 .stroke-N5{stroke:#DEE1EB;}
		.d2-907817201 .stroke-N6{stroke:#EEF1F8;}
		.d2-907817201 .stroke-N7{stroke:#FFFFFF;}
		.d2-907817201 .stroke-B1{stroke:#0D32B2;}
		.d2-907817201 .stroke-B2{stroke:#0D32B2;}
		.d2-907817201 .stroke-B3{stroke:#E3E9FD;}
		.d2-907817201 .stroke-B4{stroke:#E3E9FD;}
		.d2-907817201 .stroke-B5{stroke:#EDF0FD;}
		.d2-907817201 .stroke-B6{stroke:#F7F8FE;}
		.d2-907817201 .stroke-AA2{stroke:#4A6FF3;}
		.d2-907817201 .stroke-AA4{stroke:#EDF0FD;}
		.d2-907817201 .stroke-AA5{stroke:#F7F8FE;}
		.d2-907817201 .stroke-AB4{stroke:#EDF0FD;}
		.d2-907817201 .stroke-AB5{stroke:#F7F8FE;}
		.d2-907817201 .background-color-N1{background-color:#0A0F25;}
		.d2-907817201 .background-color-N2{background-color:#676C7E;}
		.d2-907817201 .background-color-N3{background-color:#9499AB;}
		.d2-907817201 .background-color-N4{background-color:#CFD2DD;}
		.d2-907817201 .background-color-N5{background-color:#DEE1EB;}
		.d2-907817201 .background-color-N6{background-color:#EEF1F8;}
		.d2-907817201 .background-color-N7{background-color:#FFFFFF;}
		.d2-907817201 .background-color-B1{background-color:#0D32B2;}
		.d2-907817201 .background-color-B2{background-color:#0D32B2;}
		.d2-907817201 .background-color-B3{background-color:#E3E9FD;}
		.d2-907817201 .background-color-B4{background-color:#E3E9FD;}
		.d2-907817201 .background-color-B5{background-color:#EDF0FD;}
		.d2-907817201 .background-color-B6{background-color:#F7F8FE;}
		.d2-907817201 .background-color-AA2{background-color:#4A6FF3;}
		.d2-907817201 .background-color-AA4{background-color:#EDF0FD;}
		.d2-907817201 .background-color-AA5{background-color:#F7F8FE;}
		.d2-907817201 .background-color-AB4{background-color:#EDF0FD;}
		.d2-907817201 .background-color-AB5{background-color:#F7F8FE;}
		.d2-907817201 .color-N1{color:#0A0F25;}
		.d2-907817201 .color-N2{color:#676C7E;}
		.d2-907817201 .color-N3{color:#9499AB;}
		.d2-907817201 .color-N4{color:#CFD2DD;}
		.d2-907817201 .color-N5{color:#DEE1EB;}
		.d2-907817201 .color-N6{color:#EEF1F8;}
		.d2-907817201 .color-N7{color:#FFFFFF;}
		.d2-907817201 .color-B1{color:#0D32B2;}
		.d2-907817201 .color-B2{color:#0D32B2;}
		.d2-907817201 .color-B3{color:#E3E9FD;}
		.d2-907817201 .color-B4{color:#E3E9FD;}
		.d2-907817201 .color-B5{color:#EDF0FD;}
		.d2-907817201 .color-B6{color:#F7F8FE;}
		.d2-907817201 .color-AA2{color:#4A6FF3;}
		.d2-907817201 .color-AA4{color:#EDF0FD;}
		.d2-907817201 .color-AA5{color:#F7F8FE;}
		.d2-907817201 .color-AB4{color:#EDF0FD;}
		.d2-907817201 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker-d2-907817201);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker-d2-907817201);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark-d2-907817201);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker-d2-907817201);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark-d2-907817201);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal-d2-907817201);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal-d2-907817201);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright-d2-907817201);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g class="VXNlciBBZ2VudA=="><g class="shape" ><rect x="12.000000" y="52.000000" width="118.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="71.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">User Agent</text></g><g class="QVBJ"><g class="shape" ><rect x="288.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="338.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">API</text></g><g class="REI="><g class="shape" ><rect x="439.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="489.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">DB</text></g><g class="UjI="><g class="shape" ><rect x="589.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="639.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">R2</text></g><g class="KFVzZXIgQWdlbnQgLS0gKVswXQ=="><path d="M 71.000000 120.000000 L 71.000000 627.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-907817201)" /></g><g class="KEFQSSAtLSApWzBd"><path d="M 338.000000 120.000000 L 338.000000 627.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-907817201)" /></g><g class="KERCIC0tIClbMF0="><path d="M 489.000000 120.000000 L 489.000000 627.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-907817201)" /></g><g class="KFIyIC0tIClbMF0="><path d="M 639.000000 120.000000 L 639.000000 627.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-907817201)" /></g><g class="KFVzZXIgQWdlbnQgLSZndDsgQVBJKVswXQ=="><marker id="mk-d2-907817201-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#0D32B2" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 73.000000 198.000000 L 334.000000 198.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-907817201-3488378134)" mask="url(#d2-907817201)" /><text x="205.000000" y="204.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Request for Image</text></g><g class="KEFQSSAtJmd0OyBEQilbMF0="><path d="M 340.000000 288.000000 L 485.000000 288.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-907817201-3488378134)" mask="url(#d2-907817201)" /><text x="413.500000" y="294.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Query Images</text></g><g class="KERCIC0mZ3Q7IEFQSSlbMF0="><path d="M 487.000000 378.000000 L 342.000000 378.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-907817201-3488378134)" mask="url(#d2-907817201)" /><text x="413.500000" y="384.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Image Entries</text></g><g class="KEFQSSAtJmd0OyBVc2VyIEFnZW50KVswXQ=="><path d="M 336.000000 468.000000 L 75.000000 468.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-907817201-3488378134)" mask="url(#d2-907817201)" /><text x="204.500000" y="474.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Return Images with retrieval url</text></g><g class="KFVzZXIgQWdlbnQgLSZndDsgUjIpWzBd"><path d="M 73.000000 558.000000 L 635.000000 558.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-907817201-3488378134)" mask="url(#d2-907817201)" /><text x="355.500000" y="564.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Retrieve Image</text></g><mask id="d2-907817201" maskUnits="userSpaceOnUse" x="-89" y="-49" width="879" height="778">
<rect x="-89" y="-49" width="879" height="778" fill="white"></rect>
<rect x="32.500000" y="74.500000" width="77" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="325.500000" y="74.500000" width="25" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="478.000000" y="74.500000" width="22" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="628.500000" y="74.500000" width="21" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="143.000000" y="188.000000" width="124" height="21" fill="black"></rect>
<rect x="366.000000" y="278.000000" width="95" height="21" fill="black"></rect>
<rect x="366.000000" y="368.000000" width="95" height="21" fill="black"></rect>
<rect x="99.000000" y="458.000000" width="211" height="21" fill="black"></rect>
<rect x="304.000000" y="548.000000" width="103" height="21" fill="black"></rect>
</mask></svg></svg>
