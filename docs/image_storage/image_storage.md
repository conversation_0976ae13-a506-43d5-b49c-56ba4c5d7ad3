## Overview

### Store Sequence Diagram

![store_seq.d2](store_seq.svg)



### Retrieve Sequence Diagram

![retrieve_seq.d2](retrieve_seq.svg)



## Frontend

Considering ImageState and Pagination


**Initial option**: Remove ImageState (previously used as persistent store) + server-side pagination (for lazy loading)

Current client-side pagination already allows lazy loading.  
Server-side pagination only helps to defer url generation.  

Some testing on retrieving same page (of images) within a session.

![alt text](image_cached_firefox.png)

On Firefox, images are cached on broswer.  

![alt text](image_cached_chrome.png)

On Chrome, two possible flows are observed:
1. calling url and got 304 response (HTTP cached, small data transfer)
2. browser cached the image (no network call)  

On browser refresh, images will be fetched again with normal flow.  


On removing ImageState, try remove all usage of this component.  

Observed in DialogImageGrid (which is used in ColoringCanvas and ImageEditor), 
isLoading state is taken from ImageState. 
images are directly taken from ImageState with useHydratedImageState. (VS ImageGrid where images are passed as props)



**Alt Option**: Use ImageState as a cache layer instead.

- allow cached across refreshes


change to (Dialog)ImageGrid component such that images are requested from ImageState (determine loading state from images requested?)  
this loading state basically means there's image in generating state?

if ImageState only caches paginated result, then loading state need to handle separately

