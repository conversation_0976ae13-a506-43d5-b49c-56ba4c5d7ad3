<?xml version="1.0" encoding="utf-8"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-d2-version="v0.7.0" preserveAspectRatio="xMinYMin meet" viewBox="0 0 1322 1138"><svg class="d2-2849217579 d2-svg" width="1322" height="1138" viewBox="-89 -49 1322 1138"><rect x="-89.000000" y="-49.000000" width="1322.000000" height="1138.000000" rx="0.000000" fill="#FFFFFF" class=" fill-N7" stroke-width="0" /><style type="text/css"><![CDATA[
.d2-2849217579 .text {
	font-family: "d2-2849217579-font-regular";
}
@font-face {
	font-family: d2-2849217579-font-regular;
	src: url("data:application/font-woff;base64,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");
}
.d2-2849217579 .text-italic {
	font-family: "d2-2849217579-font-italic";
}
@font-face {
	font-family: d2-2849217579-font-italic;
	src: url("data:application/font-woff;base64,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");
}]]></style><style type="text/css"><![CDATA[.shape {
  shape-rendering: geometricPrecision;
  stroke-linejoin: round;
}
.connection {
  stroke-linecap: round;
  stroke-linejoin: round;
}
.blend {
  mix-blend-mode: multiply;
  opacity: 0.5;
}

		.d2-2849217579 .fill-N1{fill:#0A0F25;}
		.d2-2849217579 .fill-N2{fill:#676C7E;}
		.d2-2849217579 .fill-N3{fill:#9499AB;}
		.d2-2849217579 .fill-N4{fill:#CFD2DD;}
		.d2-2849217579 .fill-N5{fill:#DEE1EB;}
		.d2-2849217579 .fill-N6{fill:#EEF1F8;}
		.d2-2849217579 .fill-N7{fill:#FFFFFF;}
		.d2-2849217579 .fill-B1{fill:#0D32B2;}
		.d2-2849217579 .fill-B2{fill:#0D32B2;}
		.d2-2849217579 .fill-B3{fill:#E3E9FD;}
		.d2-2849217579 .fill-B4{fill:#E3E9FD;}
		.d2-2849217579 .fill-B5{fill:#EDF0FD;}
		.d2-2849217579 .fill-B6{fill:#F7F8FE;}
		.d2-2849217579 .fill-AA2{fill:#4A6FF3;}
		.d2-2849217579 .fill-AA4{fill:#EDF0FD;}
		.d2-2849217579 .fill-AA5{fill:#F7F8FE;}
		.d2-2849217579 .fill-AB4{fill:#EDF0FD;}
		.d2-2849217579 .fill-AB5{fill:#F7F8FE;}
		.d2-2849217579 .stroke-N1{stroke:#0A0F25;}
		.d2-2849217579 .stroke-N2{stroke:#676C7E;}
		.d2-2849217579 .stroke-N3{stroke:#9499AB;}
		.d2-2849217579 .stroke-N4{stroke:#CFD2DD;}
		.d2-2849217579 .stroke-N5{stroke:#DEE1EB;}
		.d2-2849217579 .stroke-N6{stroke:#EEF1F8;}
		.d2-2849217579 .stroke-N7{stroke:#FFFFFF;}
		.d2-2849217579 .stroke-B1{stroke:#0D32B2;}
		.d2-2849217579 .stroke-B2{stroke:#0D32B2;}
		.d2-2849217579 .stroke-B3{stroke:#E3E9FD;}
		.d2-2849217579 .stroke-B4{stroke:#E3E9FD;}
		.d2-2849217579 .stroke-B5{stroke:#EDF0FD;}
		.d2-2849217579 .stroke-B6{stroke:#F7F8FE;}
		.d2-2849217579 .stroke-AA2{stroke:#4A6FF3;}
		.d2-2849217579 .stroke-AA4{stroke:#EDF0FD;}
		.d2-2849217579 .stroke-AA5{stroke:#F7F8FE;}
		.d2-2849217579 .stroke-AB4{stroke:#EDF0FD;}
		.d2-2849217579 .stroke-AB5{stroke:#F7F8FE;}
		.d2-2849217579 .background-color-N1{background-color:#0A0F25;}
		.d2-2849217579 .background-color-N2{background-color:#676C7E;}
		.d2-2849217579 .background-color-N3{background-color:#9499AB;}
		.d2-2849217579 .background-color-N4{background-color:#CFD2DD;}
		.d2-2849217579 .background-color-N5{background-color:#DEE1EB;}
		.d2-2849217579 .background-color-N6{background-color:#EEF1F8;}
		.d2-2849217579 .background-color-N7{background-color:#FFFFFF;}
		.d2-2849217579 .background-color-B1{background-color:#0D32B2;}
		.d2-2849217579 .background-color-B2{background-color:#0D32B2;}
		.d2-2849217579 .background-color-B3{background-color:#E3E9FD;}
		.d2-2849217579 .background-color-B4{background-color:#E3E9FD;}
		.d2-2849217579 .background-color-B5{background-color:#EDF0FD;}
		.d2-2849217579 .background-color-B6{background-color:#F7F8FE;}
		.d2-2849217579 .background-color-AA2{background-color:#4A6FF3;}
		.d2-2849217579 .background-color-AA4{background-color:#EDF0FD;}
		.d2-2849217579 .background-color-AA5{background-color:#F7F8FE;}
		.d2-2849217579 .background-color-AB4{background-color:#EDF0FD;}
		.d2-2849217579 .background-color-AB5{background-color:#F7F8FE;}
		.d2-2849217579 .color-N1{color:#0A0F25;}
		.d2-2849217579 .color-N2{color:#676C7E;}
		.d2-2849217579 .color-N3{color:#9499AB;}
		.d2-2849217579 .color-N4{color:#CFD2DD;}
		.d2-2849217579 .color-N5{color:#DEE1EB;}
		.d2-2849217579 .color-N6{color:#EEF1F8;}
		.d2-2849217579 .color-N7{color:#FFFFFF;}
		.d2-2849217579 .color-B1{color:#0D32B2;}
		.d2-2849217579 .color-B2{color:#0D32B2;}
		.d2-2849217579 .color-B3{color:#E3E9FD;}
		.d2-2849217579 .color-B4{color:#E3E9FD;}
		.d2-2849217579 .color-B5{color:#EDF0FD;}
		.d2-2849217579 .color-B6{color:#F7F8FE;}
		.d2-2849217579 .color-AA2{color:#4A6FF3;}
		.d2-2849217579 .color-AA4{color:#EDF0FD;}
		.d2-2849217579 .color-AA5{color:#F7F8FE;}
		.d2-2849217579 .color-AB4{color:#EDF0FD;}
		.d2-2849217579 .color-AB5{color:#F7F8FE;}.appendix text.text{fill:#0A0F25}.md{--color-fg-default:#0A0F25;--color-fg-muted:#676C7E;--color-fg-subtle:#9499AB;--color-canvas-default:#FFFFFF;--color-canvas-subtle:#EEF1F8;--color-border-default:#0D32B2;--color-border-muted:#0D32B2;--color-neutral-muted:#EEF1F8;--color-accent-fg:#0D32B2;--color-accent-emphasis:#0D32B2;--color-attention-subtle:#676C7E;--color-danger-fg:red;}.sketch-overlay-B1{fill:url(#streaks-darker-d2-2849217579);mix-blend-mode:lighten}.sketch-overlay-B2{fill:url(#streaks-darker-d2-2849217579);mix-blend-mode:lighten}.sketch-overlay-B3{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-B4{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-B5{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-B6{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-AA2{fill:url(#streaks-dark-d2-2849217579);mix-blend-mode:overlay}.sketch-overlay-AA4{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-AA5{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-AB4{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-AB5{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-N1{fill:url(#streaks-darker-d2-2849217579);mix-blend-mode:lighten}.sketch-overlay-N2{fill:url(#streaks-dark-d2-2849217579);mix-blend-mode:overlay}.sketch-overlay-N3{fill:url(#streaks-normal-d2-2849217579);mix-blend-mode:color-burn}.sketch-overlay-N4{fill:url(#streaks-normal-d2-2849217579);mix-blend-mode:color-burn}.sketch-overlay-N5{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-N6{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.sketch-overlay-N7{fill:url(#streaks-bright-d2-2849217579);mix-blend-mode:darken}.light-code{display: block}.dark-code{display: none}]]></style><g class="VXNlciBBZ2VudA=="><g class="shape" ><rect x="12.000000" y="52.000000" width="118.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="71.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">User Agent</text></g><g class="QVBJ"><g class="shape" ><rect x="256.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="306.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">API</text></g><g class="SW1hZ2UgR2VuZXJhdGlvbiBTZXJ2aWNl"><g class="shape" ><rect x="397.000000" y="52.000000" width="216.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="505.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">Image Generation Service</text></g><g class="TW9kYWw="><g class="shape" ><rect x="732.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="782.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">Modal</text></g><g class="REI="><g class="shape" ><rect x="882.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="932.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">DB</text></g><g class="UjI="><g class="shape" ><rect x="1032.000000" y="52.000000" width="100.000000" height="66.000000" stroke="#0D32B2" fill="#EDF0FD" class=" stroke-B1 fill-B5" style="stroke-width:2;" /></g><text x="1082.000000" y="90.500000" fill="#0A0F25" class="text fill-N1" style="text-anchor:middle;font-size:16px">R2</text></g><g class="KFVzZXIgQWdlbnQgLS0gKVswXQ=="><path d="M 71.000000 120.000000 L 71.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KEFQSSAtLSApWzBd"><path d="M 306.000000 120.000000 L 306.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KEltYWdlIEdlbmVyYXRpb24gU2VydmljZSAtLSApWzBd"><path d="M 505.000000 120.000000 L 505.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KE1vZGFsIC0tIClbMF0="><path d="M 782.000000 120.000000 L 782.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KERCIC0tIClbMF0="><path d="M 932.000000 120.000000 L 932.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KFIyIC0tIClbMF0="><path d="M 1082.000000 120.000000 L 1082.000000 987.000000" stroke="#0D32B2" fill="none" class="connection stroke-B2" style="stroke-width:2;stroke-dasharray:12.000000,11.838767;" mask="url(#d2-2849217579)" /></g><g class="KFVzZXIgQWdlbnQgLSZndDsgQVBJKVswXQ=="><marker id="mk-d2-2849217579-3488378134" markerWidth="10.000000" markerHeight="12.000000" refX="7.000000" refY="6.000000" viewBox="0.000000 0.000000 10.000000 12.000000" orient="auto" markerUnits="userSpaceOnUse"> <polygon points="0.000000,0.000000 10.000000,6.000000 0.000000,12.000000" fill="#0D32B2" class="connection fill-B1" stroke-width="2" /> </marker><path d="M 73.000000 198.000000 L 302.000000 198.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="188.500000" y="204.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Image Generation Request</text></g><g class="KEFQSSAtJmd0OyBJbWFnZSBHZW5lcmF0aW9uIFNlcnZpY2UpWzBd"><path d="M 308.000000 288.000000 L 501.000000 288.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="405.500000" y="294.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Handle Request</text></g><g class="KEltYWdlIEdlbmVyYXRpb24gU2VydmljZSAtJmd0OyBEQilbMF0="><path d="M 507.000000 378.000000 L 928.000000 378.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="719.000000" y="384.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Create Image Entries</text></g><g class="KEltYWdlIEdlbmVyYXRpb24gU2VydmljZSAtJmd0OyBNb2RhbClbMF0="><path d="M 507.000000 468.000000 L 778.000000 468.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="643.500000" y="474.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Generate Image (with upload url)</text></g><g class="KE1vZGFsIC0mZ3Q7IFIyKVswXQ=="><path d="M 784.000000 558.000000 L 1078.000000 558.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="932.500000" y="564.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Upload Image to R2</text></g><g class="KE1vZGFsIC0mZ3Q7IEltYWdlIEdlbmVyYXRpb24gU2VydmljZSlbMF0="><path d="M 780.000000 648.000000 L 509.000000 648.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="643.500000" y="654.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Return Image Metadata</text></g><g class="KEltYWdlIEdlbmVyYXRpb24gU2VydmljZSAtJmd0OyBEQilbMV0="><path d="M 507.000000 738.000000 L 928.000000 738.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="719.000000" y="744.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Update Image Entries</text></g><g class="KEltYWdlIEdlbmVyYXRpb24gU2VydmljZSAtJmd0OyBBUEkpWzBd"><path d="M 503.000000 828.000000 L 310.000000 828.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="405.500000" y="834.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Generation Response</text></g><g class="KEFQSSAtJmd0OyBVc2VyIEFnZW50KVswXQ=="><path d="M 304.000000 918.000000 L 75.000000 918.000000" stroke="#0D32B2" fill="none" class="connection stroke-B1" style="stroke-width:2;" marker-end="url(#mk-d2-2849217579-3488378134)" mask="url(#d2-2849217579)" /><text x="188.500000" y="924.000000" fill="#676C7E" class="text-italic fill-N2" style="text-anchor:middle;font-size:16px">Generation Response</text></g><mask id="d2-2849217579" maskUnits="userSpaceOnUse" x="-89" y="-49" width="1322" height="1138">
<rect x="-89" y="-49" width="1322" height="1138" fill="white"></rect>
<rect x="32.500000" y="74.500000" width="77" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="293.500000" y="74.500000" width="25" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="417.500000" y="74.500000" width="175" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="759.500000" y="74.500000" width="45" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="921.000000" y="74.500000" width="22" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="1071.500000" y="74.500000" width="21" height="21" fill="rgba(0,0,0,0.75)"></rect>
<rect x="99.000000" y="188.000000" width="179" height="21" fill="black"></rect>
<rect x="351.000000" y="278.000000" width="109" height="21" fill="black"></rect>
<rect x="648.000000" y="368.000000" width="142" height="21" fill="black"></rect>
<rect x="533.000000" y="458.000000" width="221" height="21" fill="black"></rect>
<rect x="866.000000" y="548.000000" width="133" height="21" fill="black"></rect>
<rect x="563.000000" y="638.000000" width="161" height="21" fill="black"></rect>
<rect x="646.000000" y="728.000000" width="146" height="21" fill="black"></rect>
<rect x="334.000000" y="818.000000" width="143" height="21" fill="black"></rect>
<rect x="117.000000" y="908.000000" width="143" height="21" fill="black"></rect>
</mask></svg></svg>
