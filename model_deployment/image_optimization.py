"""
Image optimization functions for various formats.
Provides PNG, JPEG, WebP, and GIF optimization capabilities.
"""

import io
from typing import Optional
from PIL import Image


def optimize_png_image(image_data: bytes, max_colors: int = 256, max_size: Optional[tuple[int, int]] = None, aggressive: bool = True) -> bytes:
    """
    Optimize PNG image by reducing colors, resizing, and applying compression.
    Based on techniques from: https://noerguerra.com/a-guide-to-efficient-image-compression-with-python/
    
    Args:
        image_data: The binary image data
        max_colors: Maximum number of colors to use (default: 256)
        max_size: Optional tuple (width, height) for maximum size
        aggressive: Whether to apply aggressive optimization (default: True)
        
    Returns:
        Optimized image data as bytes
    """
    try:
        # Open image from bytes
        img = Image.open(io.BytesIO(image_data))
        original_mode = img.mode
        original_size = len(image_data)
        
        # Get color count for analysis
        colors = img.getcolors()
        color_count = len(colors) if colors else "many"
        
        print(f"PNG optimization - Original: {img.size}, Mode: {original_mode}, Colors: {color_count}, Size: {original_size} bytes")
        
        # Resize if max_size is specified and image is larger
        resized = False
        if max_size and (img.width > max_size[0] or img.height > max_size[1]):
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            resized = True
            print(f"PNG resized to: {img.size}")
        
        # Try different optimization strategies and pick the best one
        optimization_results = []
        
        # Strategy 1: Basic optimization (just compress_level and optimize)
        basic_buffer = io.BytesIO()
        img_copy = img.copy()
        img_copy.save(basic_buffer, format='PNG', optimize=True, compress_level=9)
        basic_data = basic_buffer.getvalue()
        optimization_results.append(("basic", basic_data))
        
        # Strategy 2: Aggressive optimization (only if beneficial)
        if aggressive and (color_count == "many" or (isinstance(color_count, int) and color_count > max_colors)):
            # Convert RGBA to RGB with white background if it has transparency
            if img.mode == 'RGBA':
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[-1])
                img_aggressive = background
                print("PNG converted RGBA to RGB with white background")
            else:
                img_aggressive = img.copy()
            
            # Convert to RGB if not already
            if img_aggressive.mode not in ('RGB', 'P'):
                img_aggressive = img_aggressive.convert('RGB')
            
            # Apply quantization to reduce color palette
            if img_aggressive.mode != 'P':
                img_aggressive = img_aggressive.quantize(colors=max_colors, method=Image.Quantize.MEDIANCUT)
                actual_colors = len(img_aggressive.palette.colors) if hasattr(img_aggressive, 'palette') and img_aggressive.palette else 'unknown'
                print(f"PNG quantized to {max_colors} colors, actual colors: {actual_colors}")
            
            # Save aggressive optimization
            aggressive_buffer = io.BytesIO()
            img_aggressive.save(aggressive_buffer, format='PNG', optimize=True, compress_level=9)
            aggressive_data = aggressive_buffer.getvalue()
            optimization_results.append(("aggressive", aggressive_data))
        
        # Choose the best optimization (smallest file size)
        best_strategy, best_data = min(optimization_results, key=lambda x: len(x[1]))
        
        # Only use optimized version if it's actually smaller or if we resized
        if len(best_data) < original_size or resized:
            optimized_data = best_data
            strategy_used = best_strategy
        else:
            optimized_data = image_data
            strategy_used = "none (original was better)"
        
        optimized_size = len(optimized_data)
        reduction = ((original_size - optimized_size) / original_size * 100) if original_size > 0 else 0
        
        print(f"PNG optimization complete: {original_size} -> {optimized_size} bytes ({reduction:.1f}% reduction) using {strategy_used} strategy")
        
        return optimized_data
        
    except Exception as e:
        print(f"Error optimizing PNG image: {e}")
        # Return original data if optimization fails
        return image_data


def optimize_jpeg_image(image_data: bytes, quality: int = 85, max_size: Optional[tuple[int, int]] = None) -> bytes:
    """
    Optimize JPEG image by adjusting quality and optionally resizing.
    
    Args:
        image_data: The binary image data
        quality: JPEG quality (1-100, default: 85)
        max_size: Optional tuple (width, height) for maximum size
        
    Returns:
        Optimized image data as bytes
    """
    try:
        # Open image from bytes
        img = Image.open(io.BytesIO(image_data))
        original_size = len(image_data)
        
        print(f"JPEG optimization - Original: {img.size}, Mode: {img.mode}, Size: {original_size} bytes")
        
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
            print("JPEG converted to RGB mode")
        
        # Resize if max_size is specified and image is larger
        if max_size and (img.width > max_size[0] or img.height > max_size[1]):
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            print(f"JPEG resized to: {img.size}")
        
        # Save optimized image to bytes
        output_buffer = io.BytesIO()
        img.save(output_buffer, format='JPEG', quality=quality, optimize=True)
        
        optimized_data = output_buffer.getvalue()
        optimized_size = len(optimized_data)
        reduction = ((original_size - optimized_size) / original_size * 100) if original_size > 0 else 0
        
        print(f"JPEG optimization complete: {original_size} -> {optimized_size} bytes ({reduction:.1f}% reduction)")
        
        return optimized_data
        
    except Exception as e:
        print(f"Error optimizing JPEG image: {e}")
        # Return original data if optimization fails
        return image_data


def optimize_webp_image(image_data: bytes, quality: int = 80, max_size: Optional[tuple[int, int]] = None) -> bytes:
    """
    Optimize WebP image by adjusting quality and optionally resizing.
    
    Args:
        image_data: The binary image data
        quality: WebP quality (1-100, default: 80)
        max_size: Optional tuple (width, height) for maximum size
        
    Returns:
        Optimized image data as bytes
    """
    try:
        # Open image from bytes
        img = Image.open(io.BytesIO(image_data))
        original_size = len(image_data)
        
        print(f"WebP optimization - Original: {img.size}, Mode: {img.mode}, Size: {original_size} bytes")
        
        # Resize if max_size is specified and image is larger
        if max_size and (img.width > max_size[0] or img.height > max_size[1]):
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            print(f"WebP resized to: {img.size}")
        
        # Save optimized image to bytes
        output_buffer = io.BytesIO()
        img.save(output_buffer, format='WebP', quality=quality, optimize=True)
        
        optimized_data = output_buffer.getvalue()
        optimized_size = len(optimized_data)
        reduction = ((original_size - optimized_size) / original_size * 100) if original_size > 0 else 0
        
        print(f"WebP optimization complete: {original_size} -> {optimized_size} bytes ({reduction:.1f}% reduction)")
        
        return optimized_data
        
    except Exception as e:
        print(f"Error optimizing WebP image: {e}")
        # Return original data if optimization fails
        return image_data


def optimize_gif_image(image_data: bytes, max_size: Optional[tuple[int, int]] = None, max_colors: int = 256) -> bytes:
    """
    Optimize GIF image by reducing colors and optionally resizing.
    
    Args:
        image_data: The binary image data
        max_size: Optional tuple (width, height) for maximum size
        max_colors: Maximum number of colors (default: 256)
        
    Returns:
        Optimized image data as bytes
    """
    try:
        # Open image from bytes
        img = Image.open(io.BytesIO(image_data))
        original_size = len(image_data)
        
        print(f"GIF optimization - Original: {img.size}, Mode: {img.mode}, Size: {original_size} bytes")
        
        # Resize if max_size is specified and image is larger
        if max_size and (img.width > max_size[0] or img.height > max_size[1]):
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            print(f"GIF resized to: {img.size}")
        
        # For GIF, ensure we're in palette mode and optimize colors
        if img.mode != 'P':
            img = img.convert('RGB').quantize(colors=max_colors, method=Image.Quantize.MEDIANCUT)
            print(f"GIF quantized to {max_colors} colors")
        
        # Save optimized image to bytes
        output_buffer = io.BytesIO()
        img.save(output_buffer, format='GIF', optimize=True)
        
        optimized_data = output_buffer.getvalue()
        optimized_size = len(optimized_data)
        reduction = ((original_size - optimized_size) / original_size * 100) if original_size > 0 else 0
        
        print(f"GIF optimization complete: {original_size} -> {optimized_size} bytes ({reduction:.1f}% reduction)")
        
        return optimized_data
        
    except Exception as e:
        print(f"Error optimizing GIF image: {e}")
        # Return original data if optimization fails
        return image_data


def auto_optimize_image(image_data: bytes, max_size: Optional[tuple[int, int]] = None, png_aggressive: bool = True,
                       jpeg_quality: int = 85, webp_quality: int = 80) -> bytes:
    """
    Automatically optimize image based on format with specialized optimization for each type.
    
    Args:
        image_data: The binary image data
        max_size: Optional tuple (width, height) for maximum size
        png_aggressive: Whether to apply aggressive PNG optimization (default: True)
        jpeg_quality: JPEG quality level (1-100, default: 85)
        webp_quality: WebP quality level (1-100, default: 80)
        
    Returns:
        Optimized image data as bytes
    """
    try:
        # Detect image format
        img = Image.open(io.BytesIO(image_data))
        format_name = img.format
        
        print(f"Auto-optimizing {format_name} image: {img.size}, mode: {img.mode}")
        
        if format_name == 'PNG':
            # Apply specialized PNG optimization
            return optimize_png_image(
                image_data, 
                max_colors=256, 
                max_size=max_size, 
                aggressive=png_aggressive
            )
        elif format_name in ('JPEG', 'JPG'):
            # Apply specialized JPEG optimization
            return optimize_jpeg_image(
                image_data,
                quality=jpeg_quality,
                max_size=max_size
            )
        elif format_name == 'WEBP':
            # Apply specialized WebP optimization
            return optimize_webp_image(
                image_data,
                quality=webp_quality,
                max_size=max_size
            )
        elif format_name == 'GIF':
            # Apply specialized GIF optimization
            return optimize_gif_image(
                image_data,
                max_size=max_size,
                max_colors=256
            )
        else:
            # For other formats, apply basic optimization
            print(f"Applying basic optimization for {format_name} format")
            if max_size and (img.width > max_size[0] or img.height > max_size[1]):
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                output_buffer = io.BytesIO()
                img.save(output_buffer, format=format_name, optimize=True)
                return output_buffer.getvalue()
            else:
                # Just apply basic optimization without resizing
                output_buffer = io.BytesIO()
                img.save(output_buffer, format=format_name, optimize=True)
                return output_buffer.getvalue()
                
    except Exception as e:
        print(f"Error auto-optimizing image: {e}")
        # Return original data if optimization fails
        return image_data
