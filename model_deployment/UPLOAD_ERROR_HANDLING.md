# Upload Error Handling

This document explains the backward-compatible error handling system for image uploads to R2 storage.

## Overview

The upload functions now return `ImageMetadata` objects that include an optional `error` field. This approach maintains backward compatibility while providing detailed error information to clients.

## ImageMetadata Model

```python
class ImageMetadata(BaseModel):
    """Metadata for a generated image."""
    image_id: str
    width: int
    height: int
    size: int
    error: Optional[str] = None  # None for success, error message for failures
```

## Usage Examples

### Basic Error Checking

```python
from model_deployment.utils import upload_file_to_r2

# Upload a file
result = upload_file_to_r2(
    upload_url="https://r2.example.com/upload",
    file_path="image.png",
    image_id="img_123",
    width=512,
    height=512
)

# Check for errors
if result.error:
    print(f"Upload failed: {result.error}")
    # Handle error case
else:
    print(f"Upload successful: {result.image_id}")
    # Handle success case
```

### Batch Processing with <PERSON><PERSON><PERSON> Handling

```python
def process_batch_uploads(upload_data):
    """Process multiple uploads and handle partial failures."""
    successful_uploads = []
    failed_uploads = []
    
    for data in upload_data:
        result = upload_file_to_r2(
            upload_url=data['upload_url'],
            file_path=data['file_path'],
            image_id=data['image_id'],
            width=data['width'],
            height=data['height']
        )
        
        if result.error:
            failed_uploads.append({
                "image_id": result.image_id,
                "error": result.error
            })
        else:
            successful_uploads.append(result)
    
    return {
        "successful": successful_uploads,
        "failed": failed_uploads,
        "total": len(upload_data),
        "success_rate": len(successful_uploads) / len(upload_data)
    }
```

### API Response Generation

```python
from fastapi import HTTPException
from fastapi.responses import JSONResponse

def create_upload_response(results: List[ImageMetadata]):
    """Create API response from upload results."""
    successful = [r for r in results if not r.error]
    failed = [r for r in results if r.error]
    
    response_data = {
        "success": len(failed) == 0,
        "total_images": len(results),
        "successful_count": len(successful),
        "failed_count": len(failed),
        "images": [r.model_dump() for r in results]
    }
    
    if len(failed) == 0:
        # All uploads successful
        return JSONResponse(status_code=200, content=response_data)
    elif len(successful) > 0:
        # Partial success
        return JSONResponse(status_code=207, content=response_data)  # Multi-Status
    else:
        # Complete failure
        return JSONResponse(status_code=500, content=response_data)
```

### Error Type Detection

```python
def categorize_upload_error(error_message: str) -> str:
    """Categorize error types for better handling."""
    if "File not found" in error_message:
        return "FILE_NOT_FOUND"
    elif "timeout" in error_message.lower():
        return "TIMEOUT"
    elif "Connection error" in error_message:
        return "CONNECTION_ERROR"
    elif "HTTP" in error_message:
        return "HTTP_ERROR"
    else:
        return "UNKNOWN_ERROR"

# Usage
result = upload_file_to_r2(...)
if result.error:
    error_type = categorize_upload_error(result.error)
    if error_type == "TIMEOUT":
        # Retry logic
        pass
    elif error_type == "FILE_NOT_FOUND":
        # File validation logic
        pass
```

## Error Types

The system handles various error scenarios:

| Error Type | Example Message | Typical Cause |
|------------|----------------|---------------|
| HTTP Error | `HTTP 500: Upload failed` | Server-side issues |
| Timeout | `Upload timeout after 30 seconds` | Network or server slowness |
| Connection Error | `Connection error during upload` | Network connectivity issues |
| File Error | `File not found: /path/to/file.jpg` | Missing or inaccessible files |
| Read Error | `Error reading file: Permission denied` | File permission issues |
| Unexpected Error | `Unexpected error: ...` | Other unforeseen issues |

## Backward Compatibility

### Before (Exception-based)
```python
try:
    result = upload_file_to_r2(...)
    # Handle success
except UploadError as e:
    # Handle error
```

### After (Error field-based)
```python
result = upload_file_to_r2(...)
if result.error:
    # Handle error
else:
    # Handle success
```

## Migration Guide

### For Existing Code

1. **Replace exception handling** with error field checking:
   ```python
   # Old way
   try:
       result = upload_function(...)
       if result:  # Check for None
           process_success(result)
   except Exception:
       handle_error()
   
   # New way
   result = upload_function(...)
   if result.error:
       handle_error(result.error)
   else:
       process_success(result)
   ```

2. **Update response processing**:
   ```python
   # Old way
   if result is None:
       return {"error": "Upload failed"}
   
   # New way
   if result.error:
       return {"error": result.error}
   ```

### For New Code

Always check the `error` field:
```python
result = upload_file_to_r2(...)
if result.error:
    logger.error(f"Upload failed for {result.image_id}: {result.error}")
    return create_error_response(result.error)

logger.info(f"Upload successful for {result.image_id}")
return create_success_response(result)
```

## Benefits

1. **Backward Compatibility**: Existing code continues to work
2. **Detailed Error Information**: Error messages provide specific failure reasons
3. **Consistent Return Type**: Always returns `ImageMetadata`, simplifying type handling
4. **Easy Error Checking**: Simple `if result.error:` check
5. **JSON Serializable**: Can be directly serialized for API responses
6. **Batch Processing Friendly**: Easy to filter successful vs failed uploads

## Testing

```python
def test_upload_error_handling():
    # Test successful upload
    result = upload_file_to_r2(valid_params)
    assert result.error is None
    assert result.image_id == expected_id
    
    # Test failed upload
    result = upload_file_to_r2(invalid_params)
    assert result.error is not None
    assert "error description" in result.error
```

This approach provides robust error handling while maintaining simplicity and backward compatibility.
