"""
Example of how to use upload error handling in API endpoints.
Shows how to return proper error responses to clients.
"""

from fastapi import HTTPException
from fastapi.responses import JSONResponse
from typing import List, Union

from ..utils import upload_file_to_r2, is_upload_error, get_upload_error_response
from ..models import ImageMetadata, ImagesResponse


def example_single_image_endpoint(upload_url: str, file_path: str, image_id: str, 
                                width: int, height: int) -> Union[ImageMetadata, dict]:
    """
    Example endpoint that uploads a single image and returns proper error responses.
    
    Returns:
        ImageMetadata on success, error dict on failure
    """
    result = upload_file_to_r2(
        upload_url=upload_url,
        file_path=file_path,
        image_id=image_id,
        width=width,
        height=height
    )
    
    if is_upload_error(result):
        # Return error information for client
        return get_upload_error_response(result)
    
    # Return successful metadata
    return result


def example_batch_image_endpoint(upload_data: List[dict]) -> dict:
    """
    Example endpoint that uploads multiple images and handles partial failures.
    
    Args:
        upload_data: List of dicts with upload information
        
    Returns:
        Dict with successful uploads, failed uploads, and summary
    """
    successful_uploads = []
    failed_uploads = []
    
    for data in upload_data:
        result = upload_file_to_r2(
            upload_url=data['upload_url'],
            file_path=data['file_path'],
            image_id=data['image_id'],
            width=data['width'],
            height=data['height']
        )
        
        if is_upload_error(result):
            # Add error information to failed uploads
            error_info = get_upload_error_response(result)
            error_info['image_id'] = data['image_id']
            failed_uploads.append(error_info)
        else:
            # Add successful metadata
            successful_uploads.append(result)
    
    return {
        "success": len(failed_uploads) == 0,
        "total_images": len(upload_data),
        "successful_count": len(successful_uploads),
        "failed_count": len(failed_uploads),
        "successful_uploads": [img.dict() if hasattr(img, 'dict') else img.__dict__ for img in successful_uploads],
        "failed_uploads": failed_uploads
    }


def example_fastapi_endpoint_with_error_handling():
    """
    Example FastAPI endpoint with proper error handling and HTTP responses.
    """
    from fastapi import FastAPI, HTTPException, UploadFile, File, Form
    
    app = FastAPI()
    
    @app.post("/upload-image")
    async def upload_image(
        image: UploadFile = File(...),
        upload_url: str = Form(...),
        image_id: str = Form(...),
        width: int = Form(512),
        height: int = Form(512)
    ):
        """Upload a single image with comprehensive error handling."""
        
        # Save uploaded file temporarily
        import tempfile
        from pathlib import Path
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{image.filename}") as temp_file:
            content = await image.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            # Attempt upload
            result = upload_file_to_r2(
                upload_url=upload_url,
                file_path=temp_file_path,
                image_id=image_id,
                width=width,
                height=height
            )
            
            if is_upload_error(result):
                # Return appropriate HTTP error based on error type
                error_response = get_upload_error_response(result)
                
                if result.error_type == "FILE_NOT_FOUND":
                    raise HTTPException(status_code=400, detail=error_response)
                elif result.error_type == "TIMEOUT":
                    raise HTTPException(status_code=504, detail=error_response)
                elif result.error_type == "CONNECTION_ERROR":
                    raise HTTPException(status_code=502, detail=error_response)
                elif result.error_type == "HTTP_ERROR":
                    # Use the original HTTP status code if available
                    status_code = result.details.get("status_code", 500)
                    raise HTTPException(status_code=status_code, detail=error_response)
                else:
                    # Generic server error for unexpected errors
                    raise HTTPException(status_code=500, detail=error_response)
            
            # Return successful response
            return {
                "success": True,
                "message": "Image uploaded successfully",
                "image_metadata": {
                    "image_id": result.image_id,
                    "width": result.width,
                    "height": result.height,
                    "size": result.size
                }
            }
            
        finally:
            # Clean up temporary file
            try:
                Path(temp_file_path).unlink()
            except:
                pass  # Ignore cleanup errors
    
    @app.post("/upload-batch")
    async def upload_batch(upload_requests: List[dict]):
        """Upload multiple images with partial failure handling."""
        
        result = example_batch_image_endpoint(upload_requests)
        
        if result["success"]:
            return JSONResponse(
                status_code=200,
                content=result
            )
        elif result["successful_count"] > 0:
            # Partial success - some uploads worked
            return JSONResponse(
                status_code=207,  # Multi-Status
                content=result
            )
        else:
            # Complete failure - no uploads worked
            return JSONResponse(
                status_code=500,
                content=result
            )
    
    return app


# Error type mapping for HTTP status codes
ERROR_TYPE_TO_HTTP_STATUS = {
    "FILE_NOT_FOUND": 400,      # Bad Request
    "FILE_READ_ERROR": 400,     # Bad Request
    "TIMEOUT": 504,             # Gateway Timeout
    "CONNECTION_ERROR": 502,    # Bad Gateway
    "HTTP_ERROR": None,         # Use original status code
    "REQUEST_ERROR": 400,       # Bad Request
    "UNEXPECTED_ERROR": 500     # Internal Server Error
}


def get_http_status_for_error(upload_error) -> int:
    """
    Get appropriate HTTP status code for an upload error.
    
    Args:
        upload_error: UploadError instance
        
    Returns:
        HTTP status code
    """
    if not is_upload_error(upload_error):
        return 200
    
    error_type = upload_error.error_type
    
    if error_type == "HTTP_ERROR":
        # Use the original HTTP status code if available
        return upload_error.details.get("status_code", 500)
    
    return ERROR_TYPE_TO_HTTP_STATUS.get(error_type, 500)


def create_client_error_response(upload_error, include_details: bool = False) -> dict:
    """
    Create a client-friendly error response.
    
    Args:
        upload_error: UploadError instance
        include_details: Whether to include technical details (for debugging)
        
    Returns:
        Dict suitable for JSON response
    """
    if not is_upload_error(upload_error):
        return {"error": False, "message": "No error"}
    
    response = {
        "error": True,
        "error_type": upload_error.error_type,
        "message": upload_error.message,
        "timestamp": None  # Could add timestamp here
    }
    
    if include_details:
        response["details"] = upload_error.details
    
    # Add user-friendly messages
    user_messages = {
        "FILE_NOT_FOUND": "The specified file could not be found.",
        "FILE_READ_ERROR": "Unable to read the file. Please check file permissions.",
        "TIMEOUT": "Upload timed out. Please try again.",
        "CONNECTION_ERROR": "Connection error occurred. Please check your network.",
        "HTTP_ERROR": "Server error occurred during upload.",
        "REQUEST_ERROR": "Invalid request. Please check your parameters.",
        "UNEXPECTED_ERROR": "An unexpected error occurred. Please try again."
    }
    
    response["user_message"] = user_messages.get(upload_error.error_type, "An error occurred during upload.")
    
    return response


if __name__ == "__main__":
    # Example usage
    print("Upload Error Handling Examples")
    print("==============================")
    
    # This would be used in your actual API endpoints
    # See the functions above for implementation examples
    pass
