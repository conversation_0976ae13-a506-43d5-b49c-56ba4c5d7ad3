#!/usr/bin/env python3
"""
Multi-Format Image Optimizer Script

A command-line tool for optimizing images (PNG, JPEG, WebP, GIF) using techniques from:
https://noerguerra.com/a-guide-to-efficient-image-compression-with-python/

Usage:
    python png_optimizer.py <input_file> [output_file] [--colors N] [--max-size WxH] [--jpeg-quality N] [--webp-quality N]
    python png_optimizer.py --bulk <directory> [--colors N] [--max-size WxH] [--format FORMAT]

Examples:
    # Optimize single file (auto-detects format)
    python png_optimizer.py image.png optimized.png --colors 256 --max-size 1024x768
    python png_optimizer.py photo.jpg optimized.jpg --jpeg-quality 85 --max-size 1024x768

    # Optimize all images of specific format in directory
    python png_optimizer.py --bulk ./images --format png --colors 128
    python png_optimizer.py --bulk ./images --format jpeg --jpeg-quality 80
"""

import argparse
import sys
from pathlib import Path
from utils import optimize_png_file, bulk_optimize_png_directory, auto_optimize_image


def parse_size(size_str):
    """Parse size string like '1024x768' into tuple (1024, 768)."""
    if not size_str:
        return None
    try:
        width, height = size_str.lower().split('x')
        return (int(width), int(height))
    except ValueError:
        raise argparse.ArgumentTypeError(f"Size must be in format WIDTHxHEIGHT, got: {size_str}")


def optimize_single_file(input_path, output_path, max_colors, max_size):
    """Optimize a single PNG file."""
    input_path = Path(input_path)
    
    if not input_path.exists():
        print(f"Error: Input file '{input_path}' not found")
        return False
    
    if not input_path.suffix.lower() == '.png':
        print(f"Error: Input file must be a PNG file, got: {input_path.suffix}")
        return False
    
    # Use input filename with _optimized suffix if no output specified
    if output_path is None:
        output_path = input_path.with_name(f"{input_path.stem}_optimized{input_path.suffix}")
    else:
        output_path = Path(output_path)
    
    print(f"Optimizing: {input_path} -> {output_path}")
    print(f"Settings: max_colors={max_colors}, max_size={max_size}")
    
    success = optimize_png_file(
        input_path=input_path,
        output_path=output_path,
        max_colors=max_colors,
        max_size=max_size
    )
    
    if success:
        print(f"✅ Successfully optimized: {output_path}")
        return True
    else:
        print(f"❌ Failed to optimize: {input_path}")
        return False


def optimize_directory(directory_path, max_colors, max_size):
    """Optimize all PNG files in a directory."""
    directory_path = Path(directory_path)
    
    if not directory_path.exists():
        print(f"Error: Directory '{directory_path}' not found")
        return False
    
    if not directory_path.is_dir():
        print(f"Error: '{directory_path}' is not a directory")
        return False
    
    print(f"Bulk optimizing PNG files in: {directory_path}")
    print(f"Settings: max_colors={max_colors}, max_size={max_size}")
    
    optimized_count = bulk_optimize_png_directory(
        directory_path=directory_path,
        max_colors=max_colors,
        max_size=max_size,
        suffix="_optimized"
    )
    
    if optimized_count > 0:
        print(f"✅ Successfully optimized {optimized_count} PNG files")
        return True
    else:
        print(f"❌ No PNG files were optimized")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Optimize PNG images using advanced compression techniques",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Input/output arguments
    parser.add_argument('input', nargs='?', help='Input PNG file or directory (with --bulk)')
    parser.add_argument('output', nargs='?', help='Output PNG file (optional, defaults to input_optimized.png)')
    
    # Mode selection
    parser.add_argument('--bulk', action='store_true', help='Optimize all PNG files in directory')
    
    # Optimization parameters
    parser.add_argument('--colors', type=int, default=256, 
                       help='Maximum number of colors (default: 256)')
    parser.add_argument('--max-size', type=parse_size, 
                       help='Maximum size as WIDTHxHEIGHT (e.g., 1024x768)')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Validate arguments
    if not args.input:
        parser.print_help()
        sys.exit(1)
    
    if args.colors < 2 or args.colors > 256:
        print("Error: Colors must be between 2 and 256")
        sys.exit(1)
    
    # Execute optimization
    try:
        if args.bulk:
            if args.output:
                print("Warning: Output argument ignored in bulk mode")
            success = optimize_directory(args.input, args.colors, args.max_size)
        else:
            success = optimize_single_file(args.input, args.output, args.colors, args.max_size)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
