"""
Configuration module for Modal ComfyUI deployment.
Contains all constants, model definitions, and configuration settings.
"""

# Container Configuration
CONTAINER_CPU = 2.0
CONTAINER_MEMORY = 16384
CONTAINER_GPU = "A10G"
CONTAINER_TIMEOUT = 5 * 60  # 5 minutes
MAX_CONTAINERS = 1
SCALEDOWN_WINDOW = 2
MAX_CONCURRENT_INPUTS = 5

# API Configuration
API_TIMEOUT = 5  # seconds for health checks
MAX_SEED_VALUE = 2**32 - 1

# Python Dependencies
PYTHON_DEPENDENCIES = [
    "fastapi[standard]==0.115.4",
    "comfy-cli==1.3.8",
    "huggingface_hub[hf_transfer]==0.30.0",
]

# ComfyUI Node Packages
COMFYUI_NODES = [
    "comfyui-easy-use",
    "comfyui_controlnet_aux",
    "comfyui_ipadapter_plus",
    "comfyui-custom-scripts",
    "was-node-suite-comfyui",
]

# Environment Variables
ENV_VARS = {
    "HF_HUB_ENABLE_HF_TRANSFER": "1",
}

# Default Image Generation Parameters
DEFAULT_WIDTH = 512
DEFAULT_HEIGHT = 512
DEFAULT_BATCH_SIZE = 1

# Directory Paths
COMFYUI_BASE_DIR = "/root/comfy/ComfyUI"
COMFYUI_INPUT_DIR = f"{COMFYUI_BASE_DIR}/input"
COMFYUI_OUTPUT_DIR = f"{COMFYUI_BASE_DIR}/output"

# Prompt Templates
T2I_PROMPT_TEMPLATE = """
    Create a black and white coloring page based on the provided prompt below:
    {prompt}
"""