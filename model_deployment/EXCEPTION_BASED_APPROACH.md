# Exception-Based Upload Error Handling

This document explains the clean exception-based approach for handling upload errors while maintaining backward compatibility.

## Architecture

The system uses a two-layer approach:

1. **Internal functions** (`_upload_image_to_r2_internal`, etc.) that raise specific exceptions
2. **Public functions** (`upload_image_to_r2`, `upload_file_to_r2`) that catch exceptions and convert them to `ImageMetadata` with error fields

## Benefits

✅ **Clean Exception Handling**: Structured exception types for different error scenarios  
✅ **Backward Compatibility**: Always returns `ImageMetadata` objects  
✅ **No Duplicate Error Messages**: Exception messages are directly used as error strings  
✅ **Maintainable**: Single source of truth for error messages  
✅ **Testable**: Easy to test specific error scenarios  

## Implementation Pattern

### Internal Function (Raises Exceptions)
```python
def _upload_image_to_r2_internal(...) -> ImageMetadata:
    """Internal upload function that raises exceptions on failure."""
    try:
        # ... upload logic ...
        if response.status_code != 200:
            raise UploadError(f"HTTP {response.status_code}: Upload failed")
        return ImageMetadata(...)
    except requests.exceptions.Timeout:
        raise UploadError(f"Upload timeout after {timeout} seconds")
    except requests.exceptions.ConnectionError:
        raise UploadError("Connection error during upload")
    # ... other specific exceptions ...
```

### Public Function (Converts Exceptions)
```python
def upload_image_to_r2(...) -> ImageMetadata:
    """Public upload function with backward-compatible error handling."""
    try:
        return _upload_image_to_r2_internal(...)
    except Exception as e:
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            size=len(image_data),
            error=str(e)  # Exception message becomes error field
        )
```

## Exception Types

### UploadError
```python
class UploadError(Exception):
    """Exception raised when image upload fails."""
    pass
```

**Usage Examples:**
- `UploadError("HTTP 500: Upload failed")`
- `UploadError("Upload timeout after 30 seconds")`
- `UploadError("File not found: /path/to/file.jpg")`
- `UploadError("Connection error during upload")`

## Error Scenarios

| Scenario | Exception Message | ImageMetadata.error |
|----------|------------------|-------------------|
| HTTP Error | `"HTTP 500: Upload failed"` | `"HTTP 500: Upload failed"` |
| Timeout | `"Upload timeout after 30 seconds"` | `"Upload timeout after 30 seconds"` |
| Connection Error | `"Connection error during upload"` | `"Connection error during upload"` |
| File Not Found | `"File not found: /path/to/file.jpg"` | `"File not found: /path/to/file.jpg"` |
| File Read Error | `"Error reading file: Permission denied"` | `"Error reading file: Permission denied"` |

## Usage Examples

### Basic Upload with Error Checking
```python
result = upload_file_to_r2(
    upload_url="https://r2.example.com/upload",
    file_path="image.png",
    image_id="img_123",
    width=512,
    height=512
)

if result.error:
    print(f"Upload failed: {result.error}")
    # Handle specific error types
    if "timeout" in result.error.lower():
        # Retry logic
        pass
    elif "File not found" in result.error:
        # File validation logic
        pass
else:
    print(f"Upload successful: {result.image_id}")
```

### Advanced Error Handling
```python
def handle_upload_result(result: ImageMetadata) -> dict:
    """Convert upload result to API response."""
    if result.error:
        # Categorize error for client
        if "HTTP 4" in result.error:
            status_code = 400  # Client error
        elif "HTTP 5" in result.error:
            status_code = 500  # Server error
        elif "timeout" in result.error.lower():
            status_code = 504  # Gateway timeout
        elif "Connection error" in result.error:
            status_code = 502  # Bad gateway
        else:
            status_code = 500  # Generic error
            
        return {
            "success": False,
            "error": result.error,
            "status_code": status_code,
            "image_id": result.image_id
        }
    else:
        return {
            "success": True,
            "image_id": result.image_id,
            "width": result.width,
            "height": result.height,
            "size": result.size
        }
```

### Batch Processing
```python
def process_batch_uploads(files: List[dict]) -> dict:
    """Process multiple uploads with detailed error reporting."""
    results = []
    
    for file_info in files:
        result = upload_file_to_r2(**file_info)
        results.append(result)
    
    # Categorize results
    successful = [r for r in results if not r.error]
    failed = [r for r in results if r.error]
    
    # Group errors by type
    error_summary = {}
    for result in failed:
        error_type = categorize_error(result.error)
        if error_type not in error_summary:
            error_summary[error_type] = []
        error_summary[error_type].append(result.image_id)
    
    return {
        "total": len(results),
        "successful": len(successful),
        "failed": len(failed),
        "success_rate": len(successful) / len(results),
        "error_summary": error_summary,
        "results": [r.model_dump() for r in results]
    }

def categorize_error(error_message: str) -> str:
    """Categorize error message into type."""
    if "HTTP" in error_message:
        return "http_error"
    elif "timeout" in error_message.lower():
        return "timeout"
    elif "Connection error" in error_message:
        return "connection_error"
    elif "File not found" in error_message:
        return "file_not_found"
    else:
        return "unknown_error"
```

## Testing

### Testing Internal Functions (Exception-based)
```python
def test_internal_upload_exceptions():
    """Test that internal functions raise proper exceptions."""
    with pytest.raises(UploadError, match="HTTP 500"):
        _upload_image_to_r2_internal(...)  # Mock 500 response
    
    with pytest.raises(UploadError, match="timeout"):
        _upload_image_to_r2_internal(...)  # Mock timeout
```

### Testing Public Functions (Error Field)
```python
def test_public_upload_error_handling():
    """Test that public functions return ImageMetadata with errors."""
    result = upload_image_to_r2(...)  # Mock failure
    assert result.error is not None
    assert "HTTP 500" in result.error
    assert result.image_id == expected_id
```

## Migration from Previous Approach

### Before (Manual Error Messages)
```python
# Multiple places creating similar error messages
error_msg = f"HTTP {response.status_code}: Upload failed"
return ImageMetadata(..., error=error_msg)

error_msg = f"Upload timeout after {timeout} seconds"  
return ImageMetadata(..., error=error_msg)
```

### After (Exception-based)
```python
# Single place for each error type
raise UploadError(f"HTTP {response.status_code}: Upload failed")
raise UploadError(f"Upload timeout after {timeout} seconds")

# Automatic conversion to error field
except Exception as e:
    return ImageMetadata(..., error=str(e))
```

## Advantages

1. **DRY Principle**: No duplicate error message creation
2. **Consistency**: All error messages come from exception definitions
3. **Maintainability**: Change error message in one place
4. **Testability**: Easy to test specific exception scenarios
5. **Extensibility**: Easy to add new exception types
6. **Debugging**: Stack traces available during development
7. **Backward Compatibility**: Existing code continues to work

This approach provides the best of both worlds: clean exception-based error handling internally with backward-compatible responses externally.
