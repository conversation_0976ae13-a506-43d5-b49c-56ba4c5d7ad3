# Multi-Format Image Optimization

This module provides advanced image optimization for multiple formats, with specialized techniques for each format based on [A guide to efficient image compression with Python](https://noerguerra.com/a-guide-to-efficient-image-compression-with-python/).

## Supported Formats

- **PNG**: Color palette reduction, transparency handling, maximum compression
- **JPEG**: Quality optimization, RGB conversion, progressive encoding
- **WebP**: Modern compression with quality control
- **GIF**: Color quantization, palette optimization
- **Other formats**: Basic optimization with resize support

## Features

### PNG-Specific Optimizations

1. **Color Palette Reduction (Quantization)**
   - Reduces the number of colors in PNG images
   - Uses median cut algorithm for optimal color selection
   - Configurable color limits (2-256 colors)

2. **Smart Compression**
   - Maximum PNG compression level (9)
   - Automatic optimization flag
   - Preserves quality while reducing file size
   - Proper MIME type detection (`image/png` for PNG files)

3. **Intelligent Resizing**
   - Optional maximum size constraints
   - Maintains aspect ratio using high-quality Lanczos resampling
   - Only resizes when beneficial

4. **RGBA to RGB Conversion**
   - Converts transparent PNGs to RGB with white background
   - Reduces file size by eliminating alpha channel
   - Configurable for aggressive optimization

### JPEG-Specific Optimizations

1. **Quality Control**
   - Configurable quality levels (1-100)
   - Default quality of 85 for good balance
   - Progressive JPEG encoding

2. **Color Space Optimization**
   - Automatic RGB conversion
   - Removes unnecessary color channels

### WebP Optimizations

1. **Modern Compression**
   - Superior compression ratios
   - Quality control (1-100, default: 80)
   - Supports both lossy and lossless modes

### GIF Optimizations

1. **Color Palette Reduction**
   - Quantization to reduce colors
   - Maintains animation support
   - Optimized palette generation

## Usage

### In Code

```python
from model_deployment.utils import (
    optimize_png_image, optimize_jpeg_image, optimize_webp_image,
    auto_optimize_image, optimize_png_file
)

# Auto-optimize any image format
with open('image.jpg', 'rb') as f:
    original_data = f.read()

optimized_data = auto_optimize_image(
    image_data=original_data,
    max_size=(1024, 768),
    png_aggressive=True,
    jpeg_quality=85,
    webp_quality=80
)

# Format-specific optimization
png_data = optimize_png_image(
    image_data=png_original,
    max_colors=256,
    max_size=(1024, 768),
    aggressive=True
)

jpeg_data = optimize_jpeg_image(
    image_data=jpeg_original,
    quality=85,
    max_size=(1024, 768)
)

webp_data = optimize_webp_image(
    image_data=webp_original,
    quality=80,
    max_size=(1024, 768)
)
```

### Command Line Tool

```bash
# Optimize single file
python model_deployment/png_optimizer.py image.png optimized.png --colors 256 --max-size 1024x768

# Optimize all PNGs in directory
python model_deployment/png_optimizer.py --bulk ./images --colors 128
```

### Automatic R2 Upload Optimization

PNG optimization is automatically applied when uploading to R2:

```python
from model_deployment.utils import upload_file_to_r2

# PNG files are automatically optimized before upload
metadata = upload_file_to_r2(
    upload_url=presigned_url,
    file_path='large_image.png',
    image_id='img_123',
    width=1024,
    height=768,
    optimize=True  # Enables PNG optimization
)
```

## Optimization Strategies

### 1. Basic Optimization
- Applies PNG compression level 9
- Uses optimize flag
- Minimal quality loss

### 2. Aggressive Optimization
- Color palette reduction via quantization
- RGBA to RGB conversion
- Maximum compression
- Significant file size reduction

### 3. Smart Selection
- Compares multiple optimization strategies
- Selects the best result (smallest file size)
- Falls back to original if optimization increases size

## Performance Examples

Based on testing with various image formats:

| Format | Image Type | Original Size | Optimized Size | Reduction |
|--------|------------|---------------|----------------|-----------|
| PNG | Full-color gradient (600x400) | 7.7 KB | 4.8 KB | 37.1% |
| PNG | Large image (1600x1200→800x600) | 30.5 KB | 8.1 KB | 73.5% |
| JPEG | Full-color gradient (600x400) | 22.8 KB | 8.8 KB | 61.5% |
| JPEG | Large image (1600x1200→800x600) | 41.2 KB | 15.7 KB | 61.9% |
| WebP | Full-color gradient (600x400) | 6.6 KB | 2.5 KB | 61.5% |
| GIF | Palette image (600x400) | 37.7 KB | 37.7 KB | 0.0%* |

*GIF optimization depends on existing palette efficiency

## Configuration Options

### `max_colors` (int, default: 256)
- Maximum number of colors in the optimized image
- Lower values = smaller files, potentially lower quality
- Range: 2-256

### `max_size` (tuple, optional)
- Maximum dimensions as (width, height)
- Images larger than this will be resized
- Maintains aspect ratio

### `aggressive` (bool, default: True)
- Enables aggressive optimization techniques
- Includes RGBA→RGB conversion and quantization
- May reduce quality for significant size savings

## Integration Points

### R2 Upload Pipeline
PNG optimization is integrated into the R2 upload process:

1. **workflows.py**: Optimizes generated images before upload
2. **api_handlers.py**: Optimizes uploaded images before storage
3. **utils.py**: Core optimization functions

### Endpoints Using PNG Optimization
- `/t2i` - Text-to-image generation
- `/i2i` - Image-to-image processing  
- `/rmbg` - Background removal

## Testing

Run the PNG optimization tests:

```bash
# Run all PNG optimization tests
python -m model_deployment.tests.test_png_optimization

# Run R2 upload tests (includes PNG optimization)
python -m model_deployment.tests.test_utils
```

## Best Practices

1. **For Generated Images**: Use aggressive optimization with 256 colors
2. **For User Uploads**: Use moderate optimization to preserve quality
3. **For Large Images**: Always set max_size to reasonable dimensions
4. **For Batch Processing**: Use bulk optimization functions for efficiency

## Technical Details

The optimization process follows these steps:

1. **Analysis**: Detect image format, size, and color count
2. **Strategy Selection**: Choose optimization approach based on image characteristics
3. **Processing**: Apply resize, quantization, and compression
4. **Validation**: Compare results and select best optimization
5. **Fallback**: Return original if optimization doesn't improve file size
