{"1": {"inputs": {"ckpt_name": "SDXL/sd_xl_base_1.0.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "2": {"inputs": {"vae_name": "SDXL/sdxl_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "3": {"inputs": {"image": ["4", 0]}, "class_type": "CM_NearestSDXLResolution", "_meta": {"title": "NearestSDXLResolution"}}, "4": {"inputs": {"image": "photo_6204083605307769704_x.jpg"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "5": {"inputs": {"max_width": ["3", 0], "max_height": ["3", 1], "min_width": 0, "min_height": 0, "crop_if_required": "yes", "images": ["4", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "6": {"inputs": {"mode": "caption", "question": "What does the background consist of?", "min_length": 24, "max_length": 64, "num_beams": 5, "no_repeat_ngram_size": 3, "early_stopping": false, "images": ["4", 0], "blip_model": ["7", 0]}, "class_type": "BLIP Analyze Image", "_meta": {"title": "BLIP Analyze Image"}}, "7": {"inputs": {"blip_model": "Salesforce/blip-image-captioning-base", "vqa_model_id": "Salesforce/blip-vqa-base", "device": "cuda"}, "class_type": "BLIP Model Loader", "_meta": {"title": "BLIP Model Loader"}}, "9": {"inputs": {"text": "", "text_b": "black and white", "text_c": "colorless", "text_d": "lineart, linework."}, "class_type": "Text String", "_meta": {"title": "Text String"}}, "10": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["6", 0], "text_b": ["9", 1], "text_c": ["9", 2], "text_d": ["9", 3]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "11": {"inputs": {"text": ["10", 0]}, "class_type": "Text Multiline", "_meta": {"title": "Positive Prompt"}}, "15": {"inputs": {"text_positive": ["11", 0], "text_negative": "shadows, gray fill, blurry, watermark, text, dark background, thick, color, shading, gradient, transparency", "style": "sai-line art", "log_prompt": true, "style_positive": true, "style_negative": true}, "class_type": "SDXLPromptStyler", "_meta": {"title": "SDXL Prompt Styler"}}, "16": {"inputs": {"width": ["3", 0], "height": ["3", 1], "crop_w": 0, "crop_h": 0, "target_width": ["3", 0], "target_height": ["3", 1], "text_g": ["15", 0], "text_l": ["15", 0], "clip": ["1", 1]}, "class_type": "CLIPTextEncodeSDXL", "_meta": {"title": "CLIPTextEncodeSDXL"}}, "17": {"inputs": {"width": ["3", 0], "height": ["3", 1], "crop_w": 0, "crop_h": 0, "target_width": ["3", 0], "target_height": ["3", 1], "text_g": ["15", 1], "text_l": ["15", 1], "clip": ["1", 1]}, "class_type": "CLIPTextEncodeSDXL", "_meta": {"title": "CLIPTextEncodeSDXL"}}, "20": {"inputs": {"ipadapter_file": "SDXL/ip-adapter_sdxl.safetensors"}, "class_type": "IPAdapterModelLoader", "_meta": {"title": "IPAdapter Model Loader"}}, "21": {"inputs": {"clip_name": "CLIP-ViT-bigG-14-laion2B-39B-b160k.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "22": {"inputs": {"upscale_method": "nearest-exact", "width": 1024, "height": 1024, "crop": "center", "image": ["29", 0]}, "class_type": "ImageScale", "_meta": {"title": "Upscale Image"}}, "23": {"inputs": {"control_net_name": "control-lora-depth-rank256.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "24": {"inputs": {"control_net_name": "control-lora-canny-rank256.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "25": {"inputs": {"coarse": "enable", "resolution": 1024, "image": ["5", 0]}, "class_type": "LineArtPreprocessor", "_meta": {"title": "Realistic Lineart"}}, "26": {"inputs": {"resolution": 1024, "image": ["5", 0]}, "class_type": "Zoe-DepthMapPreprocessor", "_meta": {"title": "<PERSON>"}}, "27": {"inputs": {"strength": 0.7000000000000002, "start_percent": 0, "end_percent": 1, "positive": ["16", 0], "negative": ["17", 0], "control_net": ["23", 0], "image": ["26", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "28": {"inputs": {"strength": 0.25000000000000006, "start_percent": 0, "end_percent": 1, "positive": ["27", 0], "negative": ["27", 1], "control_net": ["24", 0], "image": ["25", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet"}}, "29": {"inputs": {"image": ["25", 0]}, "class_type": "ImageInvert", "_meta": {"title": "Invert Image"}}, "30": {"inputs": {"pixels": ["29", 0], "vae": ["2", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "31": {"inputs": {"amount": 1, "samples": ["30", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "33": {"inputs": {"add_noise": "enable", "noise_seed": 937570849584701, "steps": 20, "cfg": 10, "sampler_name": "euler_ancestral", "scheduler": "karras", "start_at_step": 0, "end_at_step": 10000, "return_with_leftover_noise": "enable", "model": ["40", 0], "positive": ["28", 0], "negative": ["28", 1], "latent_image": ["31", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "34": {"inputs": {"samples": ["33", 0], "vae": ["2", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "40": {"inputs": {"weight": 0.25000000000000006, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["1", 0], "ipadapter": ["20", 0], "image": ["22", 0], "clip_vision": ["21", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "49": {"inputs": {"filename_prefix": "ComfyUI", "images": ["34", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}