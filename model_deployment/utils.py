"""
Utility functions for Modal ComfyUI deployment.
Contains common helper functions used across the application.
"""

import json
import random
import subprocess
import uuid
import socket
import urllib.request
import urllib.error
from typing import Dict, Any, Optional, Union
from urllib.parse import urlsplit
from pathlib import Path

from .config import MAX_SEED_VALUE, API_TIMEOUT


def extract_object_key(url: str) -> str:
    """
    Extracts the object key (path) from a presigned S3 URL.
    
    Args:
        url (str): The full presigned URL string.
        
    Returns:
        str: The extracted object key. If no path is found, returns an empty string.
    """
    # Parse the URL into components
    parsed_url = urlsplit(url)
    
    # Get the path component from the parsed URL
    path = parsed_url.path
    
    # Split the path by '/' to extract individual segments
    parts = [part for part in path.split('/') if part.strip() != '']
    
    # The last non-empty segment is typically the object key
    if parts:
        return parts[-1]
    else:
        return ''


def find_node_by_type(workflow_data: Dict[str, Any], node_type: str) -> Optional[Dict[str, Any]]:
    """
    Find the first node in the workflow with the specified class_type.
    
    Args:
        workflow_data: The workflow JSON data
        node_type: The class_type to search for
        
    Returns:
        The first matching node or None if not found
    """
    return next(
        (node for node in workflow_data.values() 
         if node.get("class_type") == node_type),
        None
    )


def generate_random_seed() -> int:
    """
    Generate a random seed for image generation.
    
    Returns:
        A random integer seed value
    """
    random.seed()
    return random.randint(0, MAX_SEED_VALUE)


def generate_unique_id() -> str:
    """
    Generate a unique identifier for files and operations.
    
    Returns:
        A unique hex string
    """
    return uuid.uuid4().hex


def load_workflow_json(workflow_path: Path) -> Dict[str, Any]:
    """
    Load and parse a workflow JSON file.
    
    Args:
        workflow_path: Path to the workflow JSON file
        
    Returns:
        Parsed workflow data
        
    Raises:
        FileNotFoundError: If the workflow file doesn't exist
        json.JSONDecodeError: If the JSON is invalid
    """
    if not workflow_path.exists():
        raise FileNotFoundError(f"Workflow file not found: {workflow_path}")
    
    try:
        return json.loads(workflow_path.read_text())
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Invalid JSON in workflow file {workflow_path}: {e}", e.doc, e.pos)


def save_workflow_json(workflow_data: Dict[str, Any], output_path: Path) -> None:
    """
    Save workflow data to a JSON file.
    
    Args:
        workflow_data: The workflow data to save
        output_path: Path where to save the JSON file
    """
    with open(output_path, "w") as f:
        json.dump(workflow_data, f, indent=2)


def check_server_health(port: int, timeout: int = API_TIMEOUT) -> bool:
    """
    Check if the ComfyUI server is healthy and responding.
    
    Args:
        port: The port number where ComfyUI is running
        timeout: Timeout in seconds for the health check
        
    Returns:
        True if server is healthy, False otherwise
    """
    try:
        req = urllib.request.Request(f"http://127.0.0.1:{port}/system_stats")
        urllib.request.urlopen(req, timeout=timeout)
        return True
    except (socket.timeout, urllib.error.URLError):
        return False


def parse_image_urls_string(image_urls_str: str) -> list[str]:
    """
    Parse a comma-separated string of image URLs into a list.
    
    Args:
        image_urls_str: Comma-separated string of URLs
        
    Returns:
        List of URL strings
    """
    if not image_urls_str:
        return []
    return [url.strip() for url in image_urls_str.split(",") if url.strip()]


def validate_batch_size_and_urls(batch_size: int, image_urls: list[str]) -> None:
    """
    Validate that the batch size matches the number of image URLs.
    
    Args:
        batch_size: The requested batch size
        image_urls: List of image URLs
        
    Raises:
        ValueError: If batch size doesn't match number of URLs
    """
    if len(image_urls) != batch_size:
        raise ValueError(f"Number of upload URLs ({len(image_urls)}) must match batch size ({batch_size})")


def cleanup_file(file_path: Path) -> None:
    """
    Safely remove a file if it exists.
    
    Args:
        file_path: Path to the file to remove
    """
    try:
        if file_path.exists():
            file_path.unlink()
    except Exception as e:
        print(f"Warning: Failed to cleanup file {file_path}: {e}")


def find_output_files(output_dir: Path, image_id: str) -> list[Path]:
    """
    Find all output files that start with the given image ID.
    
    Args:
        output_dir: Directory to search in
        image_id: The image ID prefix to search for
        
    Returns:
        List of matching file paths
    """
    if not output_dir.exists():
        return []
    
    return [
        file_path for file_path in output_dir.iterdir()
        if file_path.is_file() and file_path.name.startswith(image_id)
    ]


def create_image_filename(original_filename: str) -> str:
    """
    Create a unique filename for an uploaded image.
    
    Args:
        original_filename: The original filename from the upload
        
    Returns:
        A unique filename string
    """
    unique_id = generate_unique_id()
    return f"{unique_id}_{original_filename}"


def log_request_info(endpoint: str, client_host: str, **kwargs) -> None:
    """
    Log information about an incoming request.
    
    Args:
        endpoint: The API endpoint being called
        client_host: The client's host/IP address
        **kwargs: Additional parameters to log
    """
    params_str = ", ".join(f"{k}: {v}" for k, v in kwargs.items())
    print(f"[{endpoint}] Request from {client_host} - {params_str}")


def handle_subprocess_error(e: Union[subprocess.CalledProcessError, Exception], operation: str) -> None:
    """
    Handle and log subprocess errors consistently.

    Args:
        e: The exception that occurred
        operation: Description of the operation that failed
    """
    if isinstance(e, subprocess.CalledProcessError):
        print(f"{operation} failed with exit code {e.returncode}")
        if e.stdout:
            print(f"Standard Output: {e.stdout}")
        if e.stderr:
            print(f"Standard Error: {e.stderr}")
    else:
        print(f"{operation} failed: {e}")
