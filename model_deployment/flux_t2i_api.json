{"1": {"inputs": {"filename_prefix": "ComfyUI", "images": ["18", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "6": {"inputs": {"ckpt_name": "flux1-dev-fp8.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"seed": 518595436478966, "steps": 30, "cfg": 1, "sampler_name": "deis", "scheduler": "normal", "denoise": 1, "model": ["11", 0], "positive": ["13", 0], "negative": ["14", 0], "latent_image": ["15", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "11": {"inputs": {"lora_name": "c0l0ringb00k_Flux_v1_renderartist.safetensors", "strength_model": 0.8000000000000002, "strength_clip": 1.0000000000000002, "model": ["6", 0], "clip": ["6", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "12": {"inputs": {"samples": ["8", 0], "vae": ["6", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "13": {"inputs": {"clip_l": "c0l0ringb00k, coloring book, line art, high resolution, black and white, colorless", "t5xxl": "c0l0ringb00k, ", "guidance": 3.5, "clip": ["11", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "14": {"inputs": {"text": "", "clip": ["11", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative Prompt"}}, "15": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "18": {"inputs": {"brightness": 0, "contrast": 1, "saturation": 0, "sharpness": 1, "blur": 0, "gaussian_blur": 0, "edge_enhance": 0, "detail_enhance": "false", "image": ["12", 0]}, "class_type": "Image Filter Adjustments", "_meta": {"title": "Image Filter Adjustments"}}}