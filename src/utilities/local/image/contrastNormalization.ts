import { imageUrlToBase64, isBase64Str } from '.'

export async function imageContrastManual(
  src: string,
  options: { contrast?: number; brightness?: number; lightness?: number },
): Promise<string> {
  const { contrast = 1, brightness = 0, lightness = 0 } = options

  const img = new Image()
  img.crossOrigin = 'Anonymous'
  img.src = src

  await new Promise<void>((resolve) => {
    img.onload = () => resolve()
  })

  const canvas = document.createElement('canvas')
  canvas.width = img.width
  canvas.height = img.height
  const ctx = canvas.getContext('2d')!
  ctx.drawImage(img, 0, 0)

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const data = imageData.data

  const factor = (259 * (contrast * 255 + 255)) / (255 * (259 - contrast * 255))

  for (let i = 0; i < data.length; i += 4) {
    for (let j = 0; j < 3; j++) {
      // R, G, B
      // Apply brightness
      let value = data[i + j] + brightness * 255

      // Apply lightness (similar to brightness but adjusted by factor)
      value += lightness * 255

      // Apply contrast
      value = factor * (value - 128) + 128

      // Clamp between 0 and 255
      data[i + j] = Math.max(0, Math.min(255, value))
    }
    // Alpha (data[i+3]) stays the same
  }

  ctx.putImageData(imageData, 0, 0)

  return canvas.toDataURL()
}

export async function imageContrastNormalization(src: string): Promise<string> {
  let b64Str = src
  if (!isBase64Str(src)) {
    b64Str = await imageUrlToBase64(src)
  }
  const img = new Image()
  img.crossOrigin = 'Anonymous' // important if loading external images
  img.src = b64Str

  await new Promise<void>((resolve) => {
    img.onload = () => resolve()
  })

  const canvas = document.createElement('canvas')
  canvas.width = img.width
  canvas.height = img.height
  const ctx = canvas.getContext('2d')!
  ctx.drawImage(img, 0, 0)

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const data = imageData.data

  // You can adjust here: Hard normalization
  for (let i = 0; i < data.length; i += 4) {
    // For each pixel (r, g, b)
    const r = data[i]
    const g = data[i + 1]
    const b = data[i + 2]

    // You could use luminance if you want (grayscale value)
    const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b

    if (luminance < 128) {
      data[i] = 0
      data[i + 1] = 0
      data[i + 2] = 0
    } else {
      data[i] = 255
      data[i + 1] = 255
      data[i + 2] = 255
    }
    // Alpha (data[i+3]) stays unchanged
  }

  ctx.putImageData(imageData, 0, 0)

  return canvas.toDataURL() // This is your normalized src
}
