import { StatusCodes } from 'http-status-codes'
import { notFound } from 'next/navigation'
import { JSX } from 'react'
import { HTTPException } from '@/app/(app)/_backend/common/exception'

export function asyncErrorBoundary<T extends (...args: any[]) => Promise<JSX.Element | void>>(
  fn: T,
): (...args: Parameters<T>) => Promise<JSX.Element | void> {
  return async (...args: Parameters<T>) => {
    try {
      return await fn(...args)
    } catch (error) {
      if (error instanceof HTTPException) {
        if (error.statusCode === StatusCodes.NOT_FOUND) {
          return notFound()
        }
      }
      return notFound()
    }
  }
}
