import escapeHTML from 'escape-html'
import NextImage from 'next/image'
import Link from 'next/link'
import React, { Fragment, JSX } from 'react'
import { components } from '@/app/(app)/_component/BlockRender/components'
import Text from '@/app/(app)/_component/Text'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import { cn } from '@/lib/utils'
import {
  IS_BOLD,
  IS_ITALIC,
  IS_STRIKETHROUGH,
  IS_UNDERLINE,
  IS_CODE,
  IS_SUBSCRIPT,
  IS_SUPERSCRIPT,
} from './RichTextNodeFormat'
import type { SerializedLexicalNode } from './types'

interface Props {
  nodes: SerializedLexicalNode[]
  separation?: string
}

export function Serialize({ nodes, separation }: Props): JSX.Element {
  const separationDefault = separation ?? 'mb-6'
  return (
    <span className="[&>*:not(ul):not(ol):not(li):not(:first-child)]:mt-6">
      {nodes?.map((node, index): JSX.Element | null => {
        if (node.type === 'text') {
          let content = escapeHTML(node.text)
          let text = (
            <span
              key={index}
              dangerouslySetInnerHTML={{ __html: content }}
              className="leading-[1.75]"
            />
          )

          // Apply text formatting based on the flags
          if (node.format & IS_BOLD) {
            text = (
              <Text key={index} as="strong" size="lg" variant="emphasis">
                {text}
              </Text>
            )
          }
          if (node.format & IS_ITALIC) {
            text = (
              <Text key={index} as="em" size="lg" className="italic">
                {text}
              </Text>
            )
          }
          if (node.format & IS_STRIKETHROUGH) {
            text = (
              <Text key={index} size="lg" className="line-through">
                {text}
              </Text>
            )
          }
          if (node.format & IS_UNDERLINE) {
            text = (
              <Text key={index} size="lg" className="underline">
                {text}
              </Text>
            )
          }
          if (node.format & IS_CODE) {
            text = (
              <Text
                key={index}
                as="code"
                size="lg"
                className="font-mono bg-gray-100 px-1 py-0.5 rounded"
              >
                {text}
              </Text>
            )
          }
          if (node.format & IS_SUBSCRIPT) {
            text = (
              <Text key={index} as="sub" size="lg" className="text-sm">
                {text}
              </Text>
            )
          }
          if (node.format & IS_SUPERSCRIPT) {
            text = (
              <Text key={index} as="sup" size="lg" className="text-sm">
                {text}
              </Text>
            )
          }

          return text
        }

        if (node == null) {
          return null
        }

        const serializedChildren = node.children ? <Serialize nodes={node.children} /> : null

        switch (node.type) {
          case 'linebreak':
            return <br key={index} />

          case 'paragraph':
            const isLast = index === nodes.length - 1
            return (
              <Text key={index} as="p" size="lg" className={isLast ? '' : separationDefault}>
                {serializedChildren && serializedChildren.type !== 'p' ? serializedChildren : null}
              </Text>
            )

          case 'heading': {
            const Tag = node?.tag as HeaderLevel
            return (
              <Title key={index} level={Tag} className={separationDefault}>
                {serializedChildren}
              </Title>
            )
          }

          case 'list': {
            const Tag = node?.tag as 'ul' | 'ol'

            return (
              <Tag key={index} className={cn('list-outside list-disc pl-6')}>
                {serializedChildren}
              </Tag>
            )
          }

          case 'listitem': {
            const hasNestedList = node.children?.some((child) => child.type === 'list')
            return (
              <li key={index} className={hasNestedList ? 'list-none' : ''}>
                {serializedChildren}
              </li>
            )
          }

          case 'quote':
            return (
              <Text
                key={index}
                as="blockquote"
                size="lg"
                variant="quote"
                className={cn('border-l-4 pl-4', separationDefault)}
              >
                {serializedChildren}
              </Text>
            )

          case 'link': {
            const { url, newTab, nofollow, rel, linkType } = node.fields || {}
            console.log(node.fields)
            const fullRel = `${rel ?? ''} ${nofollow ? ' nofollow' : ''}`.trim()
            if (linkType === 'custom') {
              return (
                <Link
                  key={index}
                  href={url}
                  target={newTab ? '_blank' : undefined}
                  rel={fullRel}
                  className="text-accent2 hover:text-accent2-lighter underline"
                >
                  {serializedChildren}
                </Link>
              )
            } else if (linkType === 'internal') {
              //We assume it's BLOG
              const { doc, newTab } = node.fields
              const slug = doc?.value.slug || ''
              return (
                <Link
                  key={index}
                  href={`/blog/${slug}`}
                  target={newTab ? '_blank' : undefined}
                  rel={fullRel}
                  className="text-accent2 hover:text-accent2-lighter underline"
                >
                  {serializedChildren}
                </Link>
              )
            } else {
              return (
                <Link
                  key={index}
                  href={url}
                  target={newTab ? '_blank' : undefined}
                  rel={fullRel}
                  className="text-accent2 hover:text-accent2-lighter underline"
                >
                  {serializedChildren}
                </Link>
              )
            }
          }

          case 'inline-image':
            return (
              <Text key={index} className="text-gray-500">
                Internal link coming soon
              </Text>
            )

          case 'upload':
            const { url, alt, width, height } = node.value
            return <NextImage key={index} src={url} alt={alt} width={width} height={height} />

          case 'block':
            const blockType = node.fields.blockType as keyof typeof components
            const Block: React.FC<any> | undefined = components[blockType]
            if (Block) {
              return (
                <section key={index}>
                  <Block {...node.fields} className={separationDefault} />
                </section>
              )
            }

          default:
            return null
        }
      })}
    </span>
  )
}
