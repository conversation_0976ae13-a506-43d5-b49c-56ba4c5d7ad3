// export type SerializedLexicalEditorState = {
//   root: {
//     type: string
//     indent: number
//     version: number
//     children: SerializedLexicalNode[]
//     [k: string]: unknown
//     direction: ('ltr' | 'rtl') | null
//     format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
//   }
// }

import type {
  SerializedAutoLinkNode,
  SerializedBlockNode,
  SerializedHorizontalRuleNode,
  SerializedLinkNode,
  SerializedListItemNode,
  SerializedListNode,
  SerializedParagraphNode,
  SerializedQuoteNode,
  SerializedRelationshipNode,
  SerializedTextNode,
  SerializedUploadNode,
  TypedEditorState,
  SerializedHeadingNode,
} from '@payloadcms/richtext-lexical'

export type SerializedLexicalEditorState = {
  root: {
    type: string
    children: SerializedLexicalNode[]
    direction: ('ltr' | 'rtl') | null
    format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
    indent: number
    version: number
  }
  [k: string]: any
}

export type SerializedLexicalNode = {
  children?: SerializedLexicalNode[]
  direction: ('ltr' | 'rtl') | null
  format: number
  indent: number
  type: string
  version: number
  style?: string
  mode?: string
  text?: string
  [other: string]: any
}

// type SerializedLexicalNode = {
//   type: string
//   version: number
//   direction: ('ltr' | 'rtl') | null
//   format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
//   indent: number
//   children?: SerializedLexicalNode[]
//   [k: string]: unknown
// }
