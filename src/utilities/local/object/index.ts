export function deepEqual<T, K>(a: T, b: T | K): boolean {
  // Handle strict equality and cases where both are undefined or null
  if (a === b) return true

  // If one is null/undefined and the other isn't, they're not equal
  if (a === null || a === undefined || b === null || b === undefined) {
    return false
  }

  // Ensure both are objects
  if (typeof a !== 'object' || typeof b !== 'object') {
    return false
  }

  // Handle arrays
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false
    }
    return true
  } else if (Array.isArray(a) || Array.isArray(b)) {
    return false
  }

  // Combine keys from both objects (including keys with undefined values)
  const keys = new Set([...Object.keys(a), ...Object.keys(b)])

  for (const key of keys) {
    // Check if key exists in both objects
    const keyInA = key in a
    const keyInB = key in b

    if (keyInA !== keyInB) return false

    // @ts-expect-error: we trust that both objects share the same keys here
    if (!deepEqual(a[key], b[key])) return false
  }

  return true
}

export function isObjectEmpty(obj: unknown): boolean {
  if (obj === null || obj === undefined) return true
  if (typeof obj !== 'object') return false

  if (Array.isArray(obj)) {
    return obj.every(isObjectEmpty)
  }

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (!isObjectEmpty((obj as Record<string, unknown>)[key])) {
        return false
      }
    }
  }

  return true
}

export function allValuesInObjectEmpty<T extends Record<string, any>>(obj: T): boolean {
  return Object.values(obj).every((value) => {
    return (
      value === undefined ||
      value === null ||
      (typeof value === 'string' && value.trim() === '') ||
      (Array.isArray(value) && value.length === 0) ||
      (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
    )
  })
}
