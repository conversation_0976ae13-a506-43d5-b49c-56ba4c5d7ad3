import { NextRequest } from 'next/server'
import { createHash } from 'crypto'

export function getClientIP(request: NextRequest): string {
  // Priority order for different deployment scenarios
  const ipHeaders = [
    'cf-connecting-ip', // Cloudflare
    'x-forwarded-for', // Standard proxy/load balancer
    'x-real-ip', // Nginx proxy
    'x-client-ip', // Apache
    'x-forwarded', // General forwarded
    'forwarded-for', // Alternative
    'forwarded', // RFC 7239 standard
  ]

  for (const header of ipHeaders) {
    const value = request.headers.get(header)
    if (value) {
      // Handle comma-separated IPs (take the first real IP)
      const ips = value.split(',').map((ip) => ip.trim())
      for (const ip of ips) {
        if (isValidIP(ip) && !isPrivateIP(ip)) {
          return ip
        }
      }
    }
  }

  // If no valid public IP found, return first available
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  return request.headers.get('x-real-ip') || request.headers.get('cf-connecting-ip') || 'unknown'
}

function isValidIP(ip: string): boolean {
  // IPv4 validation
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (ipv4Regex.test(ip)) {
    const parts = ip.split('.').map(Number)
    return parts.every((part) => part >= 0 && part <= 255)
  }

  // IPv6 validation (basic)
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  return ipv6Regex.test(ip)
}

function isPrivateIP(ip: string): boolean {
  // Check for private IP ranges
  const privateRanges = [
    /^10\./, // 10.0.0.0/8
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
    /^192\.168\./, // ***********/16
    /^127\./, // *********/8 (localhost)
    /^169\.254\./, // ***********/16 (link-local)
  ]

  return privateRanges.some((range) => range.test(ip))
}

// Additional utility for getting comprehensive network info
// export function getNetworkFingerprint(request: NextRequest) {
//   // Convert headers to array to count them
//   const headerEntries = Array.from(request.headers.entries())
//   const headerNames = headerEntries.map(([name]) => name).sort()

//   return {
//     ip: getClientIP(request),
//     userAgent: request.headers.get('user-agent') || '',
//     acceptLanguage: request.headers.get('accept-language') || '',
//     acceptEncoding: request.headers.get('accept-encoding') || '',
//     connection: request.headers.get('connection') || '',
//     dnt: request.headers.get('dnt') || '',

//     // Security headers
//     secFetchSite: request.headers.get('sec-fetch-site') || '',
//     secFetchMode: request.headers.get('sec-fetch-mode') || '',
//     secFetchDest: request.headers.get('sec-fetch-dest') || '',

//     // Client hints
//     secChUa: request.headers.get('sec-ch-ua') || '',
//     secChUaPlatform: request.headers.get('sec-ch-ua-platform') || '',
//     secChUaMobile: request.headers.get('sec-ch-ua-mobile') || '',

//     // Proxy detection
//     hasXForwardedFor: !!request.headers.get('x-forwarded-for'),
//     hasXRealIp: !!request.headers.get('x-real-ip'),
//     hasCfConnectingIp: !!request.headers.get('cf-connecting-ip'),

//     // Header fingerprint
//     headerCount: headerEntries.length,
//     headerNames: headerNames,
//   }
// }

export function getNetworkFingerprint(request: NextRequest) {
  const headerEntries = Array.from(request.headers.entries())

  // Filter out navigation-dependent headers for stability
  const stableHeaders = headerEntries.filter(([name]) => {
    const lowerName = name.toLowerCase()
    return !['referer', 'origin', 'sec-fetch-site', 'sec-fetch-mode', 'sec-fetch-dest'].includes(
      lowerName,
    )
  })

  // Create header order fingerprint (extremely stable and unique)
  const headerOrder = headerEntries.map(([name]) => name.toLowerCase()).sort()
  const headerOrderHash = createHash('sha256')
    .update(headerOrder.join('|'))
    .digest('hex')
    .substring(0, 16)

  return {
    // Core stable identifiers
    ip: getClientIP(request),

    // Browser signature (normalize for consistency)
    userAgentCore: normalizeUserAgent(request.headers.get('user-agent') || ''),

    // Language/encoding preferences (very stable)
    acceptLanguage: normalizeAcceptLanguage(request.headers.get('accept-language') || ''),
    acceptEncoding: normalizeAcceptEncoding(request.headers.get('accept-encoding') || ''),

    // Privacy settings (stable)
    dnt: request.headers.get('dnt') || '',

    // Client hints (stable, Chrome-specific)
    secChUa: normalizeSecChUa(request.headers.get('sec-ch-ua') || ''),
    secChUaPlatform: request.headers.get('sec-ch-ua-platform') || '',
    secChUaMobile: request.headers.get('sec-ch-ua-mobile') || '',
    secChUaArch: request.headers.get('sec-ch-ua-arch') || '',

    // Connection characteristics (stable)
    connection: request.headers.get('connection') || '',
    upgradeInsecureRequests: request.headers.get('upgrade-insecure-requests') || '',

    // Proxy detection (stable)
    hasXForwardedFor: !!request.headers.get('x-forwarded-for'),
    hasXRealIp: !!request.headers.get('x-real-ip'),
    hasCfConnectingIp: !!request.headers.get('cf-connecting-ip'),

    // Header pattern analysis (very unique and stable)
    headerOrderHash: headerOrderHash,
    stableHeaderCount: stableHeaders.length,

    // REMOVED: secFetchSite, secFetchMode, secFetchDest (these change with OAuth)
    // REMOVED: headerNames array (replaced with headerOrderHash)
  }
}

// Normalization functions for consistency
function normalizeUserAgent(ua: string): string {
  if (!ua) return ''

  // Extract core browser info, ignore detailed version numbers that might change
  const coreMatch = ua.match(/(Chrome|Firefox|Safari|Edge)\/(\d+)/)
  if (coreMatch) {
    return `${coreMatch[1]}/${coreMatch[2]}`
  }

  // Fallback to first 100 characters
  return ua.substring(0, 100)
}

function normalizeAcceptLanguage(lang: string): string {
  if (!lang) return ''

  // Keep only primary languages, ignore quality values
  return lang
    .split(',')
    .map((l) => l.split(';')[0].trim())
    .slice(0, 3) // Top 3 languages only
    .join(',')
}

function normalizeAcceptEncoding(encoding: string): string {
  if (!encoding) return ''

  // Normalize encoding list
  return encoding
    .split(',')
    .map((e) => e.trim())
    .sort()
    .join(',')
}

function normalizeSecChUa(secChUa: string): string {
  if (!secChUa) return ''

  // Extract browser names only, ignore versions
  const brands = secChUa.match(/"([^"]+)"/g)
  return brands ? brands.sort().join(',') : secChUa
}

export function generateNetworkFingerprint(fingerprint: any): string {
  const str = JSON.stringify(fingerprint, Object.keys(fingerprint).sort())
  return createHash('sha256').update(str).digest('hex').substring(0, 32)
}
