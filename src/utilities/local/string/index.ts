export function capitalizeAndReplace(input: string | null | undefined): string {
  if (input == null) {
    return ''
  }

  return input
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/^\w/, (c) => c.toUpperCase()) // Capitalize the first letter
}

export function capitalizeAllLetters(input: string | null | undefined): string {
  if (input == null) {
    return ''
  }
  return input.toUpperCase()
}
