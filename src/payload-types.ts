/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
    'public-users': PublicUserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    'public-users': PublicUser;
    posts: Post;
    categories: Category;
    tags: Tag;
    'subscription-plans': SubscriptionPlan;
    'subscription-records': SubscriptionRecord;
    'generated-images': GeneratedImage;
    features: Feature;
    scopes: Scope;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    'public-users': PublicUsersSelect<false> | PublicUsersSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    categories: CategoriesSelect<false> | CategoriesSelect<true>;
    tags: TagsSelect<false> | TagsSelect<true>;
    'subscription-plans': SubscriptionPlansSelect<false> | SubscriptionPlansSelect<true>;
    'subscription-records': SubscriptionRecordsSelect<false> | SubscriptionRecordsSelect<true>;
    'generated-images': GeneratedImagesSelect<false> | GeneratedImagesSelect<true>;
    features: FeaturesSelect<false> | FeaturesSelect<true>;
    scopes: ScopesSelect<false> | ScopesSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user:
    | (User & {
        collection: 'users';
      })
    | (PublicUser & {
        collection: 'public-users';
      });
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
export interface PublicUserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  author_name: string;
  role: 'admin' | 'editor' | 'contributer';
  createdAt: string;
  updatedAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  alt: string;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "public-users".
 */
export interface PublicUser {
  id: string;
  firstName: string;
  lastName: string;
  freeCredits: number;
  paidCredits: number;
  rolloverCredits: number;
  address?: string | null;
  country?: string | null;
  state?: string | null;
  city?: string | null;
  fingerprintHash?: string | null;
  ipAddress?: string | null;
  role: 'admin' | 'editor' | 'subscriber' | 'free';
  /**
   * Not subscription plan, but subscription record.
   */
  subscription?: (number | null) | SubscriptionRecord;
  verified?: boolean | null;
  verificationToken?: string | null;
  createdAt: string;
  isSSO: boolean;
  lastOtpSent?: string | null;
  updatedAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscription-records".
 */
export interface SubscriptionRecord {
  id: number;
  subscriptionId?: string | null;
  user?: (string | null) | PublicUser;
  subscriptionPlan?: (string | null) | SubscriptionPlan;
  customerId?: string | null;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete' | 'incomplete_expired' | 'trialing' | 'unpaid' | 'paused';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAt?: string | null;
  canceledAt?: string | null;
  createdAt: string;
  updatedAt: string;
  /**
   * Strictly for stripe, mainly to check reasons for possible payment failures
   */
  stripeComment?: string | null;
  /**
   * PLEASE explain why this subscription record was added / edited if manually added / edited. If it is a dispute, PLEASE add the header of the email chain or the STRIPE dispute id. Or any other link references if external.
   */
  comments?: string | null;
  customTitle?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscription-plans".
 */
export interface SubscriptionPlan {
  /**
   * Refer to the Stripe Product ID and retrieve it
   */
  id: string;
  name: string;
  description?: string | null;
  scope?: (number | null) | Scope;
  /**
   * A product can have more than two prices (yearly / monthly). Denoted by the Stripe Price ID
   */
  stripePriceId: string;
  price: number;
  monthlyCreditStipend: number;
  subscription_duration: 'day' | 'week' | 'month' | 'year';
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "scopes".
 */
export interface Scope {
  id: number;
  scope: string;
  /**
   * Features allocated to a scope
   */
  features?: (number | Feature)[] | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "features".
 */
export interface Feature {
  id: number;
  feature: string;
  creditCost: number;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: number;
  title: string;
  category?: (number | Category)[] | null;
  tags?: (number | Tag)[] | null;
  image?: (number | null) | Media;
  summary?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  layout?:
    | (
        | {
            richText?: {
              root: {
                type: string;
                children: {
                  type: string;
                  version: number;
                  [k: string]: unknown;
                }[];
                direction: ('ltr' | 'rtl') | null;
                format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
                indent: number;
                version: number;
              };
              [k: string]: unknown;
            } | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'content';
          }
        | {
            youtubeUrl?: string | null;
            id?: string | null;
            blockName?: string | null;
            blockType: 'youtubeEmbed';
          }
        | {
            title?: string | null;
            description?: string | null;
            downloadUrl: string;
            id?: string | null;
            blockName?: string | null;
            blockType: 'downloadLock';
          }
      )[]
    | null;
  readingTime?: number | null;
  slug?: string | null;
  author?: (number | null) | User;
  /**
   * Posts will not be public until this date
   */
  publishDate?: string | null;
  meta?: {
    title?: string | null;
    description?: string | null;
    /**
     * Maximum upload file size: 12MB. Recommended file size for images is <500KB.
     */
    image?: (number | null) | Media;
  };
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories".
 */
export interface Category {
  id: number;
  name?: string | null;
  icon?: string | null;
  /**
   * Archiving filters it from being an option in the posts collection
   */
  archived?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags".
 */
export interface Tag {
  id: number;
  name?: string | null;
  /**
   * Archiving filters it from being an option in the posts collection
   */
  archived?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "generated-images".
 */
export interface GeneratedImage {
  id: string;
  taskType: string;
  prompt?: string | null;
  status?: string | null;
  createdAt: string;
  updatedAt: string;
  createdBy: string | PublicUser;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'public-users';
        value: string | PublicUser;
      } | null)
    | ({
        relationTo: 'posts';
        value: number | Post;
      } | null)
    | ({
        relationTo: 'categories';
        value: number | Category;
      } | null)
    | ({
        relationTo: 'tags';
        value: number | Tag;
      } | null)
    | ({
        relationTo: 'subscription-plans';
        value: string | SubscriptionPlan;
      } | null)
    | ({
        relationTo: 'subscription-records';
        value: number | SubscriptionRecord;
      } | null)
    | ({
        relationTo: 'generated-images';
        value: string | GeneratedImage;
      } | null)
    | ({
        relationTo: 'features';
        value: number | Feature;
      } | null)
    | ({
        relationTo: 'scopes';
        value: number | Scope;
      } | null);
  globalSlug?: string | null;
  user:
    | {
        relationTo: 'users';
        value: number | User;
      }
    | {
        relationTo: 'public-users';
        value: string | PublicUser;
      };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user:
    | {
        relationTo: 'users';
        value: number | User;
      }
    | {
        relationTo: 'public-users';
        value: string | PublicUser;
      };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  author_name?: T;
  role?: T;
  createdAt?: T;
  updatedAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "public-users_select".
 */
export interface PublicUsersSelect<T extends boolean = true> {
  id?: T;
  firstName?: T;
  lastName?: T;
  freeCredits?: T;
  paidCredits?: T;
  rolloverCredits?: T;
  address?: T;
  country?: T;
  state?: T;
  city?: T;
  fingerprintHash?: T;
  ipAddress?: T;
  role?: T;
  subscription?: T;
  verified?: T;
  verificationToken?: T;
  createdAt?: T;
  isSSO?: T;
  lastOtpSent?: T;
  updatedAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  category?: T;
  tags?: T;
  image?: T;
  summary?: T;
  layout?:
    | T
    | {
        content?:
          | T
          | {
              richText?: T;
              id?: T;
              blockName?: T;
            };
        youtubeEmbed?:
          | T
          | {
              youtubeUrl?: T;
              id?: T;
              blockName?: T;
            };
        downloadLock?:
          | T
          | {
              title?: T;
              description?: T;
              downloadUrl?: T;
              id?: T;
              blockName?: T;
            };
      };
  readingTime?: T;
  slug?: T;
  author?: T;
  publishDate?: T;
  meta?:
    | T
    | {
        title?: T;
        description?: T;
        image?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "categories_select".
 */
export interface CategoriesSelect<T extends boolean = true> {
  name?: T;
  icon?: T;
  archived?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tags_select".
 */
export interface TagsSelect<T extends boolean = true> {
  name?: T;
  archived?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscription-plans_select".
 */
export interface SubscriptionPlansSelect<T extends boolean = true> {
  id?: T;
  name?: T;
  description?: T;
  scope?: T;
  stripePriceId?: T;
  price?: T;
  monthlyCreditStipend?: T;
  subscription_duration?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "subscription-records_select".
 */
export interface SubscriptionRecordsSelect<T extends boolean = true> {
  subscriptionId?: T;
  user?: T;
  subscriptionPlan?: T;
  customerId?: T;
  status?: T;
  currentPeriodStart?: T;
  currentPeriodEnd?: T;
  cancelAt?: T;
  canceledAt?: T;
  createdAt?: T;
  updatedAt?: T;
  stripeComment?: T;
  comments?: T;
  customTitle?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "generated-images_select".
 */
export interface GeneratedImagesSelect<T extends boolean = true> {
  id?: T;
  taskType?: T;
  prompt?: T;
  status?: T;
  createdAt?: T;
  updatedAt?: T;
  createdBy?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "features_select".
 */
export interface FeaturesSelect<T extends boolean = true> {
  feature?: T;
  creditCost?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "scopes_select".
 */
export interface ScopesSelect<T extends boolean = true> {
  scope?: T;
  features?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}