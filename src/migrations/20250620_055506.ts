import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "features" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"feature" varchar NOT NULL,
  	"credit_cost" numeric NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS "scopes" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"scope" varchar NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS "scopes_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"features_id" integer
  );
  
  ALTER TABLE "public_users" ADD COLUMN "fingerprint_hash" varchar;
  ALTER TABLE "public_users" ADD COLUMN "ip_address" varchar;
  ALTER TABLE "subscription_plans" ADD COLUMN "scope_id" integer;
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "features_id" integer;
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "scopes_id" integer;
  DO $$ BEGIN
   ALTER TABLE "scopes_rels" ADD CONSTRAINT "scopes_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."scopes"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "scopes_rels" ADD CONSTRAINT "scopes_rels_features_fk" FOREIGN KEY ("features_id") REFERENCES "public"."features"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE UNIQUE INDEX IF NOT EXISTS "features_feature_idx" ON "features" USING btree ("feature");
  CREATE INDEX IF NOT EXISTS "features_updated_at_idx" ON "features" USING btree ("updated_at");
  CREATE INDEX IF NOT EXISTS "features_created_at_idx" ON "features" USING btree ("created_at");
  CREATE UNIQUE INDEX IF NOT EXISTS "scopes_scope_idx" ON "scopes" USING btree ("scope");
  CREATE INDEX IF NOT EXISTS "scopes_updated_at_idx" ON "scopes" USING btree ("updated_at");
  CREATE INDEX IF NOT EXISTS "scopes_created_at_idx" ON "scopes" USING btree ("created_at");
  CREATE INDEX IF NOT EXISTS "scopes_rels_order_idx" ON "scopes_rels" USING btree ("order");
  CREATE INDEX IF NOT EXISTS "scopes_rels_parent_idx" ON "scopes_rels" USING btree ("parent_id");
  CREATE INDEX IF NOT EXISTS "scopes_rels_path_idx" ON "scopes_rels" USING btree ("path");
  CREATE INDEX IF NOT EXISTS "scopes_rels_features_id_idx" ON "scopes_rels" USING btree ("features_id");
  DO $$ BEGIN
   ALTER TABLE "subscription_plans" ADD CONSTRAINT "subscription_plans_scope_id_scopes_id_fk" FOREIGN KEY ("scope_id") REFERENCES "public"."scopes"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_features_fk" FOREIGN KEY ("features_id") REFERENCES "public"."features"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_scopes_fk" FOREIGN KEY ("scopes_id") REFERENCES "public"."scopes"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "subscription_plans_scope_idx" ON "subscription_plans" USING btree ("scope_id");
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_features_id_idx" ON "payload_locked_documents_rels" USING btree ("features_id");
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_scopes_id_idx" ON "payload_locked_documents_rels" USING btree ("scopes_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "features" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "scopes" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "scopes_rels" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "features" CASCADE;
  DROP TABLE "scopes" CASCADE;
  DROP TABLE "scopes_rels" CASCADE;
  ALTER TABLE "subscription_plans" DROP CONSTRAINT "subscription_plans_scope_id_scopes_id_fk";
  
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_features_fk";
  
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_scopes_fk";
  
  DROP INDEX IF EXISTS "subscription_plans_scope_idx";
  DROP INDEX IF EXISTS "payload_locked_documents_rels_features_id_idx";
  DROP INDEX IF EXISTS "payload_locked_documents_rels_scopes_id_idx";
  ALTER TABLE "public_users" DROP COLUMN IF EXISTS "fingerprint_hash";
  ALTER TABLE "public_users" DROP COLUMN IF EXISTS "ip_address";
  ALTER TABLE "subscription_plans" DROP COLUMN IF EXISTS "scope_id";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "features_id";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "scopes_id";`)
}
