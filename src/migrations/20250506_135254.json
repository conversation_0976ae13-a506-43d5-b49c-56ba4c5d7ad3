{"id": "6267cfa5-3cc8-4cbc-a2b8-9c04f15bb71e", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "author_name": {"name": "author_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "enum_users_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'contributer'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media": {"name": "media", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.public_users": {"name": "public_users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "free_credits": {"name": "free_credits", "type": "numeric", "primaryKey": false, "notNull": true, "default": 0}, "paid_credits": {"name": "paid_credits", "type": "numeric", "primaryKey": false, "notNull": true, "default": 0}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "enum_public_users_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'free'"}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verification_token": {"name": "verification_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"public_users_updated_at_idx": {"name": "public_users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "public_users_email_idx": {"name": "public_users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "image_id": {"name": "image_id", "type": "integer", "primaryKey": false, "notNull": false}, "summary": {"name": "summary", "type": "jsonb", "primaryKey": false, "notNull": false}, "rich_text": {"name": "rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "reading_time": {"name": "reading_time", "type": "numeric", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "integer", "primaryKey": false, "notNull": false}, "publish_date": {"name": "publish_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "meta_image_id": {"name": "meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_posts_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"posts_image_idx": {"name": "posts_image_idx", "columns": [{"expression": "image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_author_idx": {"name": "posts_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_meta_meta_image_idx": {"name": "posts_meta_meta_image_idx", "columns": [{"expression": "meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_updated_at_idx": {"name": "posts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts__status_idx": {"name": "posts__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_image_id_media_id_fk": {"name": "posts_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "columnsFrom": ["image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_author_id_users_id_fk": {"name": "posts_author_id_users_id_fk", "tableFrom": "posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "posts_meta_image_id_media_id_fk": {"name": "posts_meta_image_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "columnsFrom": ["meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts_rels": {"name": "posts_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"posts_rels_order_idx": {"name": "posts_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_parent_idx": {"name": "posts_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_path_idx": {"name": "posts_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_categories_id_idx": {"name": "posts_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_tags_id_idx": {"name": "posts_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_rels_parent_fk": {"name": "posts_rels_parent_fk", "tableFrom": "posts_rels", "tableTo": "posts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_categories_fk": {"name": "posts_rels_categories_fk", "tableFrom": "posts_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_tags_fk": {"name": "posts_rels_tags_fk", "tableFrom": "posts_rels", "tableTo": "tags", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v": {"name": "_posts_v", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_image_id": {"name": "version_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_summary": {"name": "version_summary", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_rich_text": {"name": "version_rich_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_reading_time": {"name": "version_reading_time", "type": "numeric", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_author_id": {"name": "version_author_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_publish_date": {"name": "version_publish_date", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_meta_title": {"name": "version_meta_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_description": {"name": "version_meta_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_meta_image_id": {"name": "version_meta_image_id", "type": "integer", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__posts_v_version_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_parent_idx": {"name": "_posts_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_image_idx": {"name": "_posts_v_version_version_image_idx", "columns": [{"expression": "version_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_author_idx": {"name": "_posts_v_version_version_author_idx", "columns": [{"expression": "version_author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_meta_version_meta_image_idx": {"name": "_posts_v_version_meta_version_meta_image_idx", "columns": [{"expression": "version_meta_image_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_updated_at_idx": {"name": "_posts_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_created_at_idx": {"name": "_posts_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version__status_idx": {"name": "_posts_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_created_at_idx": {"name": "_posts_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_updated_at_idx": {"name": "_posts_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_latest_idx": {"name": "_posts_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_parent_id_posts_id_fk": {"name": "_posts_v_parent_id_posts_id_fk", "tableFrom": "_posts_v", "tableTo": "posts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_image_id_media_id_fk": {"name": "_posts_v_version_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "columnsFrom": ["version_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_author_id_users_id_fk": {"name": "_posts_v_version_author_id_users_id_fk", "tableFrom": "_posts_v", "tableTo": "users", "columnsFrom": ["version_author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_meta_image_id_media_id_fk": {"name": "_posts_v_version_meta_image_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "columnsFrom": ["version_meta_image_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public._posts_v_rels": {"name": "_posts_v_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_rels_order_idx": {"name": "_posts_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_parent_idx": {"name": "_posts_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_path_idx": {"name": "_posts_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_categories_id_idx": {"name": "_posts_v_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_tags_id_idx": {"name": "_posts_v_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_rels_parent_fk": {"name": "_posts_v_rels_parent_fk", "tableFrom": "_posts_v_rels", "tableTo": "_posts_v", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_categories_fk": {"name": "_posts_v_rels_categories_fk", "tableFrom": "_posts_v_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_tags_fk": {"name": "_posts_v_rels_tags_fk", "tableFrom": "_posts_v_rels", "tableTo": "tags", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"categories_updated_at_idx": {"name": "categories_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "categories_created_at_idx": {"name": "categories_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "archived": {"name": "archived", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tags_updated_at_idx": {"name": "tags_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "tags_created_at_idx": {"name": "tags_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_plans": {"name": "subscription_plans", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "stripe_price_id": {"name": "stripe_price_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric", "primaryKey": false, "notNull": true}, "subscription_duration": {"name": "subscription_duration", "type": "enum_subscription_plans_subscription_duration", "typeSchema": "public", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"subscription_plans_stripe_price_id_idx": {"name": "subscription_plans_stripe_price_id_idx", "columns": [{"expression": "stripe_price_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "subscription_plans_updated_at_idx": {"name": "subscription_plans_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_plans_created_at_idx": {"name": "subscription_plans_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_records": {"name": "subscription_records", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "subscription_plan_id": {"name": "subscription_plan_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_subscription_records_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'incomplete'"}, "current_period_start": {"name": "current_period_start", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true}, "cancel_at": {"name": "cancel_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "canceled_at": {"name": "canceled_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "comments": {"name": "comments", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "custom_title": {"name": "custom_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"subscription_records_user_idx": {"name": "subscription_records_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "subscription_records_subscription_plan_idx": {"name": "subscription_records_subscription_plan_idx", "columns": [{"expression": "subscription_plan_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"subscription_records_user_id_public_users_id_fk": {"name": "subscription_records_user_id_public_users_id_fk", "tableFrom": "subscription_records", "tableTo": "public_users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "subscription_records_subscription_plan_id_subscription_plans_id_fk": {"name": "subscription_records_subscription_plan_id_subscription_plans_id_fk", "tableFrom": "subscription_records", "tableTo": "subscription_plans", "columnsFrom": ["subscription_plan_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generated_images": {"name": "generated_images", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "task_type": {"name": "task_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'generating'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by_id": {"name": "created_by_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"generated_images_created_by_idx": {"name": "generated_images_created_by_idx", "columns": [{"expression": "created_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_images_filename_idx": {"name": "generated_images_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"generated_images_created_by_id_public_users_id_fk": {"name": "generated_images_created_by_id_public_users_id_fk", "tableFrom": "generated_images", "tableTo": "public_users", "columnsFrom": ["created_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents": {"name": "payload_locked_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "integer", "primaryKey": false, "notNull": false}, "public_users_id": {"name": "public_users_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "integer", "primaryKey": false, "notNull": false}, "categories_id": {"name": "categories_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags_id": {"name": "tags_id", "type": "integer", "primaryKey": false, "notNull": false}, "subscription_plans_id": {"name": "subscription_plans_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "subscription_records_id": {"name": "subscription_records_id", "type": "integer", "primaryKey": false, "notNull": false}, "generated_images_id": {"name": "generated_images_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_public_users_id_idx": {"name": "payload_locked_documents_rels_public_users_id_idx", "columns": [{"expression": "public_users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_posts_id_idx": {"name": "payload_locked_documents_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_categories_id_idx": {"name": "payload_locked_documents_rels_categories_id_idx", "columns": [{"expression": "categories_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_tags_id_idx": {"name": "payload_locked_documents_rels_tags_id_idx", "columns": [{"expression": "tags_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_subscription_plans_id_idx": {"name": "payload_locked_documents_rels_subscription_plans_id_idx", "columns": [{"expression": "subscription_plans_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_subscription_records_id_idx": {"name": "payload_locked_documents_rels_subscription_records_id_idx", "columns": [{"expression": "subscription_records_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_generated_images_id_idx": {"name": "payload_locked_documents_rels_generated_images_id_idx", "columns": [{"expression": "generated_images_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_public_users_fk": {"name": "payload_locked_documents_rels_public_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "public_users", "columnsFrom": ["public_users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_posts_fk": {"name": "payload_locked_documents_rels_posts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "posts", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_categories_fk": {"name": "payload_locked_documents_rels_categories_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "categories", "columnsFrom": ["categories_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_tags_fk": {"name": "payload_locked_documents_rels_tags_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "tags", "columnsFrom": ["tags_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_subscription_plans_fk": {"name": "payload_locked_documents_rels_subscription_plans_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "subscription_plans", "columnsFrom": ["subscription_plans_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_subscription_records_fk": {"name": "payload_locked_documents_rels_subscription_records_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "subscription_records", "columnsFrom": ["subscription_records_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_generated_images_fk": {"name": "payload_locked_documents_rels_generated_images_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "generated_images", "columnsFrom": ["generated_images_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences": {"name": "payload_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "integer", "primaryKey": false, "notNull": false}, "public_users_id": {"name": "public_users_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_public_users_id_idx": {"name": "payload_preferences_rels_public_users_id_idx", "columns": [{"expression": "public_users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_public_users_fk": {"name": "payload_preferences_rels_public_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "public_users", "columnsFrom": ["public_users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payload_migrations": {"name": "payload_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.enum_users_role": {"name": "enum_users_role", "schema": "public", "values": ["admin", "editor", "contributer"]}, "public.enum_public_users_role": {"name": "enum_public_users_role", "schema": "public", "values": ["admin", "editor", "subscriber", "free"]}, "public.enum_posts_status": {"name": "enum_posts_status", "schema": "public", "values": ["draft", "published"]}, "public.enum__posts_v_version_status": {"name": "enum__posts_v_version_status", "schema": "public", "values": ["draft", "published"]}, "public.enum_subscription_plans_subscription_duration": {"name": "enum_subscription_plans_subscription_duration", "schema": "public", "values": ["day", "week", "month", "year"]}, "public.enum_subscription_records_status": {"name": "enum_subscription_records_status", "schema": "public", "values": ["active", "canceled", "past_due", "incomplete", "incomplete_expired", "trialing", "unpaid", "paused"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}