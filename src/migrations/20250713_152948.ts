import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "posts_blocks_download_lock" ALTER COLUMN "title" SET DEFAULT 'Your download is waiting!';
  ALTER TABLE "_posts_v_blocks_download_lock" ALTER COLUMN "title" SET DEFAULT 'Your download is waiting!';
  ALTER TABLE "public_users" ADD COLUMN "last_otp_sent" timestamp(3) with time zone;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "posts_blocks_download_lock" ALTER COLUMN "title" SET DEFAULT 'Download Locked Content';
  ALTER TABLE "_posts_v_blocks_download_lock" ALTER COLUMN "title" SET DEFAULT 'Download Locked Content';
  ALTER TABLE "public_users" DROP COLUMN IF EXISTS "last_otp_sent";`)
}
