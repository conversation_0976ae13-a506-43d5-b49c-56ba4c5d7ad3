import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "posts_blocks_download_lock" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar DEFAULT 'Download Locked Content',
  	"description" varchar,
  	"download_url" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE IF NOT EXISTS "_posts_v_blocks_download_lock" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar DEFAULT 'Download Locked Content',
  	"description" varchar,
  	"download_url" varchar,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  DO $$ BEGIN
   ALTER TABLE "posts_blocks_download_lock" ADD CONSTRAINT "posts_blocks_download_lock_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."posts"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "_posts_v_blocks_download_lock" ADD CONSTRAINT "_posts_v_blocks_download_lock_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_posts_v"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "posts_blocks_download_lock_order_idx" ON "posts_blocks_download_lock" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "posts_blocks_download_lock_parent_id_idx" ON "posts_blocks_download_lock" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "posts_blocks_download_lock_path_idx" ON "posts_blocks_download_lock" USING btree ("_path");
  CREATE INDEX IF NOT EXISTS "_posts_v_blocks_download_lock_order_idx" ON "_posts_v_blocks_download_lock" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "_posts_v_blocks_download_lock_parent_id_idx" ON "_posts_v_blocks_download_lock" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "_posts_v_blocks_download_lock_path_idx" ON "_posts_v_blocks_download_lock" USING btree ("_path");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "posts_blocks_download_lock" CASCADE;
  DROP TABLE "_posts_v_blocks_download_lock" CASCADE;`)
}
