import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "public_users" ADD COLUMN "verified" boolean DEFAULT false;
  ALTER TABLE "public_users" ADD COLUMN "verification_token" varchar;`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "public_users" DROP COLUMN IF EXISTS "verified";
  ALTER TABLE "public_users" DROP COLUMN IF EXISTS "verification_token";`)
}
