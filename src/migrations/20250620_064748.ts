import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "public_users" ADD COLUMN "subscription_id" integer;
  DO $$ BEGIN
   ALTER TABLE "public_users" ADD CONSTRAINT "public_users_subscription_id_subscription_records_id_fk" FOREIGN KEY ("subscription_id") REFERENCES "public"."subscription_records"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "public_users_subscription_idx" ON "public_users" USING btree ("subscription_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "public_users" DROP CONSTRAINT "public_users_subscription_id_subscription_records_id_fk";
  
  DROP INDEX IF EXISTS "public_users_subscription_idx";
  ALTER TABLE "public_users" DROP COLUMN IF EXISTS "subscription_id";`)
}
