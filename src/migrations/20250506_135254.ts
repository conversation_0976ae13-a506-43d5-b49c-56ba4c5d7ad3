import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "generated_images" (
  	"id" varchar PRIMARY KEY NOT NULL,
  	"task_type" varchar NOT NULL,
  	"prompt" varchar,
  	"status" varchar DEFAULT 'generating',
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_by_id" varchar NOT NULL,
  	"url" varchar,
  	"thumbnail_u_r_l" varchar,
  	"filename" varchar,
  	"mime_type" varchar,
  	"filesize" numeric,
  	"width" numeric,
  	"height" numeric,
  	"focal_x" numeric,
  	"focal_y" numeric
  );
  
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "generated_images_id" varchar;
  DO $$ BEGIN
   ALTER TABLE "generated_images" ADD CONSTRAINT "generated_images_created_by_id_public_users_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."public_users"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "generated_images_created_by_idx" ON "generated_images" USING btree ("created_by_id");
  CREATE UNIQUE INDEX IF NOT EXISTS "generated_images_filename_idx" ON "generated_images" USING btree ("filename");
  DO $$ BEGIN
   ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_generated_images_fk" FOREIGN KEY ("generated_images_id") REFERENCES "public"."generated_images"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "payload_locked_documents_rels_generated_images_id_idx" ON "payload_locked_documents_rels" USING btree ("generated_images_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "generated_images" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "generated_images" CASCADE;
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_generated_images_fk";
  
  DROP INDEX IF EXISTS "payload_locked_documents_rels_generated_images_id_idx";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN IF EXISTS "generated_images_id";`)
}
