/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:db-schema` to regenerate this file.
 */

import {
  pgTable,
  index,
  uniqueIndex,
  foreignKey,
  serial,
  varchar,
  timestamp,
  numeric,
  boolean,
  integer,
  jsonb,
  pgEnum,
} from '@payloadcms/db-postgres/drizzle/pg-core'
import { sql, relations } from '@payloadcms/db-postgres/drizzle'
export const enum_users_role = pgEnum('enum_users_role', ['admin', 'editor', 'contributer'])
export const enum_public_users_role = pgEnum('enum_public_users_role', [
  'admin',
  'editor',
  'subscriber',
  'free',
])
export const enum_posts_status = pgEnum('enum_posts_status', ['draft', 'published'])
export const enum__posts_v_version_status = pgEnum('enum__posts_v_version_status', [
  'draft',
  'published',
])
export const enum_subscription_plans_subscription_duration = pgEnum(
  'enum_subscription_plans_subscription_duration',
  ['day', 'week', 'month', 'year'],
)
export const enum_subscription_records_status = pgEnum('enum_subscription_records_status', [
  'active',
  'canceled',
  'past_due',
  'incomplete',
  'incomplete_expired',
  'trialing',
  'unpaid',
  'paused',
])

export const users = pgTable(
  'users',
  {
    id: serial('id').primaryKey(),
    author_name: varchar('author_name').notNull(),
    role: enum_users_role('role').notNull().default('contributer'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    email: varchar('email').notNull(),
    resetPasswordToken: varchar('reset_password_token'),
    resetPasswordExpiration: timestamp('reset_password_expiration', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    salt: varchar('salt'),
    hash: varchar('hash'),
    loginAttempts: numeric('login_attempts').default('0'),
    lockUntil: timestamp('lock_until', { mode: 'string', withTimezone: true, precision: 3 }),
  },
  (columns) => ({
    users_updated_at_idx: index('users_updated_at_idx').on(columns.updatedAt),
    users_email_idx: uniqueIndex('users_email_idx').on(columns.email),
  }),
)

export const media = pgTable(
  'media',
  {
    id: serial('id').primaryKey(),
    alt: varchar('alt').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    url: varchar('url'),
    thumbnailURL: varchar('thumbnail_u_r_l'),
    filename: varchar('filename'),
    mimeType: varchar('mime_type'),
    filesize: numeric('filesize'),
    width: numeric('width'),
    height: numeric('height'),
    focalX: numeric('focal_x'),
    focalY: numeric('focal_y'),
  },
  (columns) => ({
    media_updated_at_idx: index('media_updated_at_idx').on(columns.updatedAt),
    media_created_at_idx: index('media_created_at_idx').on(columns.createdAt),
    media_filename_idx: uniqueIndex('media_filename_idx').on(columns.filename),
  }),
)

export const public_users = pgTable(
  'public_users',
  {
    id: varchar('id').primaryKey(),
    firstName: varchar('first_name').notNull(),
    lastName: varchar('last_name').notNull(),
    freeCredits: numeric('free_credits').notNull().default('0'),
    paidCredits: numeric('paid_credits').notNull().default('0'),
    rolloverCredits: numeric('rollover_credits').notNull().default('0'),
    address: varchar('address'),
    country: varchar('country'),
    state: varchar('state'),
    city: varchar('city'),
    role: enum_public_users_role('role').notNull().default('free'),
    verified: boolean('verified').default(false),
    verificationToken: varchar('verification_token'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    email: varchar('email').notNull(),
    resetPasswordToken: varchar('reset_password_token'),
    resetPasswordExpiration: timestamp('reset_password_expiration', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    salt: varchar('salt'),
    hash: varchar('hash'),
    loginAttempts: numeric('login_attempts').default('0'),
    lockUntil: timestamp('lock_until', { mode: 'string', withTimezone: true, precision: 3 }),
  },
  (columns) => ({
    public_users_updated_at_idx: index('public_users_updated_at_idx').on(columns.updatedAt),
    public_users_email_idx: uniqueIndex('public_users_email_idx').on(columns.email),
  }),
)

export const posts = pgTable(
  'posts',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    image: integer('image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    summary: jsonb('summary'),
    richText: jsonb('rich_text'),
    readingTime: numeric('reading_time'),
    slug: varchar('slug'),
    author: integer('author_id').references(() => users.id, {
      onDelete: 'set null',
    }),
    publishDate: timestamp('publish_date', { mode: 'string', withTimezone: true, precision: 3 }),
    meta_title: varchar('meta_title'),
    meta_description: varchar('meta_description'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    _status: enum_posts_status('_status').default('draft'),
  },
  (columns) => ({
    posts_image_idx: index('posts_image_idx').on(columns.image),
    posts_author_idx: index('posts_author_idx').on(columns.author),
    posts_meta_meta_image_idx: index('posts_meta_meta_image_idx').on(columns.meta_image),
    posts_updated_at_idx: index('posts_updated_at_idx').on(columns.updatedAt),
    posts_created_at_idx: index('posts_created_at_idx').on(columns.createdAt),
    posts__status_idx: index('posts__status_idx').on(columns._status),
  }),
)

export const posts_rels = pgTable(
  'posts_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    categoriesID: integer('categories_id'),
    tagsID: integer('tags_id'),
  },
  (columns) => ({
    order: index('posts_rels_order_idx').on(columns.order),
    parentIdx: index('posts_rels_parent_idx').on(columns.parent),
    pathIdx: index('posts_rels_path_idx').on(columns.path),
    posts_rels_categories_id_idx: index('posts_rels_categories_id_idx').on(columns.categoriesID),
    posts_rels_tags_id_idx: index('posts_rels_tags_id_idx').on(columns.tagsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [posts.id],
      name: 'posts_rels_parent_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'posts_rels_categories_fk',
    }).onDelete('cascade'),
    tagsIdFk: foreignKey({
      columns: [columns['tagsID']],
      foreignColumns: [tags.id],
      name: 'posts_rels_tags_fk',
    }).onDelete('cascade'),
  }),
)

export const _posts_v = pgTable(
  '_posts_v',
  {
    id: serial('id').primaryKey(),
    parent: integer('parent_id').references(() => posts.id, {
      onDelete: 'set null',
    }),
    version_title: varchar('version_title'),
    version_image: integer('version_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_summary: jsonb('version_summary'),
    version_richText: jsonb('version_rich_text'),
    version_readingTime: numeric('version_reading_time'),
    version_slug: varchar('version_slug'),
    version_author: integer('version_author_id').references(() => users.id, {
      onDelete: 'set null',
    }),
    version_publishDate: timestamp('version_publish_date', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_meta_title: varchar('version_meta_title'),
    version_meta_description: varchar('version_meta_description'),
    version_meta_image: integer('version_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_updatedAt: timestamp('version_updated_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_createdAt: timestamp('version_created_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version__status: enum__posts_v_version_status('version__status').default('draft'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    latest: boolean('latest'),
  },
  (columns) => ({
    _posts_v_parent_idx: index('_posts_v_parent_idx').on(columns.parent),
    _posts_v_version_version_image_idx: index('_posts_v_version_version_image_idx').on(
      columns.version_image,
    ),
    _posts_v_version_version_author_idx: index('_posts_v_version_version_author_idx').on(
      columns.version_author,
    ),
    _posts_v_version_meta_version_meta_image_idx: index(
      '_posts_v_version_meta_version_meta_image_idx',
    ).on(columns.version_meta_image),
    _posts_v_version_version_updated_at_idx: index('_posts_v_version_version_updated_at_idx').on(
      columns.version_updatedAt,
    ),
    _posts_v_version_version_created_at_idx: index('_posts_v_version_version_created_at_idx').on(
      columns.version_createdAt,
    ),
    _posts_v_version_version__status_idx: index('_posts_v_version_version__status_idx').on(
      columns.version__status,
    ),
    _posts_v_created_at_idx: index('_posts_v_created_at_idx').on(columns.createdAt),
    _posts_v_updated_at_idx: index('_posts_v_updated_at_idx').on(columns.updatedAt),
    _posts_v_latest_idx: index('_posts_v_latest_idx').on(columns.latest),
  }),
)

export const _posts_v_rels = pgTable(
  '_posts_v_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    categoriesID: integer('categories_id'),
    tagsID: integer('tags_id'),
  },
  (columns) => ({
    order: index('_posts_v_rels_order_idx').on(columns.order),
    parentIdx: index('_posts_v_rels_parent_idx').on(columns.parent),
    pathIdx: index('_posts_v_rels_path_idx').on(columns.path),
    _posts_v_rels_categories_id_idx: index('_posts_v_rels_categories_id_idx').on(
      columns.categoriesID,
    ),
    _posts_v_rels_tags_id_idx: index('_posts_v_rels_tags_id_idx').on(columns.tagsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [_posts_v.id],
      name: '_posts_v_rels_parent_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: '_posts_v_rels_categories_fk',
    }).onDelete('cascade'),
    tagsIdFk: foreignKey({
      columns: [columns['tagsID']],
      foreignColumns: [tags.id],
      name: '_posts_v_rels_tags_fk',
    }).onDelete('cascade'),
  }),
)

export const categories = pgTable(
  'categories',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    icon: varchar('icon'),
    archived: boolean('archived').default(false),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    categories_updated_at_idx: index('categories_updated_at_idx').on(columns.updatedAt),
    categories_created_at_idx: index('categories_created_at_idx').on(columns.createdAt),
  }),
)

export const tags = pgTable(
  'tags',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    archived: boolean('archived').default(false),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    tags_updated_at_idx: index('tags_updated_at_idx').on(columns.updatedAt),
    tags_created_at_idx: index('tags_created_at_idx').on(columns.createdAt),
  }),
)

export const subscription_plans = pgTable(
  'subscription_plans',
  {
    id: varchar('id').primaryKey(),
    name: varchar('name').notNull(),
    description: varchar('description'),
    stripePriceId: varchar('stripe_price_id').notNull(),
    price: numeric('price').notNull(),
    subscription_duration:
      enum_subscription_plans_subscription_duration('subscription_duration').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    subscription_plans_stripe_price_id_idx: uniqueIndex(
      'subscription_plans_stripe_price_id_idx',
    ).on(columns.stripePriceId),
    subscription_plans_updated_at_idx: index('subscription_plans_updated_at_idx').on(
      columns.updatedAt,
    ),
    subscription_plans_created_at_idx: index('subscription_plans_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const subscription_records = pgTable(
  'subscription_records',
  {
    id: serial('id').primaryKey(),
    subscriptionId: varchar('subscription_id'),
    user: varchar('user_id').references(() => public_users.id, {
      onDelete: 'set null',
    }),
    subscriptionPlan: varchar('subscription_plan_id').references(() => subscription_plans.id, {
      onDelete: 'set null',
    }),
    customerId: varchar('customer_id'),
    status: enum_subscription_records_status('status').notNull().default('incomplete'),
    currentPeriodStart: timestamp('current_period_start', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    currentPeriodEnd: timestamp('current_period_end', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    cancelAt: timestamp('cancel_at', { mode: 'string', withTimezone: true, precision: 3 }),
    canceledAt: timestamp('canceled_at', { mode: 'string', withTimezone: true, precision: 3 }),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    comments: varchar('comments'),
    customTitle: varchar('custom_title'),
  },
  (columns) => ({
    subscription_records_user_idx: index('subscription_records_user_idx').on(columns.user),
    subscription_records_subscription_plan_idx: index(
      'subscription_records_subscription_plan_idx',
    ).on(columns.subscriptionPlan),
  }),
)

export const generated_images = pgTable(
  'generated_images',
  {
    id: varchar('id').primaryKey(),
    taskType: varchar('task_type').notNull(),
    prompt: varchar('prompt'),
    status: varchar('status').default('generating'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdBy: varchar('created_by_id')
      .notNull()
      .references(() => public_users.id, {
        onDelete: 'set null',
      }),
    url: varchar('url'),
    thumbnailURL: varchar('thumbnail_u_r_l'),
    filename: varchar('filename'),
    mimeType: varchar('mime_type'),
    filesize: numeric('filesize'),
    width: numeric('width'),
    height: numeric('height'),
    focalX: numeric('focal_x'),
    focalY: numeric('focal_y'),
  },
  (columns) => ({
    generated_images_created_by_idx: index('generated_images_created_by_idx').on(columns.createdBy),
    generated_images_filename_idx: uniqueIndex('generated_images_filename_idx').on(
      columns.filename,
    ),
  }),
)

export const payload_locked_documents = pgTable(
  'payload_locked_documents',
  {
    id: serial('id').primaryKey(),
    globalSlug: varchar('global_slug'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_locked_documents_global_slug_idx: index('payload_locked_documents_global_slug_idx').on(
      columns.globalSlug,
    ),
    payload_locked_documents_updated_at_idx: index('payload_locked_documents_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_locked_documents_created_at_idx: index('payload_locked_documents_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_locked_documents_rels = pgTable(
  'payload_locked_documents_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    usersID: integer('users_id'),
    mediaID: integer('media_id'),
    'public-usersID': varchar('public_users_id'),
    postsID: integer('posts_id'),
    categoriesID: integer('categories_id'),
    tagsID: integer('tags_id'),
    'subscription-plansID': varchar('subscription_plans_id'),
    'subscription-recordsID': integer('subscription_records_id'),
    'generated-imagesID': varchar('generated_images_id'),
  },
  (columns) => ({
    order: index('payload_locked_documents_rels_order_idx').on(columns.order),
    parentIdx: index('payload_locked_documents_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_locked_documents_rels_path_idx').on(columns.path),
    payload_locked_documents_rels_users_id_idx: index(
      'payload_locked_documents_rels_users_id_idx',
    ).on(columns.usersID),
    payload_locked_documents_rels_media_id_idx: index(
      'payload_locked_documents_rels_media_id_idx',
    ).on(columns.mediaID),
    payload_locked_documents_rels_public_users_id_idx: index(
      'payload_locked_documents_rels_public_users_id_idx',
    ).on(columns['public-usersID']),
    payload_locked_documents_rels_posts_id_idx: index(
      'payload_locked_documents_rels_posts_id_idx',
    ).on(columns.postsID),
    payload_locked_documents_rels_categories_id_idx: index(
      'payload_locked_documents_rels_categories_id_idx',
    ).on(columns.categoriesID),
    payload_locked_documents_rels_tags_id_idx: index(
      'payload_locked_documents_rels_tags_id_idx',
    ).on(columns.tagsID),
    payload_locked_documents_rels_subscription_plans_id_idx: index(
      'payload_locked_documents_rels_subscription_plans_id_idx',
    ).on(columns['subscription-plansID']),
    payload_locked_documents_rels_subscription_records_id_idx: index(
      'payload_locked_documents_rels_subscription_records_id_idx',
    ).on(columns['subscription-recordsID']),
    payload_locked_documents_rels_generated_images_id_idx: index(
      'payload_locked_documents_rels_generated_images_id_idx',
    ).on(columns['generated-imagesID']),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_locked_documents.id],
      name: 'payload_locked_documents_rels_parent_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_locked_documents_rels_users_fk',
    }).onDelete('cascade'),
    mediaIdFk: foreignKey({
      columns: [columns['mediaID']],
      foreignColumns: [media.id],
      name: 'payload_locked_documents_rels_media_fk',
    }).onDelete('cascade'),
    'public-usersIdFk': foreignKey({
      columns: [columns['public-usersID']],
      foreignColumns: [public_users.id],
      name: 'payload_locked_documents_rels_public_users_fk',
    }).onDelete('cascade'),
    postsIdFk: foreignKey({
      columns: [columns['postsID']],
      foreignColumns: [posts.id],
      name: 'payload_locked_documents_rels_posts_fk',
    }).onDelete('cascade'),
    categoriesIdFk: foreignKey({
      columns: [columns['categoriesID']],
      foreignColumns: [categories.id],
      name: 'payload_locked_documents_rels_categories_fk',
    }).onDelete('cascade'),
    tagsIdFk: foreignKey({
      columns: [columns['tagsID']],
      foreignColumns: [tags.id],
      name: 'payload_locked_documents_rels_tags_fk',
    }).onDelete('cascade'),
    'subscription-plansIdFk': foreignKey({
      columns: [columns['subscription-plansID']],
      foreignColumns: [subscription_plans.id],
      name: 'payload_locked_documents_rels_subscription_plans_fk',
    }).onDelete('cascade'),
    'subscription-recordsIdFk': foreignKey({
      columns: [columns['subscription-recordsID']],
      foreignColumns: [subscription_records.id],
      name: 'payload_locked_documents_rels_subscription_records_fk',
    }).onDelete('cascade'),
    'generated-imagesIdFk': foreignKey({
      columns: [columns['generated-imagesID']],
      foreignColumns: [generated_images.id],
      name: 'payload_locked_documents_rels_generated_images_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_preferences = pgTable(
  'payload_preferences',
  {
    id: serial('id').primaryKey(),
    key: varchar('key'),
    value: jsonb('value'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_preferences_key_idx: index('payload_preferences_key_idx').on(columns.key),
    payload_preferences_updated_at_idx: index('payload_preferences_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_preferences_created_at_idx: index('payload_preferences_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_preferences_rels = pgTable(
  'payload_preferences_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    usersID: integer('users_id'),
    'public-usersID': varchar('public_users_id'),
  },
  (columns) => ({
    order: index('payload_preferences_rels_order_idx').on(columns.order),
    parentIdx: index('payload_preferences_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_preferences_rels_path_idx').on(columns.path),
    payload_preferences_rels_users_id_idx: index('payload_preferences_rels_users_id_idx').on(
      columns.usersID,
    ),
    payload_preferences_rels_public_users_id_idx: index(
      'payload_preferences_rels_public_users_id_idx',
    ).on(columns['public-usersID']),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_preferences.id],
      name: 'payload_preferences_rels_parent_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_preferences_rels_users_fk',
    }).onDelete('cascade'),
    'public-usersIdFk': foreignKey({
      columns: [columns['public-usersID']],
      foreignColumns: [public_users.id],
      name: 'payload_preferences_rels_public_users_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_migrations = pgTable(
  'payload_migrations',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    batch: numeric('batch'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_migrations_updated_at_idx: index('payload_migrations_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_migrations_created_at_idx: index('payload_migrations_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const relations_users = relations(users, () => ({}))
export const relations_media = relations(media, () => ({}))
export const relations_public_users = relations(public_users, () => ({}))
export const relations_posts_rels = relations(posts_rels, ({ one }) => ({
  parent: one(posts, {
    fields: [posts_rels.parent],
    references: [posts.id],
    relationName: '_rels',
  }),
  categoriesID: one(categories, {
    fields: [posts_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  tagsID: one(tags, {
    fields: [posts_rels.tagsID],
    references: [tags.id],
    relationName: 'tags',
  }),
}))
export const relations_posts = relations(posts, ({ one, many }) => ({
  image: one(media, {
    fields: [posts.image],
    references: [media.id],
    relationName: 'image',
  }),
  author: one(users, {
    fields: [posts.author],
    references: [users.id],
    relationName: 'author',
  }),
  meta_image: one(media, {
    fields: [posts.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  _rels: many(posts_rels, {
    relationName: '_rels',
  }),
}))
export const relations__posts_v_rels = relations(_posts_v_rels, ({ one }) => ({
  parent: one(_posts_v, {
    fields: [_posts_v_rels.parent],
    references: [_posts_v.id],
    relationName: '_rels',
  }),
  categoriesID: one(categories, {
    fields: [_posts_v_rels.categoriesID],
    references: [categories.id],
    relationName: 'categories',
  }),
  tagsID: one(tags, {
    fields: [_posts_v_rels.tagsID],
    references: [tags.id],
    relationName: 'tags',
  }),
}))
export const relations__posts_v = relations(_posts_v, ({ one, many }) => ({
  parent: one(posts, {
    fields: [_posts_v.parent],
    references: [posts.id],
    relationName: 'parent',
  }),
  version_image: one(media, {
    fields: [_posts_v.version_image],
    references: [media.id],
    relationName: 'version_image',
  }),
  version_author: one(users, {
    fields: [_posts_v.version_author],
    references: [users.id],
    relationName: 'version_author',
  }),
  version_meta_image: one(media, {
    fields: [_posts_v.version_meta_image],
    references: [media.id],
    relationName: 'version_meta_image',
  }),
  _rels: many(_posts_v_rels, {
    relationName: '_rels',
  }),
}))
export const relations_categories = relations(categories, () => ({}))
export const relations_tags = relations(tags, () => ({}))
export const relations_subscription_plans = relations(subscription_plans, () => ({}))
export const relations_subscription_records = relations(subscription_records, ({ one }) => ({
  user: one(public_users, {
    fields: [subscription_records.user],
    references: [public_users.id],
    relationName: 'user',
  }),
  subscriptionPlan: one(subscription_plans, {
    fields: [subscription_records.subscriptionPlan],
    references: [subscription_plans.id],
    relationName: 'subscriptionPlan',
  }),
}))
export const relations_generated_images = relations(generated_images, ({ one }) => ({
  createdBy: one(public_users, {
    fields: [generated_images.createdBy],
    references: [public_users.id],
    relationName: 'createdBy',
  }),
}))
export const relations_payload_locked_documents_rels = relations(
  payload_locked_documents_rels,
  ({ one }) => ({
    parent: one(payload_locked_documents, {
      fields: [payload_locked_documents_rels.parent],
      references: [payload_locked_documents.id],
      relationName: '_rels',
    }),
    usersID: one(users, {
      fields: [payload_locked_documents_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
    mediaID: one(media, {
      fields: [payload_locked_documents_rels.mediaID],
      references: [media.id],
      relationName: 'media',
    }),
    'public-usersID': one(public_users, {
      fields: [payload_locked_documents_rels['public-usersID']],
      references: [public_users.id],
      relationName: 'public-users',
    }),
    postsID: one(posts, {
      fields: [payload_locked_documents_rels.postsID],
      references: [posts.id],
      relationName: 'posts',
    }),
    categoriesID: one(categories, {
      fields: [payload_locked_documents_rels.categoriesID],
      references: [categories.id],
      relationName: 'categories',
    }),
    tagsID: one(tags, {
      fields: [payload_locked_documents_rels.tagsID],
      references: [tags.id],
      relationName: 'tags',
    }),
    'subscription-plansID': one(subscription_plans, {
      fields: [payload_locked_documents_rels['subscription-plansID']],
      references: [subscription_plans.id],
      relationName: 'subscription-plans',
    }),
    'subscription-recordsID': one(subscription_records, {
      fields: [payload_locked_documents_rels['subscription-recordsID']],
      references: [subscription_records.id],
      relationName: 'subscription-records',
    }),
    'generated-imagesID': one(generated_images, {
      fields: [payload_locked_documents_rels['generated-imagesID']],
      references: [generated_images.id],
      relationName: 'generated-images',
    }),
  }),
)
export const relations_payload_locked_documents = relations(
  payload_locked_documents,
  ({ many }) => ({
    _rels: many(payload_locked_documents_rels, {
      relationName: '_rels',
    }),
  }),
)
export const relations_payload_preferences_rels = relations(
  payload_preferences_rels,
  ({ one }) => ({
    parent: one(payload_preferences, {
      fields: [payload_preferences_rels.parent],
      references: [payload_preferences.id],
      relationName: '_rels',
    }),
    usersID: one(users, {
      fields: [payload_preferences_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
    'public-usersID': one(public_users, {
      fields: [payload_preferences_rels['public-usersID']],
      references: [public_users.id],
      relationName: 'public-users',
    }),
  }),
)
export const relations_payload_preferences = relations(payload_preferences, ({ many }) => ({
  _rels: many(payload_preferences_rels, {
    relationName: '_rels',
  }),
}))
export const relations_payload_migrations = relations(payload_migrations, () => ({}))

type DatabaseSchema = {
  enum_users_role: typeof enum_users_role
  enum_public_users_role: typeof enum_public_users_role
  enum_posts_status: typeof enum_posts_status
  enum__posts_v_version_status: typeof enum__posts_v_version_status
  enum_subscription_plans_subscription_duration: typeof enum_subscription_plans_subscription_duration
  enum_subscription_records_status: typeof enum_subscription_records_status
  users: typeof users
  media: typeof media
  public_users: typeof public_users
  posts: typeof posts
  posts_rels: typeof posts_rels
  _posts_v: typeof _posts_v
  _posts_v_rels: typeof _posts_v_rels
  categories: typeof categories
  tags: typeof tags
  subscription_plans: typeof subscription_plans
  subscription_records: typeof subscription_records
  generated_images: typeof generated_images
  payload_locked_documents: typeof payload_locked_documents
  payload_locked_documents_rels: typeof payload_locked_documents_rels
  payload_preferences: typeof payload_preferences
  payload_preferences_rels: typeof payload_preferences_rels
  payload_migrations: typeof payload_migrations
  relations_users: typeof relations_users
  relations_media: typeof relations_media
  relations_public_users: typeof relations_public_users
  relations_posts_rels: typeof relations_posts_rels
  relations_posts: typeof relations_posts
  relations__posts_v_rels: typeof relations__posts_v_rels
  relations__posts_v: typeof relations__posts_v
  relations_categories: typeof relations_categories
  relations_tags: typeof relations_tags
  relations_subscription_plans: typeof relations_subscription_plans
  relations_subscription_records: typeof relations_subscription_records
  relations_generated_images: typeof relations_generated_images
  relations_payload_locked_documents_rels: typeof relations_payload_locked_documents_rels
  relations_payload_locked_documents: typeof relations_payload_locked_documents
  relations_payload_preferences_rels: typeof relations_payload_preferences_rels
  relations_payload_preferences: typeof relations_payload_preferences
  relations_payload_migrations: typeof relations_payload_migrations
}

declare module '@payloadcms/db-postgres/types' {
  export interface GeneratedDatabaseSchema {
    schema: DatabaseSchema
  }
}
