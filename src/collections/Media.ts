import type { CollectionConfig } from 'payload'
import { Media as MediaType } from '@/payload-types'

export const Media: CollectionConfig = {
  slug: 'media',
  admin: {
    group: 'Content',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
    },
  ],
  upload: {
    staticDir: 'assets/media/uploads',
  },
}

export function isMedia(image: number | MediaType | null | undefined): image is MediaType {
  return typeof image === 'object' && image !== null && 'url' in image
}
