import { CollectionConfig } from 'payload'
import { User } from '@/payload-types'
import isFieldAdminOnly from '@/payloadCMS/roleaccess/isFieldAdminOnly'
import isAdminOnly from '../payloadCMS/roleaccess/isAdminOnly'

export function isUser(user: number | User | null | undefined): user is User {
  return typeof user === 'object' && user !== null
}

const Users: CollectionConfig = {
  slug: 'users',
  auth: true,
  admin: {
    useAsTitle: 'email',
    group: 'Admin Users',
  },
  access: {
    read: isAdminOnly,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  fields: [
    {
      name: 'author_name',
      label: 'Author Name',
      type: 'text',
      required: true,
    },
    {
      name: 'role',
      label: 'User Role',
      type: 'select',
      options: [
        { label: 'Admin', value: 'admin' },
        { label: 'Editor', value: 'editor' },
        { label: 'Contributer', value: 'contributer' },
      ],
      required: true,
      defaultValue: 'contributer',
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'createdAt',
      type: 'date',
      admin: {
        hidden: true,
      },
      hooks: {
        beforeChange: [
          () => {
            return new Date()
          },
        ],
      },
    },
  ],
}

export default Users
