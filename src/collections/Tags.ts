import type { CollectionConfig } from 'payload/'
import { Tag as TagTypeCheck } from '@/payload-types'

export function isTags(
  category: (number | TagTypeCheck)[] | null | undefined,
): category is TagTypeCheck[] {
  return typeof category === 'object' && category !== null
}

export interface Type {
  id: string
  name: string
  archived: string
  createdAt: string
  updatedAt: string
}

const Tags: CollectionConfig = {
  slug: 'tags',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'id', 'archived'],
    group: 'Content',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      localized: true,
    },
    {
      name: 'archived',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Archiving filters it from being an option in the posts collection',
      },
    },
    {
      name: 'summary',
      // ui fields do not impact the database or api, and serve as placeholders for custom components in the admin panel
      type: 'ui',
      admin: {
        position: 'sidebar',
        components: {
          // this custom component will fetch the posts count for how many posts have this category
          Field: '../payloadCMS/components/TagSummary',
        },
      },
    },
  ],
}

export default Tags
