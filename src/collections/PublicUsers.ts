import { CollectionConfig } from 'payload'
import { v4 as uuidv4 } from 'uuid'
import isAdminOnly from '../payloadCMS/roleaccess/isAdminOnly'
import isAdminOrSelf from '../payloadCMS/roleaccess/isAdminOrSelf'

const PublicUsers: CollectionConfig = {
  slug: 'public-users',
  auth: {
    tokenExpiration: Number(process.env.TOKEN_EXPIRATION_TIME) || 3600,
    cookies: {
      sameSite: 'Lax',
      secure: process.env.NODE_ENV === 'production',
    },
  },
  admin: {
    useAsTitle: 'email',
    group: 'App Users',
  },
  access: {
    read: isAdminOrSelf,
    create: isAdminOnly,
    update: isAdminOnly,
  },
  fields: [
    {
      name: 'id',
      label: 'Id',
      type: 'text',
      required: true,
      saveToJWT: true,
      hooks: {
        beforeChange: [
          () => {
            // You can transform the value before saving
            return uuidv4() // Ensures whole numbers
          },
        ],
      },
    },
    {
      name: 'email',
      label: 'Email',
      type: 'text',
      required: true,
      unique: true,
      saveToJWT: true,
    },
    {
      name: 'firstName',
      label: 'First name',
      type: 'text',
      required: true,
      saveToJWT: true,
    },
    {
      name: 'lastName',
      label: 'Last Name',
      type: 'text',
      required: true,
      saveToJWT: true,
    },
    {
      name: 'freeCredits',
      label: 'Free Credits',
      type: 'number',
      required: true,
      defaultValue: 0,
      saveToJWT: true,
    },
    {
      name: 'paidCredits',
      label: 'Paid Credits',
      type: 'number',
      required: true,
      defaultValue: 0,
      saveToJWT: true,
    },
    {
      name: 'rolloverCredits',
      label: 'Rollover Credits',
      type: 'number',
      required: true,
      defaultValue: 0,
      saveToJWT: true,
    },
    {
      name: 'address',
      label: 'Billing Address',
      type: 'text',
    },
    {
      name: 'country',
      label: 'Country',
      type: 'text',
    },
    {
      name: 'state',
      label: 'State',
      type: 'text',
    },
    {
      name: 'city',
      label: 'City',
      type: 'text',
    },
    {
      name: 'fingerprintHash',
      label: 'Fingerprint Hash',
      type: 'text',
    },
    {
      name: 'ipAddress',
      label: 'IP address',
      type: 'text',
    },
    {
      name: 'role',
      label: 'User Role',
      type: 'select',
      options: [
        { label: 'Admin', value: 'admin' },
        { label: 'Editor', value: 'editor' },
        { label: 'Active Subscriber', value: 'subscriber' },
        { label: 'Free', value: 'free' },
      ],
      required: true,
      defaultValue: 'free',
      saveToJWT: true,
    },
    {
      name: 'subscription',
      label: 'Subscription Record',
      type: 'relationship',
      relationTo: 'subscription-records',
      hasMany: false,
      admin: {
        description: 'Not subscription plan, but subscription record.',
      },
    },
    {
      name: 'verified',
      label: 'Verified',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'verificationToken',
      type: 'text',
      hidden: true,
    },
    {
      name: 'createdAt',
      type: 'date',
      defaultValue: () => new Date(),
    },
    {
      name: 'isSSO',
      type: 'checkbox',
      defaultValue: false,
      required: true,
    },
    {
      name: 'lastOtpSent',
      type: 'date',
      hidden: true,
    },
  ],
}

export default PublicUsers
