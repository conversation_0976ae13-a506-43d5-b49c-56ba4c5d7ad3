import { CollectionConfig } from 'payload'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'
import isAdminOrSelf from '@/payloadCMS/roleaccess/isAdminOrSelf'

const Features: CollectionConfig = {
  slug: 'features',
  admin: {
    useAsTitle: 'feature',
    group: 'Products',
  },
  access: {
    read: isAdminOrSelf,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  fields: [
    {
      name: 'feature',
      label: 'Feature Name',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'creditCost',
      label: 'Credit Cost',
      type: 'number',
      required: true,
    },
  ],
}

export default Features
