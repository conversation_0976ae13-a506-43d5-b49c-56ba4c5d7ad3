import { FieldHook } from 'payload'
import { PublicUser } from '@/payload-types'

export const populateAuthor: FieldHook = async ({ req: { user }, value, operation }) => {
  // using this hook only the original creator of the document can be the author
  const isUserWithAuthorName = (u: any): u is PublicUser & { author_name: string } =>
    'author_name' in u

  if (operation === 'create') {
    if (isUserWithAuthorName(user)) {
      return user.author_name
    }
    throw new Error('User does not have an author_name property')
  }

  // when the operation is "update", return the original value so as not to change it
  return value
}
