import { CollectionConfig, User } from 'payload'
import { ContentType } from '@/app/(app)/_component/BlockRender/components/Content'
import { DownloadLockType } from '@/app/(app)/_component/BlockRender/components/DownloadLock'
import { YouTubeEmbedType } from '@/app/(app)/_component/BlockRender/components/YoutubeEmbed'
import { PublicUser } from '@/payload-types'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'
import isFieldAdminOnly from '@/payloadCMS/roleaccess/isFieldAdminOnly'
import calculateReadingTimeHook from '@/utilities/payload/calculateReadingTimeLayout'
import { Content } from './blocks/Content'
import { DownloadLock } from './blocks/DownloadLock'
import { YouTubeEmbed } from './blocks/YouTubeEmbed'
import formatSlug from '../../utilities/payload/formatSlug'

export type Layout = ContentType | YouTubeEmbedType | DownloadLockType

const Posts: CollectionConfig = {
  slug: 'posts',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'category', 'publishDate', 'tags', 'status', 'readingTime'],
    group: 'Content',
  },
  access: {
    read: isAdminOnly,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  versions: {
    maxPerDoc: 5,
    drafts: true,
  },
  fields: [
    {
      name: 'title',
      label: 'Title',
      type: 'text',
      required: true,
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      // limit the options using the below query which uses the "archive" field set in the categories collection
      filterOptions: {
        archived: { equals: false },
      },
      // allow selection of one or more categories
      hasMany: true,
    },
    {
      name: 'tags',
      type: 'relationship',
      relationTo: 'tags',
      // limit the options using the below query which uses the "archive" field set in the categories collection
      filterOptions: {
        archived: { equals: false },
      },
      // allow selection of one or more categories
      hasMany: true,
    },
    {
      name: 'image',
      label: 'Featured Image',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'summary',
      type: 'richText',
      label: 'Summary (keep it short, tldr style)',
    },
    {
      name: 'layout',
      label: 'Page Layout',
      type: 'blocks',
      minRows: 1,
      // the blocks are reusable objects that will be added in array to the document, these are especially useful for structuring content purpose built for frontend componentry
      blocks: [Content, YouTubeEmbed, DownloadLock],
    },
    {
      name: 'readingTime',
      type: 'number',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeChange: [calculateReadingTimeHook('layout')],
      },
    },
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [formatSlug('title')],
      },
    },
    {
      name: 'author',
      type: 'relationship',
      relationTo: 'users',
      defaultValue: ({ user }: { user: PublicUser | User }) => user.id,
      admin: {
        position: 'sidebar',
      },
      access: {
        update: isFieldAdminOnly,
      },
    },
    {
      name: 'publishDate',
      type: 'date',
      admin: {
        position: 'sidebar',
        description: 'Posts will not be public until this date',
      },
      defaultValue: () => new Date(),
    },
  ],
}

export default Posts
