import { CollectionConfig } from 'payload'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'
import isAdminOrSelf from '@/payloadCMS/roleaccess/isAdminOrSelf'

const Scopes: CollectionConfig = {
  slug: 'scopes',
  admin: {
    useAsTitle: 'scope',
    group: 'Products',
  },
  access: {
    read: isAdminOrSelf,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  fields: [
    {
      name: 'scope',
      label: 'Scope Name',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'features',
      label: 'Features Allocated',
      type: 'relationship',
      relationTo: 'features',
      hasMany: true,
      admin: {
        description: 'Features allocated to a scope',
      },
    },
  ],
}

export default Scopes
