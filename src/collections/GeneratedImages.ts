import type { CollectionConfig } from 'payload'
import { GeneratedImageStatus } from '../types/GeneratedImageStatus'

const GeneratedImages: CollectionConfig = {
  slug: 'generated-images',
  admin: {
    group: 'App Users',
  },
  access: {
    read: ({ req: { user } }) => {
      return true
    },
  },
  upload: {
    mimeTypes: ['image/*'],
    filesRequiredOnCreate: false,
    disableLocalStorage: true,
  },
  fields: [
    {
      name: 'id',
      label: 'Id',
      type: 'text',
      required: true,
    },
    {
      name: 'taskType',
      type: 'text',
      required: true,
    },
    {
      name: 'prompt',
      type: 'text',
    },
    {
      name: 'status',
      type: 'text',
      defaultValue: GeneratedImageStatus.GENERATING,
    },
    {
      name: 'createdAt',
      type: 'date',
      defaultValue: () => new Date(),
    },
    {
      name: 'updatedAt',
      type: 'date',
      hooks: {
        beforeChange: [() => new Date()],
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'public-users',
      hasMany: false,
      required: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          if (req.user) {
            data.createdBy = req.user.id
            return data
          }
        }
        return data
      },
    ],
  },
}

export default GeneratedImages
