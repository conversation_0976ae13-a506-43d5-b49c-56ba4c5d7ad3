import { Inter, Fredoka, Nunito, Quicksand, Outfit } from 'next/font/google'
import Script from 'next/script'
import React from 'react'

import '@/app/(app)/globals.css'
import { Toaster } from '@/app/(app)/_component/Sonner'
import QueryClientProviderWrapper from './_context/QueryClientWrapper'

export const metadata = {
  title: 'ColorAria',
  description: 'Line Art Generator',
}

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})

const fredoka = Fredoka({
  subsets: ['latin'],
  display: 'swap',
})

const nunito = Nunito({
  subsets: ['latin'],
  display: 'swap',
})

const quicksand = Quicksand({
  subsets: ['latin'],
  display: 'swap',
})

const outfit = Outfit({
  subsets: ['latin'],
  display: 'swap',
})

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const recaptchaScriptSource = `https://www.google.com/recaptcha/enterprise.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_KEY_ID}`

  return (
    <html lang="en" className="white">
      <head>
        <Script src={recaptchaScriptSource}></Script>
      </head>
      <body>
        <QueryClientProviderWrapper>
          <div className={outfit.className}>
            {/* <header className={classes.header}>
          <Link
            href="https://payloadcms.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <picture>
              <source
                media="(prefers-color-scheme: dark)"
                srcSet="https://raw.githubusercontent.com/payloadcms/payload/main/packages/payload/src/admin/assets/images/payload-logo-light.svg"
              />
              <img
                className={classes.logo}
                alt="Payload Logo"
                src="https://raw.githubusercontent.com/payloadcms/payload/main/packages/payload/src/admin/assets/images/payload-logo-dark.svg"
              />
            </picture>
          </Link>
        </header> */}
            {children}
            <Toaster />
          </div>
        </QueryClientProviderWrapper>
      </body>
    </html>
  )
}
