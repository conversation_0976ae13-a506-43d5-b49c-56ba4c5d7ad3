import { create } from 'zustand'

type CreditUsage = Record<string, number>

type CreditStore = {
  usage: CreditUsage
  register: (featureKey: string, cost: number) => void
  deregister: (featureKey: string) => void
  reset: () => void
  getTotal: () => number
  getCost: (key: string) => number
}

export const useCreditStore = create<CreditStore>((set, get) => ({
  usage: {},
  register: (key, cost) =>
    set((state) => ({
      usage: {
        ...state.usage,
        [key]: cost,
      },
    })),
  deregister: (key) =>
    set((state) => {
      const { [key]: _, ...rest } = state.usage
      return { usage: rest }
    }),
  reset: () => set({ usage: {} }),
  getTotal: () => {
    const usage = get().usage
    return Object.values(usage).reduce((sum, v) => sum + v, 0)
  },
  getCost: (key) => get().usage[key] ?? 0,
}))

export function useCreditCharge(featureKey: string, cost: number) {
  const register = useCreditStore((s) => s.register)
  return () => register(featureKey, cost)
}

export function useCreditDeregisterCharge(featureKey: string) {
  const deregister = useCreditStore((s) => s.deregister)
  return () => deregister(featureKey)
}

export function useCreditChargeWithDependency(
  featureKey: string,
  baseCost: number,
  dependsOn: string,
  multiplier = 1,
) {
  const register = useCreditStore((s) => s.register)
  const getCost = useCreditStore((s) => s.getCost)

  return () => {
    const dependentCost = dependsOn ? getCost(dependsOn) : (baseCost ?? 0)
    const finalCost = dependentCost * multiplier
    register(featureKey, finalCost)
  }
}
