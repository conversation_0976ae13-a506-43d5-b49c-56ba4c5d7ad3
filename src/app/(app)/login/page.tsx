import { Metadata } from 'next'
import LoginComponent from './component'

export const metadata: Metadata = {
  title: 'Login to ColorAria',
  description: 'Login to ColorAria.',
}

interface LoginPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function LoginPage({ searchParams }: LoginPageProps) {
  const { error, message } = await searchParams

  return <LoginComponent error={error} message={message} />
}

export default LoginPage
