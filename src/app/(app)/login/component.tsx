'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { Thumbmark } from '@thumbmarkjs/thumbmarkjs'
import { LogIn, Terminal } from 'lucide-react'
import NextImage from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader, PropagateLoader } from 'react-spinners'
import { z } from 'zod'
import { Alert, AlertDescription, AlertTitle } from '@/app/(app)/_component/Alert'
import { IconSize } from '@/utilities/local/css'
import { User, UserWithAuth } from '../_backend/common/dto/user/UserDTO'
import { Button } from '../_component/Button'
import Divider from '../_component/Divider'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../_component/Form'
import { Input } from '../_component/Input'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import { loginUser } from '../_localApi/users'
import { ApiResponse } from '../_localApi/util'
import { useAuthState } from '../_state/authState'

//https://v3.magicui.design/docs/components/shine-border

declare global {
  interface Window {
    grecaptcha: any
  }
}

interface AlertMessage {
  title: string
  variant: 'default' | 'positive' | 'negative' | 'destructive' | null | undefined
  message?: string
}

interface LoginProps {
  error?: string | string[]
  message?: string | string[]
}

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

function LoginComponent(props: LoginProps) {
  const { error, message } = props
  let messageStr = ''
  if (message instanceof Array) {
    messageStr = message[0]
  } else {
    messageStr = message ?? ''
  }
  const router = useRouter()
  const [showAlert, setShowAlert] = useState(error ? true : false)
  const [bLoading, setBLoading] = useState(false)
  const [loadFingerprint, setLoadFingerprint] = useState(true)
  const [fingerprintHash, setFingerprintHash] = useState('')
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({
    title: "Oops, we've faced an error!",
    message: messageStr,
    variant: 'negative',
  })
  const formSchema = z.object({
    email: z.string().email({
      message: 'This is not a valid email!',
    }),
    password: z.string().min(1, {
      message: 'Password is required!',
    }),
  })
  useEffect(() => {
    const tm = new Thumbmark()
    tm.get()
      .then((result) => {
        setFingerprintHash(result.thumbmark)
      })
      .finally(() => {
        setLoadFingerprint(false)
      })
  }, [])
  const user = useAuthState((state) => state.user)
  const setUser = useAuthState((state) => state.setUser)
  const setIsLoggedIn = useAuthState((state) => state.setIsLoggedIn)
  const setHasEverLoggedIn = useAuthState((state) => state.setHasEverLoggedIn)
  const setExpiry = useAuthState((state) => state.setExpiry)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  useEffect(() => {
    if (user) {
      router.push('/dashboard')
    }
  }, [user])

  const loginUserMutation = useMutation({
    mutationFn: (data: { email: string; password: string; recaptchaToken: string }) => {
      return loginUser(data.email, data.password, data.recaptchaToken)
    },
    onSuccess: (data: ApiResponse<UserWithAuth>) => {
      if (data.success) {
        setUser({
          id: data.data.user.id,
          firstName: data.data.user.firstName,
          lastName: data.data.user.lastName,
          email: data.data.user.email,
          role: data.data.user.role,
          freeCredits: data.data.user.freeCredits,
          paidCredits: data.data.user.paidCredits,
          rolloverCredits: data.data.user.rolloverCredits,
        })
        setExpiry(data.data.exp)
        setIsLoggedIn(true)
        setHasEverLoggedIn(true)
        setShowAlert(true)
        setAlertMessage({
          title: 'Success!',
          variant: 'positive',
          message: data.message.message,
        })
      } else {
        setShowAlert(true)
        setAlertMessage({
          title: 'Something went wrong!',
          variant: 'negative',
          message: data.message.message,
        })
      }
    },
    onMutate: () => {
      setBLoading(true)
    },
    onSettled: () => {
      setBLoading(false)
    },
    onError: (error) => {
      setShowAlert(true)
      setAlertMessage({
        title: 'Something went wrong!',
        variant: 'negative',
        message: error.message,
      })
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (typeof window.grecaptcha !== 'undefined') {
      const token = await window.grecaptcha.enterprise.execute(
        process.env.NEXT_PUBLIC_RECAPTCHA_KEY_ID,
        { action: 'LOGIN' },
      )
      loginUserMutation.mutateAsync({
        email: values.email,
        password: values.password,
        recaptchaToken: token,
      })
    } else {
      setShowAlert(true)
      setAlertMessage({
        title: 'reCAPTCHA Error',
        variant: 'negative',
        message: 'reCAPTCHA failed to load. Please try again later.',
      })
    }
  }

  return (
    <FlexContainer
      className="bg-main border-2 border-black h-screen"
      direction={FlexDirection.COL}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
    >
      <div className="relative w-[99%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%] 3xl:w-[30%]">
        <FlexContainer className="mb-4 w-[99%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]">
          <Link href={`${mainLink}`} className="text-black hover:text-gray-800 font-bold">
            ← Back to Home
          </Link>
        </FlexContainer>
        <FlexContainer
          className="border-2 relative border-black p-4 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-white w-full"
          direction={FlexDirection.COL}
        >
          <FlexContainer
            direction={FlexDirection.COL}
            className="gap-2 w-full"
            align={AlignItems.CENTER}
          >
            <Title level={HeaderLevel.H3}>𝄞 Login to ColorAria! 𝄞</Title>
            <Text>{`Let's get started by signing in`}</Text>
          </FlexContainer>
          {showAlert && (
            <Alert className={'mt-4'} variant={alertMessage.variant}>
              <Terminal className="h-4 w-4" />
              <AlertTitle>{alertMessage.title}</AlertTitle>
              <AlertDescription>{alertMessage.message}</AlertDescription>
            </Alert>
          )}
          <Text className="my-4">
            {`Don't have an account? `}
            <Link
              href={`${mainLink}/register`}
              className="text-accent2 hover:text-accent2-lighter underline"
            >
              Sign up!
            </Link>
          </Text>
          <Text variant="emphasis" className="mb-2">
            Login With:
          </Text>
          <FlexContainer direction={FlexDirection.COL} className="w-full" align={AlignItems.CENTER}>
            {loadFingerprint ? (
              <PropagateLoader size={16} />
            ) : (
              <Button
                variant="secondary"
                className="w-full"
                onClick={() =>
                  (window.location.href = `/api/oauth2/authorize?fingerprintHash=${fingerprintHash}&redirectOnFail=/login`)
                }
              >
                <NextImage
                  className="w-6 h-6"
                  src="https://www.svgrepo.com/show/475656/google-color.svg"
                  width={24}
                  height={24}
                  loading="lazy"
                  alt="google logo"
                />
                <Text>Continue with Google</Text>
              </Button>
            )}

            <FlexContainer
              direction={FlexDirection.ROW}
              className="w-full gap-2"
              align={AlignItems.CENTER}
              justify={JustifyContent.CENTER}
            >
              <Divider className="w-[40%]" />
              <Text size="xs">Or continue with</Text>
              <Divider className="w-[40%]" />
            </FlexContainer>

            <Form {...form}>
              <FlexContainer className="space-y-4 font-bold w-full" direction={FlexDirection.COL}>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className={'w-full'}>
                      <FormLabel>Email:</FormLabel>
                      <FormControl>
                        <Input className={'w-full'} placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className={'w-full'}>
                      <FormLabel>Password:</FormLabel>
                      <FormControl>
                        <Input type="password" className={'w-full'} placeholder="" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Text size="xs">
                  <Link
                    className="text-accent2 hover:text-accent2-lighter underline"
                    href={`${mainLink}/reset-password`}
                  >
                    Forget your password?
                  </Link>
                </Text>
                <Button variant="emphasis" className="w-full" onClick={form.handleSubmit(onSubmit)}>
                  {bLoading ? (
                    <MoonLoader size={IconSize.Small} />
                  ) : (
                    <>
                      <LogIn />
                      {'Login'}
                    </>
                  )}
                </Button>
              </FlexContainer>
            </Form>
          </FlexContainer>
        </FlexContainer>
      </div>
    </FlexContainer>
  )
}

export default LoginComponent
