@import 'tailwindcss';
@import "tw-animate-css";

/* @plugin 'tailwindcss-animate'; */
@plugin 'tailwind-scrollbar-hide';

@custom-variant dark (&:is(.dark *));

@theme {
  --color-main-lightest: #f9f5f2;
  --color-main-lighter: #ffead4;
  --color-main: #ffe1c4;

  --color-green-spec-lighter: hsl(93.91deg, 93.24%, 70.98%);
  --color-green-spec: hsl(161.6deg, 100%, 41.57%);

  --color-main-accent: #84b22a;

  --color-primary-button-lighter: hsl(39, 100%, 95%);
  --color-primary-button: #ffecd4;

  --color-neutral-lighter: #eaf2d7;
  --color-neutral: #e5f7bc;

  --color-accent1-lighter: #bde0fe;
  --color-accent1: #a2d2ff;

  --color-accent2-lighter: #ffa2b9;
  --color-accent2: #fe6e94;

  --color-accent3-lighter: hsl(159, 100%, 78%);
  --color-accent3: #05e17a;

  --color-accent4-lighter: hsl(273, 82%, 85%);
  --color-accent4: hsl(273, 91%, 77%);

  --color-accent5-lighter: #f89696;
  --color-accent5: #ff5555;

  --color-accent6-lighter: #f2f3a4;
  --color-accent6: #f1f333;

  --color-black-emphasis-lighter: #2c312b;
  --color-black-emphasis: #212121;

  --color-sec-rust-orange-lighter: hsl(22, 100%, 90%);
  --color-sec-rust-orange: hsl(22, 100%, 71%);

  --color-sec-light-peach-lighter: #ffead4;
  --color-sec-light-peach: #ffe1c4;

  --color-white: #ffffff;
  --color-white-hover: #f1f1f1;

  --color-overlay: rgba(0, 0, 0, 0.8);
  --color-bg: #fff4e0;
  --color-text: #000;
  --color-border: hsl(0, 0%, 0%);
  --color-dark-bg: #2c312b;
  --color-dark-text: #eeefe9;
  --color-dark-border: #000;
  --color-secondary-black: #212121;
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --radius-base: 5px;
  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --shadow-light: 3px 4px 0px 0px #000;
  --shadow-dark: 3px 4px 0px 0px #000;

  --translate-box-shadow-x: 3px;
  --translate-box-shadow-y: 4px;
  --translate-reverse-box-shadow-x: -3px;
  --translate-reverse-box-shadow-y: -4px;

  --font-weight-base: 500;
  --font-weight-heading: 700;

  --animate-shine: shine var(--duration) infinite linear;

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-scroll: scroll var(--animation-duration, 40s)
    var(--animation-direction, forwards) linear infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes scroll {
    to {
      transform: translate(calc(-50% - 0.5rem));
    }
  }
  @keyframes move-up-alternate {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(80%);
    }
    51% {
      transform: translateY(-80%);
    }
    100% {
      transform: translateY(0);
    }
  }
  @keyframes shine {
    0% {
      background-position: 0% 0%;
    }
    50% {
      background-position: 100% 100%;
    }
    to {
      background-position: 0% 0%;
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem}
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%}
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}