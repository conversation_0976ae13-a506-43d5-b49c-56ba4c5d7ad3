import { LucideHome } from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { Button } from './_component/Button'
import Title from './_component/Title'
import MainFooter from './_templates/MainFooter'
import MainNavMenu from './_templates/MainNavMenu'

export const metadata: Metadata = {
  title: '404 Not Found | ColorAria',
  description: `The secret 404 page of ColorAria. You've found it!`,
}

export default function NotFound() {
  return (
    <div className={`flex flex-col min-h-screen w-full`}>
      <div className="bg-main sticky top-0 w-full z-400">
        <MainNavMenu />
      </div>
      <div className="flex flex-1 items-center justify-center gap-4">
        <div className="flex flex-col items-center justify-center h-full">
          <NextImage
            src="/media/static/illustration/notFound.svg"
            alt="404"
            width={300}
            height={300}
          />
          <Title className="text-center mb-4 mt-4">{`You've found the secret 404 page!`}</Title>
          <p className="text-center mb-4 text-xl">
            {`But there's nothing here. No worries, let's head back!`}
          </p>
          <Link href="/">
            <Button variant="secondary" size="xl">
              <LucideHome /> Return Home
            </Button>
          </Link>
        </div>
      </div>
      <div className="bg-main bottom-0 w-full">
        <MainFooter />
      </div>
    </div>
  )
}
