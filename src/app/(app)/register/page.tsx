import { Metadata } from 'next'
import RegisterComponent from './component'

export const metadata: Metadata = {
  title: 'Signup for ColorAria',
  description:
    'Signup for ColorAria and create a free account get 4 credits to test out our services.',
}

interface RegisterPageProps {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function RegisterPage({ searchParams }: RegisterPageProps) {
  const { error, message } = await searchParams

  return <RegisterComponent error={error} message={message} />
}

export default RegisterPage
