'use client'

import { zod<PERSON><PERSON>ol<PERSON> } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { RotateCcw, Terminal } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader } from 'react-spinners'
import { z } from 'zod'
import { Alert, AlertDescription, AlertTitle } from '@/app/(app)/_component/Alert'
import { IconSize } from '@/utilities/local/css'
import { Button } from '../_component/Button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../_component/Form'
import { Input } from '../_component/Input'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import GridContainer, { GridItem, RowSpan, ColSpan } from '../_cssComp/GridContainer'
import { ApiResponse } from '../_localApi/util'

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

interface AlertMessage {
  title: string
  variant: 'default' | 'positive' | 'negative' | 'destructive' | null | undefined
  message: string
}

function ResetPasswordPage() {
  const router = useRouter()
  const [showAlert, setShowAlert] = useState(false)
  const [bLoading, setBLoading] = useState(false)
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({
    title: '',
    variant: 'default',
    message: '',
  })

  const formSchema = z.object({
    email: z.string().email({
      message: 'This is not a valid email!',
    }),
    password: z.string().min(8, {
      message: 'Password must be at least 8 characters!',
    }),
    confirmPassword: z.string().min(1, {
      message: 'Confirm password is required!',
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
    },
    mode: 'onSubmit',
  })

  const resetPasswordMutation = useMutation({
    // TODO: Replace with verify code call
    // consider creating a separate table for different verified actions to simplify the flow
    // consider using redirection link from email directly, if putting additional data in the link, would need to encrypt it
    mutationFn: (data: { email: string; password: string }) => {
      return new Promise<ApiResponse<any>>((resolve) => {
        setTimeout(() => {
          resolve({
            success: true,
            message: { message: 'Verification email sent. Please check your inbox.', status: 200 },
            data: null,
          })
        }, 1000)
      })
    },
    onSuccess: (data: ApiResponse<any>) => {
      if (data.success) {
        setShowAlert(true)
        setAlertMessage({
          title: 'Success!',
          variant: 'positive',
          message: data.message.message,
        })
        // Redirect to verification page after a short delay
        setTimeout(() => {
          router.push(`/verify?email=${data.data.email}`)
        }, 1500)
      } else {
        setShowAlert(true)
        setAlertMessage({
          title: 'Something went wrong!',
          variant: 'negative',
          message: data.message.message,
        })
      }
    },
    onMutate: () => {
      setBLoading(true)
    },
    onSettled: () => {
      setBLoading(false)
    },
    onError: (error) => {
      setShowAlert(true)
      setAlertMessage({
        title: 'Something went wrong!',
        variant: 'negative',
        message: error.message,
      })
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Check password matching only on submit
    if (values.password !== values.confirmPassword) {
      setShowAlert(true)
      setAlertMessage({
        title: 'Password Error',
        variant: 'negative',
        message: "Passwords don't match",
      })
      return
    }

    resetPasswordMutation.mutateAsync({
      email: values.email,
      password: values.password,
    })
  }

  return (
    <FlexContainer
      className="bg-main border-2 border-black h-screen gap-2"
      direction={FlexDirection.COL}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
    >
      <FlexContainer className="w-[99%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]">
        <Link href={`${mainLink}/login`} className="text-black hover:text-gray-800 font-bold">
          ← Back to login
        </Link>
      </FlexContainer>
      <FlexContainer
        className="border-2 border-black p-4 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-white w-[99%] sm:w-[95%] md:w-[70%] lg:w-[60%] xl:w-[50%] 2xl:w-[40%]"
        direction={FlexDirection.COL}
      >
        <FlexContainer
          direction={FlexDirection.COL}
          className="gap-2 w-full mb-4"
          align={AlignItems.START}
        >
          <FlexContainer
            direction={FlexDirection.ROW}
            align={AlignItems.CENTER}
            className="gap-2 w-full"
          >
            <RotateCcw />
            <Title level={HeaderLevel.H3}>Password reset</Title>
          </FlexContainer>
          <Text>{`Enter your email and new password, we'll send you a verification email to confirm.`}</Text>
        </FlexContainer>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          {showAlert && (
            <Alert className={'mb-5'} variant={alertMessage.variant}>
              <Terminal className="h-4 w-4" />
              <AlertTitle>{alertMessage.title}</AlertTitle>
              <AlertDescription>{alertMessage.message}</AlertDescription>
            </Alert>
          )}

          <Form {...form}>
            <FlexContainer className="space-y-4 font-bold w-full" direction={FlexDirection.COL}>
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className={'w-full'}>
                    <FormLabel>Email Address:</FormLabel>
                    <FormControl>
                      <Input
                        className={'w-full'}
                        placeholder="Enter your registered email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className={'w-full'}>
                    <FormLabel>New Password:</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        className={'w-full'}
                        placeholder="Enter new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className={'w-full'}>
                    <FormLabel>Confirm Password:</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        className={'w-full'}
                        placeholder="Confirm new password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button variant="emphasis" className="w-full" onClick={form.handleSubmit(onSubmit)}>
                {bLoading ? (
                  <MoonLoader size={IconSize.Small} />
                ) : (
                  <>
                    <RotateCcw /> Reset Password
                  </>
                )}
              </Button>
            </FlexContainer>
          </Form>
        </FlexContainer>
      </FlexContainer>
    </FlexContainer>
  )
}

export default ResetPasswordPage
