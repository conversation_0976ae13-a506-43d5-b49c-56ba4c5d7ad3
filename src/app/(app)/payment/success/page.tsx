import { CircleCheckBig, Images, Mail, XCircle } from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { cache } from 'react'
import { Button } from '../../_component/Button'
import Text from '../../_component/Text'
import Title from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  FlexDirection,
  JustifyContent,
  AlignItems,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { getStripeCheckoutSession } from '../../_localApi/stripeClient'
import MainNavMenu from '../../_templates/MainNavMenu'

type Params = Promise<{ slug: string }>
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>

const getStripeSession = cache(async (sessionId: string) => {
  const sessionData = await getStripeCheckoutSession(sessionId)
  if (!sessionData) {
    redirect('/')
  }
  return sessionData
})

export async function generateMetadata(props: {
  params: Params
  searchParams: SearchParams
}): Promise<Metadata> {
  const params = await props.searchParams
  const sessionId = params.session_id as string
  const sessionData = await getStripeSession(sessionId)

  const paid = sessionData?.data.paid

  return {
    title: paid ? 'Payment successful | ColorAria' : 'Payment failed | ColorAria',
    description: paid
      ? 'Your payment was successful! Thank you for your support.'
      : 'Unfortunately, your payment was not successful. Please try again.',
  }
}

export default async function PaymentSuccessPage(props: {
  params: Params
  searchParams: SearchParams
}) {
  // Await the searchParams before accessing properties
  const params = await props.searchParams
  const sessionId = params.session_id as string

  const sessionData = await getStripeSession(sessionId)

  if (!sessionId) {
    redirect('/')
  }

  if (!sessionData) {
    redirect('/')
  }

  // To be decided, as this can mean many things.
  // Some API call failed or our failure. (Usually a failure on either Stripe's or our end)
  if (!sessionData.success) {
    redirect('/')
  }

  //Successful Payment!
  if (sessionData.data.paid) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <NextImage
                  src="/media/static/illustration/purchaseSuccess.svg"
                  alt="purchase success"
                  width={270}
                  height={270}
                />
                <FlexContainer
                  direction={FlexDirection.ROW}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="gap-4 mb-4"
                >
                  <CircleCheckBig size={32} />{' '}
                  <Title className="text-3xl">Purchase Successful!</Title>
                </FlexContainer>
                <Text className="text-xl text-center">
                  {`Thank you very much for your purchase!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  {`We're excited to have you onboard, let's not dally and start generating some coloring pages now!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  Thank you once again!~
                </Text>
                <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                  <Link href="/">
                    <Button variant="secondary" size="xl">
                      Return Home
                    </Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button variant="emphasis" size="xl">
                      <Images /> Get started now!
                    </Button>
                  </Link>
                </GridContainer>
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }

  //Something went wrong while paying
  // (Strictly a payment intent was made, but something went wrong while paying - cards were rejected, etc.)
  if (!sessionData.data.paid) {
    return (
      <main>
        <FlexContainer direction={FlexDirection.COL} className="w-full">
          <div className="bg-main sticky top-0 w-full z-400">
            <MainNavMenu />
          </div>
          <section className="w-full">
            <Container>
              <FlexContainer
                direction={FlexDirection.COL}
                className="w-full h-[90vh]"
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
              >
                <NextImage
                  src="/media/static/illustration/purchaseCancelled.svg"
                  alt="purchase cancelled"
                  width={270}
                  height={270}
                />
                <FlexContainer
                  direction={FlexDirection.ROW}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                  className="gap-4 mb-4"
                >
                  <XCircle size={32} /> <Title className="text-3xl">Purchase failed</Title>
                </FlexContainer>
                <Text className="text-xl text-center">
                  {`Looks like something went wrong with our payment processor.`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  {`Please contact us with your email you used for payment and we will get back to you as
                  soon as possible!`}
                </Text>
                <Text variant="description" className="text-xl text-center">
                  Thank you!~
                </Text>
                <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                  <Link href="/">
                    <Button variant="secondary" size="xl">
                      Return Home
                    </Button>
                  </Link>
                  {/* TODO: ADD A CONTACT BUTTON */}
                  <Button variant="emphasis" size="xl">
                    <Mail /> Contact Us
                  </Button>
                </GridContainer>
              </FlexContainer>
            </Container>
          </section>
        </FlexContainer>
      </main>
    )
  }
}
