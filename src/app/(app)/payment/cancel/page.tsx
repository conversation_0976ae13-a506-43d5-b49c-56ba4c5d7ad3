import { Gem, XCircle } from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import { Button } from '../../_component/Button'
import Text from '../../_component/Text'
import Title from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  FlexDirection,
  JustifyContent,
  AlignItems,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { apiFetch } from '../../_localApi/util'
import MainNavMenu from '../../_templates/MainNavMenu'
import { SessionInfo } from '../../api/session/[sessionId]/route'

type Params = Promise<{ slug: string }>
type SearchParams = Promise<{ [key: string]: string | string[] | undefined }>

export const metadata: Metadata = {
  title: 'Purchase Cancelled | ColorAria',
  description: 'User has cancelled their payment.',
}

export default async function PaymentCancelPage(props: {
  params: Params
  searchParams: SearchParams
}) {
  // Await the searchParams before accessing properties
  const params = await props.searchParams
  const sessionId = params.session_id as string

  const headers = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit

  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const sessionData = await apiFetch<SessionInfo>(`${HOST_URL}/api/session/${sessionId}`, headers)

  // conditions to enforce this page cannot be accessed
  // not a valid sessionId or already paid
  if (!sessionId || sessionData.data.paid) {
    redirect('/')
  }
  return (
    <main>
      <FlexContainer direction={FlexDirection.COL} className="w-full">
        <div className="bg-main sticky top-0 w-full z-400">
          <MainNavMenu />
        </div>
        <section className="w-full">
          <Container>
            <FlexContainer
              direction={FlexDirection.COL}
              className="w-full h-[90vh]"
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
            >
              <NextImage
                src="/media/static/illustration/purchaseCancelled.svg"
                alt="purchase cancelled"
                width={270}
                height={270}
              />
              <FlexContainer
                direction={FlexDirection.ROW}
                justify={JustifyContent.CENTER}
                align={AlignItems.CENTER}
                className="gap-4 mb-4"
              >
                <XCircle size={32} /> <Title className="text-3xl">Purchase cancelled</Title>
              </FlexContainer>
              <Text className="text-xl text-center">
                Please try again if this was unintentional.
              </Text>
              <Text variant="description" className="text-xl text-center">
                Do contact us if you need help or further information.
              </Text>
              <Text variant="description" className="text-xl text-center">
                Thank you!~
              </Text>
              <GridContainer className="grid-cols-1 md:grid-cols-2 mt-4 gap-4">
                <Link href="/">
                  <Button variant="secondary" size="xl">
                    Return Home
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button variant="emphasis" size="xl">
                    <Gem /> Go to Pricing
                  </Button>
                </Link>
              </GridContainer>
            </FlexContainer>
          </Container>
        </section>
      </FlexContainer>
    </main>
  )
}
