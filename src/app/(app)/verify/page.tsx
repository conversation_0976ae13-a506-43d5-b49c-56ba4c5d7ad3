import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { getPayload } from 'payload'
import { Suspense } from 'react'
import config from '@payload-config'
import { VerifyEmailForm } from './component'
import UserDAO from '../_backend/common/dao/UserDAO'
import UserService from '../_backend/common/service/UserService'

export const metadata: Metadata = {
  title: 'Verify Email | ColorAria',
  description: "Verify user's email",
}

interface VerifyEmailPage {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function VerifyEmailPage({ searchParams }: VerifyEmailPage) {
  const { userId, email } = await searchParams
  const payload = await getPayload({ config })
  const userDAO = new UserDAO(payload)
  const userService = new UserService(userDAO)

  if (userId == null || email == null) {
    redirect('/')
  }

  if (userId instanceof Array) {
    redirect('/')
  }

  try {
    const user = await userService.getUserById(userId)

    if (!user) {
      redirect('/')
    }

    if (user.email !== email) {
      redirect('/')
    }

    if (user.verified) {
      redirect('/')
    }
  } catch (error) {
    redirect('/')
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyEmailForm />
    </Suspense>
  )
}

export default VerifyEmailPage
