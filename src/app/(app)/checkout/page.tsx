'use client'
import { Elements } from '@stripe/react-stripe-js'
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js'
import React from 'react'
import CheckoutForm from './CheckoutForm'

// Important Note: This is needed only if we want to customize Checkout page
// we would need to create client component with STRIPE_PUBLISHABLE_KEY
// NOT needed in init test flow

// Load your publishable key from the environment variables
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '')

// Add Stripe Elements appearance options
const appearance = {
  theme: 'stripe' as const,
  variables: {
    colorPrimary: '#0570de',
    colorBackground: '#ffffff',
    colorText: '#30313d',
    colorDanger: '#df1b41',
    fontFamily: 'system-ui, sans-serif',
    spacingUnit: '4px',
    borderRadius: '4px',
  },
}

const options: StripeElementsOptions = {
  mode: 'payment' as const,
  amount: 1099,
  currency: 'usd',
  appearance,
}

const PaymentPage: React.FC = () => {
  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6">Payment Details</h2>
      <Elements stripe={stripePromise} options={options}>
        <CheckoutForm />
      </Elements>
    </div>
  )
}

export default PaymentPage
