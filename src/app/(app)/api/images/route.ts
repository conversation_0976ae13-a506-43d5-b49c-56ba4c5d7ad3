import { getPayload } from 'payload'
import { error<PERSON><PERSON><PERSON> } from '@/app/(app)/_backend/common/exception/errorHandler'
import { s3Client } from '@/lib/s3-client'
import { GeneratedImage } from '@/payload-types'
import { GeneratedImageStatus } from '@/types/GeneratedImageStatus'
import config from '@payload-config'
import GeneratedImageDAO from '../../_backend/common/dao/GeneratedImageDAO'

export interface GeneratedImagesResponse {
  images: GeneratedImage[]
  pagination: {
    totalCount: number
    currentPage: number
    totalPages: number
    pageSize: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export async function GET(request: Request) {
  try {
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const url = new URL(request.url)
    const status = url.searchParams.get('status') as GeneratedImageStatus | null
    const page = parseInt(url.searchParams.get('page') || '1', 10)
    const limit = parseInt(url.searchParams.get('limit') || '20', 10)

    const payload = await getPayload({ config })
    const generatedImageDAO = new GeneratedImageDAO(payload)

    let filterStatus
    if (status) {
      filterStatus = [status.toString()]
    } else {
      filterStatus = [GeneratedImageStatus.COMPLETED, GeneratedImageStatus.GENERATING]
    }
    const result = await generatedImageDAO.getImagesByStatus(user.id, filterStatus, page, limit)

    for (const image of result.docs) {
      if (image.status === GeneratedImageStatus.COMPLETED) {
        const url = await s3Client.generatePresignedUrl(
          image.id.toString(),
          undefined,
          `${user.id}/`,
        )
        image.url = url
      }
    }

    const response: GeneratedImagesResponse = {
      images: result.docs,
      pagination: {
        totalCount: result.totalDocs,
        currentPage: result.page,
        totalPages: result.totalPages,
        pageSize: result.limit,
        hasNextPage: result.hasNextPage,
        hasPrevPage: result.hasPrevPage,
      },
    }

    return new Response(JSON.stringify({ message: 'Images fetched', data: response }))
  } catch (error) {
    return errorHandler(error)
  }
}
