import { cookies } from 'next/headers'
import { type NextRequest } from 'next/server'
import { getPayload } from 'payload'
import UserDA<PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UnsplashService from '@/app/(app)/_backend/common/service/UnsplashService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const page = searchParams.get('page') ? Number(searchParams.get('page')) : 1
  const perPage = searchParams.get('per_page') ? Number(searchParams.get('per_page')) : 10
  try {
    const payload = await getPayload({ config })
    const cookieStore = cookies()
    const authToken = (await cookieStore).get('la-token')?.value
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    await userService.verifyUser(authToken)
    const unsplashService = new UnsplashService()
    const data = await unsplashService.getPhotos(page, perPage)
    return new Response(JSON.stringify({ message: 'Images successfully retrieved!', data: data }))
  } catch (error) {
    return errorHandler(error)
  }
}
