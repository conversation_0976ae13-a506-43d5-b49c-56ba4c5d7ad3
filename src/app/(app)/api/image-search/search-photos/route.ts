import { cookies } from 'next/headers'
import { type NextRequest } from 'next/server'
import { getPayload } from 'payload'
import UserDA<PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UnsplashService from '@/app/(app)/_backend/common/service/UnsplashService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const getParam = (key: string, fallback: string = '') => searchParams.get(key) ?? fallback
  const getNumberParam = (key: string, fallback: number = 1) => {
    const val = searchParams.get(key)
    return val !== null ? Number(val) : fallback
  }
  const query = getParam('query')
  const page = getNumberParam('page')
  const perPage = getNumberParam('per_page')
  const orderBy = getParam('order_by', 'relevant')
  const orientation = getParam('orientation')

  try {
    const payload = await getPayload({ config })
    const cookieStore = cookies()
    const authToken = (await cookieStore).get('la-token')?.value
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    await userService.verifyUser(authToken)
    const unsplashService = new UnsplashService()
    const data = await unsplashService.searchPhotos(query, page, perPage, orderBy, orientation)
    return new Response(JSON.stringify({ message: 'Images successfully retrieved!', data: data }))
  } catch (error) {
    return errorHandler(error)
  }
}
