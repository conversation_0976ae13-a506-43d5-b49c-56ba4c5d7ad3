import { NextRequest } from 'next/server'
import { errorHand<PERSON> } from '@/app/(app)/_backend/common/exception/errorHandler'
import UnsplashService from '@/app/(app)/_backend/common/service/UnsplashService'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const attributionId = decodeURIComponent(searchParams.get('attributionId') ?? '')
  if (!attributionId)
    return new Response(JSON.stringify({ message: 'Missing attributionId' }), { status: 400 })
  try {
    const unsplashService = new UnsplashService()
    const attributionRes = await unsplashService.attributionLink(attributionId)
    return new Response(JSON.stringify({ message: 'Endpoint sent', data: attributionRes }))
  } catch (error) {
    return errorHandler(error)
  }
}
