import * as jose from 'jose'
import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import {
  generateNetworkFingerprint,
  getNetworkFingerprint,
} from '@/utilities/local/network/clientIp'
import config from '@payload-config'

export interface StoredState {
  fingerprintHash: string
  networkFingerprintHash: string
}

export async function GET(request: NextRequest) {
  const payload = await getPayload({ config })
  const searchParams = request.nextUrl.searchParams
  const fingerprintHash = searchParams.get('fingerprintHash')
  const fingerprintHashParam = fingerprintHash ? fingerprintHash : ''
  const networkFingerprintObj = getNetworkFingerprint(request)
  const networkFingerprintHash = generateNetworkFingerprint(networkFingerprintObj)
  const redirectOnFail = searchParams.get('redirectOnFail')
  const state = {
    fingerprintHash: fingerprintHashParam,
    networkFingerprintHash,
    redirectOnFail: redirectOnFail,
  }
  const secret = new TextEncoder().encode(payload.secret)
  const expirationTime = Math.floor(Date.now() / 1000) + 60 * 5
  const token = await new jose.SignJWT(state)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(expirationTime) // Use numeric timestamp
    .sign(secret)

  const params = new URLSearchParams({
    client_id: process.env.GOOGLE_CLIENT_ID!,
    redirect_uri: process.env.GOOGLE_CALLBACK_URL!,
    response_type: 'code',
    scope: 'profile email',
    access_type: 'offline',
    prompt: 'consent',
    state: token,
  })

  const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?${params.toString()}`
  return NextResponse.redirect(googleAuthUrl)
}
