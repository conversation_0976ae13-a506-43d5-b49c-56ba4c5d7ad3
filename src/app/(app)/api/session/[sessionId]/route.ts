import { StatusCodes } from 'http-status-codes'
import { getPayload } from 'payload'
import SubscriptionPlanDAO from '@/app/(app)/_backend/common/dao/SubscriptionPlanDAO'
import SubscriptionRecordsDAO from '@/app/(app)/_backend/common/dao/SubscriptionRecordsDAO'
import UserDAO from '@/app/(app)/_backend/common/dao/UserDAO'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import SubscriptionPlanService from '@/app/(app)/_backend/common/service/SubscriptionPlanService'
import SubscriptionRecordService from '@/app/(app)/_backend/common/service/SubscriptionRecordService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { stripe } from '@/lib/stripe'
import config from '@payload-config'

export interface SessionInfo {
  paid: boolean
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ sessionId: string }> },
) {
  try {
    const { sessionId } = await params
    console.log(sessionId)
    const session = await stripe.checkout.sessions.retrieve(sessionId)
    const clientReferenceId = session.client_reference_id
    const stripeSubscriptionId = session.subscription as string
    const fetchSubscription = await stripe.subscriptions.retrieve(stripeSubscriptionId)
    if (!session.metadata) {
      throw new HTTPException('Empty session metadata', StatusCodes.BAD_REQUEST)
    }
    const subscriptionRecordId = session.metadata.subscriptionRecordId
    const stripePriceId = session.metadata.stripePriceId
    const customer = session.customer as string | null

    const payload = await getPayload({ config })
    const subscriptionPlanDAO = new SubscriptionPlanDAO(payload)
    const subscriptionService = new SubscriptionPlanService(subscriptionPlanDAO)
    const subscriptionRecordsDAO = new SubscriptionRecordsDAO(payload)
    const subscriptionRecordService = new SubscriptionRecordService(subscriptionRecordsDAO)
    const publicUserDAO = new UserDAO(payload)
    const publicUserService = new UserService(publicUserDAO)
    const startDate = fetchSubscription.items.data[0].current_period_start
    const endDate = fetchSubscription.items.data[0].current_period_end
    const cancelAt = fetchSubscription.cancel_at
    const canceledAt = fetchSubscription.canceled_at
    if (!clientReferenceId) {
      throw new HTTPException('Empty client reference id', StatusCodes.BAD_REQUEST)
    }
    const subscriptionPlan = await subscriptionService.retrieveSubscriptionByPriceId(stripePriceId)

    if (session.payment_status === 'paid') {
      await subscriptionRecordService.updateSubscription(
        subscriptionRecordId,
        stripeSubscriptionId,
        clientReferenceId,
        subscriptionPlan.id,
        customer ? customer : clientReferenceId,
        'active',
        startDate,
        endDate,
        '',
        cancelAt,
        canceledAt,
      )
      await publicUserService.updateUserSubscriptionRecord(
        clientReferenceId,
        parseInt(subscriptionRecordId),
        'subscriber',
        subscriptionPlan.monthlyCreditStipend,
      )
      return new Response(
        JSON.stringify({
          message: 'User has successfully paid for the subscription',
          data: { paid: true },
        }),
      )
    } else if (session.payment_status === 'unpaid') {
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
        expand: ['latest_invoice.payments.data.payment_intent'],
      })
      if (subscription.latest_invoice && typeof subscription.latest_invoice !== 'string') {
        const invoice = subscription.latest_invoice

        if (invoice.payments && invoice.payments.data.length > 0) {
          const payment = invoice.payments.data[0]
          if (payment.payment && payment.payment.type === 'payment_intent') {
            const paymentIntent = payment.payment.payment_intent
            if (typeof paymentIntent !== 'string' && paymentIntent != undefined) {
              const paymentIntentStatus = paymentIntent.status
              const paymentIntentErrorDeclineCode = paymentIntent.last_payment_error?.decline_code
              const paymentIntentErrorDeclineMessage = paymentIntent.last_payment_error?.message
              const paymentIntentErrorDeclineType = paymentIntent.last_payment_error?.type
              const paymentIntentErrorDeclineDocUrl = paymentIntent.last_payment_error?.doc_url
              const resStr = `
                Status: ${paymentIntentStatus}
                Decline Code: ${paymentIntentErrorDeclineCode}
                Decline Message: ${paymentIntentErrorDeclineMessage}
                Decline Type: ${paymentIntentErrorDeclineType}
                Decline Doc Url: ${paymentIntentErrorDeclineDocUrl}
              `
              await subscriptionRecordService.updateSubscription(
                subscriptionRecordId,
                stripeSubscriptionId,
                clientReferenceId,
                subscriptionPlan.id,
                customer ? customer : clientReferenceId,
                'unpaid',
                startDate,
                endDate,
                resStr,
                cancelAt,
                canceledAt,
              )
            }
          }
        } else {
          console.log('No payments found on the latest invoice')
        }
      } else {
        console.log('Latest invoice not available or not expanded')
      }
      return new Response(JSON.stringify({ message: 'Unpaid', data: { paid: false } }))
    } else if (session.status === 'open' || session.status === 'expired') {
      return new Response(JSON.stringify({ message: 'Process interrupted', data: { paid: false } }))
    }
  } catch (error) {
    return errorHandler(error)
  }
}
