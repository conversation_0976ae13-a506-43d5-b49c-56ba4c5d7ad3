import { StatusCodes } from 'http-status-codes'
import { cookies } from 'next/headers'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import { JWTType } from '@/app/(app)/_backend/common/utils/auth'

export interface LogoutResponse {
  email: string
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    // const requestHeaders = new Headers(request.headers)
    // const user = requestHeaders.get('x-user')

    // if (user == null) throw new HTTPException('Unauthenticated', StatusCodes.UNAUTHORIZED)
    // const userObj = JSON.parse(user) as JWTType

    await Promise.all([
      (await cookieStore).delete('la-token'),
      (await cookieStore).delete('auth-storage'),
    ])
    return new Response(
      JSON.stringify({
        message: 'User logged out!',
        data: {
          email: '',
        },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
