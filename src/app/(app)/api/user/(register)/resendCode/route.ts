import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import UserDA<PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import config from '@payload-config'

export interface ResendCodeResponse {
  sent: boolean
}

export async function GET(req: NextRequest) {
  try {
    const userId = req.nextUrl.searchParams.get('userId')
    if (!userId) {
      return NextResponse.json(
        { success: false, message: { message: 'User ID is required.' } },
        { status: 400 },
      )
    }

    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const user = await userDAO.getUserById(userId)
    const userService = new UserService(userDAO)
    await userService.sendVerificationEmail(user.id, user.email)

    return new Response(
      JSON.stringify({
        message: 'Resent OTP',
        data: { sent: true },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
