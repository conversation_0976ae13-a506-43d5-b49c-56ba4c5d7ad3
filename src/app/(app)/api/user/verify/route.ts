import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import { JWTType } from '@/app/(app)/_backend/common/utils/auth'
import config from '@payload-config'

export interface VerifyUserResult {
  id: JWTType['id']
  email: JWTType['email']
  firstName: JWTType['firstName']
  lastName: JWTType['lastName']
  freeCredits: JWTType['freeCredits']
  paidCredits: JWTType['paidCredits']
  rolloverCredits: JWTType['rolloverCredits']
  role: JWTType['role']
  exp: JWTType['exp']
}

export async function POST(request: Request) {
  try {
    const cookieStore = cookies()
    const authToken = (await cookieStore).get('la-token')?.value

    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)
    const verifyUser = await userService.verifyUser(authToken)
    return new Response(
      JSON.stringify({
        message: 'User verified!',
        data: {
          id: verifyUser.id,
          email: verifyUser.email,
          firstName: verifyUser.firstName,
          lastName: verifyUser.lastName,
          freeCredits: verifyUser.freeCredits,
          paidCredits: verifyUser.paidCredits,
          rolloverCredits: verifyUser.rolloverCredits,
          role: verifyUser.role,
          exp: verifyUser.exp,
        },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
