import { headers } from 'next/headers'
import { NextResponse } from 'next/server'
import { getPayload } from 'payload'
import SubscriptionDAO from '@/app/(app)/_backend/common/dao/SubscriptionRecordsDAO'
// import StripeService from '@/app/(app)/_backend/common/service/StripeService'
import { stripe } from '@/lib/stripe'
import config from '@payload-config'

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET

export async function POST(request) {
  /*
  Webhook is not strictly required, 
  note when specific event handler is enabled, 
  should make adjustment to the normal flow so that the action is not handled twice. 
  For testing, currently testing done on local with stripe CLI, should probably test it on Vercel or VPS.
  */
  try {
    // const body = await request.text()
    // const sig = request.headers.get('stripe-signature')
    let event

    const stripeSignature = (await headers()).get('stripe-signature')
    try {
      // event = stripe.webhooks.constructEvent(body, sig, webhookSecret)
      event = stripe.webhooks.constructEvent(await request.text(), stripeSignature, webhookSecret)
    } catch (err) {
      console.error('Error verifying webhook signature:', err)
      return NextResponse.json({ error: `Webhook Error: ${err.message}` }, { status: 400 })
    }

    const payload = await getPayload({ config })
    // const stripeService = new StripeService(new SubscriptionDAO(payload))
    // Handle different event types
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object
        console.log(`💰 PaymentIntent successful: ${paymentIntent.id}`)
        // Add your business logic here
        console.log('paymentIntent:', JSON.stringify(paymentIntent))
        // stripeService.handlePaymentSuccess(paymentIntent)
        break

      case 'payment_intent.payment_failed':
        const failedPaymentIntent = event.data.object
        console.log(`❌ Payment failed: ${failedPaymentIntent.id}`)
        // Add your business logic here
        break

      case 'checkout.session.completed':
        const session = event.data.object
        console.log(`✅ Checkout session completed: ${session.id}`)
        console.log('session:', JSON.stringify(session))
        // Add your business logic here
        const { client_reference_id: userId, payment_status: paymentStatus } = session
        console.log('userId:', userId)
        console.log('paymentStatus:', paymentStatus)
        if (paymentStatus !== 'paid') {
          console.log(`❌ Payment unsuccessfule: ${session.id}`)
          // TODO: check what Stripe expects from this event handling, or else just handle the paid case
          return NextResponse.json(
            { error: `Payment unsuccessful: ${session.id}` },
            { status: 400 },
          )
        }
        // cannot auto-expand event data object, need to retrieve object in separate call
        const subscription = await stripe.subscriptions.retrieve(session.subscription)
        console.log('subscription:', JSON.stringify(subscription))
        const {
          id: subscriptionId,
          customer: customerId,
          items: {
            data: [
              {
                price: { product: subscriptionPlanId },
              },
            ],
          },
          status,
          current_period_start: currentPeriodStart,
          current_period_end: currentPeriodEnd,
          cancel_at: cancelAt,
          canceled_at: canceledAt,
        } = subscription

        console.log('userId:', userId)
        console.log('subscriptionId:', subscriptionId)
        console.log('subscriptionPlanId:', subscriptionPlanId)
        console.log('customerId:', customerId)
        console.log('status:', status)
        console.log('currentPeriodStart:', currentPeriodStart)
        console.log('currentPeriodEnd:', currentPeriodEnd)
        console.log('cancelAt:', cancelAt)
        console.log('canceledAt:', canceledAt)

        // stripeService.handlePaymentSuccess(
        //   subscriptionId,
        //   userId,
        //   subscriptionPlanId,
        //   customerId,
        //   status,
        //   currentPeriodStart,
        //   currentPeriodEnd,
        // )
        break

      case 'product.updated':
        // Important: not handling this event thus should not create/modify any product in Stripe dashboard directly
        // else Stripe dashboard will be out of sync with database
        // Issue with this webhook: not compatible with beforeOperationHook in Payload CMS collection,
        // enabling both will cause them to trigger each other.
        const product = event.data.object
        console.log(`Product updated: ${product.id}`)
        // Add your business logic here
        // const payload = await getPayload({ config })
        // new SubscriptionPlanDAO(payload).updateSubscriptionPlan(product.id, product)
        break

      // Add more event types as needed
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (err) {
    console.error(`❌ Error processing webhook: ${err.message}`)
    return new Response(`Webhook Error: ${err.message}`, { status: 400 })
  }
}
