import { Spacing } from '@/utilities/local/css'
import MainFooter from '../_templates/MainFooter'
import MainNavMenu from '../_templates/MainNavMenu'

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className={`flex flex-col min-h-screen w-full`}>
      <div className="bg-main sticky top-0 w-full z-400">
        <MainNavMenu />
      </div>

      <div className="w-full md:flex-row md:overflow-hidden flex-auto">{children}</div>

      <div className="bg-main bottom-0 w-full">
        <MainFooter />
      </div>
    </div>
  )
}
