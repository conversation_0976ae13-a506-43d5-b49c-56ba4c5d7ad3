'use client'

import { Palette } from 'lucide-react'

type Props = {}

export default function DashboardSideBar(props: Props) {
  return (
    <div
      role="sidebar"
      className=" left-0 group top-0 z-50 flex h-full items-start justify-start bg-black"
    >
      <div className="flex flex-col w-full">
        <div
          // onClick={(e) => e.stopPropagation()}
          className=" p-2 flex flex-row content-center items-center justify-center z-10 dark:border-dark-border bg-black font-base hover:bg-accent2"
        >
          <Palette color="white" />
          <a className="block text-dark-text bg-accent5 px-5 py-4" href="#">
            Generate Coloring Page
          </a>
        </div>
      </div>
    </div>
  )
}
