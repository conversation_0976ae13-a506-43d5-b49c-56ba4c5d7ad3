'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { ChevronDown, Contrast, Save, Undo } from 'lucide-react'
import { useEffect, useState } from 'react'
import { v4 } from 'uuid'
import CardSolo from '@/app/(app)/_component/Card/CardSolo'
import ImageComparison from '@/app/(app)/_component/ComparisonImage'
import { Slider } from '@/app/(app)/_component/Slider'
import { Switch } from '@/app/(app)/_component/Switch'
import Text from '@/app/(app)/_component/Text'
import UploadBox from '@/app/(app)/_component/UploadBox'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'

import GridContainer, { ColSpan, GridItem } from '@/app/(app)/_cssComp/GridContainer'
import { ImageMetadata } from '@/app/(app)/_types/ImageMetadata'
import { ActionType } from '@/utilities/local/enums'
import {
  imageContrastManual,
  imageContrastNormalization,
} from '@/utilities/local/image/contrastNormalization'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import Title, { HeaderLevel } from '../../../_component/Title'

interface Props {
  extImage: ImageMetadata | null
  removeImage: () => void
  saveImage: (image: ImageMetadata) => void
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 items-center">
      <Contrast className="" />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Image Contrast
      </Title>
    </div>
  )
}

function ImageContrast(props: Props) {
  const { extImage, removeImage, saveImage } = props
  const [contrastAdjustment, setContrastAdjustment] = useState({
    contrast: 50,
    brightness: 50,
    lightness: 50,
  })
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: '',
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.CONTRAST,
  })
  const [modifiedSelectedImage, setModifiedSelectedImage] = useState<ImageMetadata>({
    id: '',
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.CONTRAST,
  })
  const [bManualOption, setBManualOption] = useState(false)
  const [disableSaving, setDisableSaving] = useState(true)

  const handleContrastChange = (contrast: number) => {
    setContrastAdjustment({ ...contrastAdjustment, contrast })
  }

  const handleBrightnessChange = (brightness: number) => {
    setContrastAdjustment({ ...contrastAdjustment, brightness })
  }

  const handleLightnessChange = (lightness: number) => {
    setContrastAdjustment({ ...contrastAdjustment, lightness })
  }

  useEffect(() => {
    if (modifiedSelectedImage.src) {
      setDisableSaving(false)
    } else {
      setDisableSaving(true)
    }
  }, [selectedImage])

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: extImage.id,
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.CONTRAST,
      })
      setModifiedSelectedImage({
        id: extImage.id,
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.CONTRAST,
      })
    }
  }, [extImage])

  useEffect(() => {
    const normalizeImage = async () => {
      if (!bManualOption) {
        const modifiedImage = await imageContrastNormalization(selectedImage.src)
        setModifiedSelectedImage((existingImage) => {
          return {
            ...existingImage,
            src: modifiedImage,
          }
        })
      } else {
        const normalizeSlider = (value: number) => value / 50 - 1
        const contrast = normalizeSlider(contrastAdjustment.contrast)
        const brightness = normalizeSlider(contrastAdjustment.brightness)
        const lightness = normalizeSlider(contrastAdjustment.lightness)
        const modifiedImage = await imageContrastManual(selectedImage.src, {
          contrast,
          brightness,
          lightness,
        })
        setModifiedSelectedImage((existingImage) => {
          return {
            ...existingImage,
            src: modifiedImage,
          }
        })
      }
    }
    normalizeImage()
  }, [selectedImage, bManualOption, contrastAdjustment])

  const revertImage = () => {
    setSelectedImage({
      id: '',
      src: '',
      width: 50,
      height: 50,
      actionType: ActionType.CONTRAST,
    })
    setModifiedSelectedImage({
      id: '',
      src: '',
      width: 50,
      height: 50,
      actionType: ActionType.CONTRAST,
    })
    removeImage()
  }

  const onGenerateImage = () => {
    saveImage(modifiedSelectedImage)
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            console.log(img.width, img.height)
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.CONTRAST,
            })
            setModifiedSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.CONTRAST,
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full h-full grid grid-cols-12 gap-8">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start">
            <FlexContainer
              className="w-full"
              direction={FlexDirection.ROW}
              justify={JustifyContent.BETWEEN}
              align={AlignItems.CENTER}
            >
              {selectedImage.src === '' ? (
                <Text variant="emphasis">Upload an Image:</Text>
              ) : (
                <Text variant="emphasis">Take a look at the image!</Text>
              )}
              {selectedImage.src && (
                <Button size="sm" round="round" variant="negative" onClick={revertImage}>
                  <Undo className="" />
                  Revert
                </Button>
              )}
            </FlexContainer>
            {selectedImage.src == '' ? (
              <FlexContainer
                direction={FlexDirection.ROW}
                justify={JustifyContent.BETWEEN}
                align={AlignItems.CENTER}
                className={'gap-2 w-full'}
              >
                <UploadBox existingImage={selectedImage.src} handleChange={handleImageChange} />
              </FlexContainer>
            ) : (
              <ImageComparison original={selectedImage.src} modified={modifiedSelectedImage.src} />
            )}
          </div>
        </div>
        <FlexContainer className="w-full col-span-12 gap-4">
          <CardSolo className="bg-accent4-lighter">
            <GridContainer className="grid-cols-4 w-full">
              <GridItem colSpan={ColSpan.SPAN_3}>
                <FlexContainer direction={FlexDirection.COL}>
                  <Text variant="emphasis" size="lg">
                    Normalize Image
                  </Text>
                  <Text variant="description" size="sm">
                    {`Automatically adjusts the image to make blacks deeper and whites brighter based on its current tones.`}
                  </Text>
                </FlexContainer>
              </GridItem>
              <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
                <Switch
                  checked={!bManualOption}
                  onCheckedChange={() => setBManualOption(!bManualOption)}
                />
              </GridItem>
            </GridContainer>
          </CardSolo>
          <Card
            className="w-full"
            titleClassName="bg-accent4-lighter"
            title={
              <GridContainer className="grid-cols-4 w-full">
                <GridItem colSpan={ColSpan.SPAN_3}>
                  <FlexContainer direction={FlexDirection.COL}>
                    <Text variant="emphasis" size="lg">
                      Manual Contrast Adjustment
                    </Text>
                    <Text variant="description" size="sm">
                      {`Manually adjust the image to your liking.`}
                    </Text>
                  </FlexContainer>
                </GridItem>
                <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
                  <Switch
                    checked={bManualOption}
                    onCheckedChange={() => setBManualOption(!bManualOption)}
                  />
                </GridItem>
              </GridContainer>
            }
          >
            <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full h-full">
              <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                <Text className={!bManualOption ? 'opacity-50' : ''}>
                  Contrast: {contrastAdjustment.contrast}
                </Text>
                <Slider
                  disabled={!bManualOption}
                  value={[contrastAdjustment.contrast]}
                  max={100}
                  min={0}
                  step={1}
                  onValueChange={(e) => handleContrastChange(e[0])}
                />
              </FlexContainer>
              <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                <Text className={!bManualOption ? 'opacity-50' : ''}>
                  Brightness: {contrastAdjustment.brightness}
                </Text>
                <Slider
                  disabled={!bManualOption}
                  value={[contrastAdjustment.brightness]}
                  max={100}
                  min={0}
                  step={1}
                  onValueChange={(e) => handleBrightnessChange(e[0])}
                />
              </FlexContainer>
              <FlexContainer direction={FlexDirection.COL} className="gap-2 w-full">
                <Text className={!bManualOption ? 'opacity-50' : ''}>
                  Lightness: {contrastAdjustment.lightness}
                </Text>
                <Slider
                  disabled={!bManualOption}
                  value={[contrastAdjustment.lightness]}
                  max={100}
                  min={0}
                  step={1}
                  onValueChange={(e) => handleLightnessChange(e[0])}
                />
              </FlexContainer>
            </FlexContainer>
          </Card>
        </FlexContainer>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={disableSaving}
          onClick={onGenerateImage}
        >
          <Save />
          Save Image
        </Button>
      </div>
    </Card>
  )
}

export default ImageContrast
