/* FlexContainer.module.css */

/* Make the scrollbar thinner for Webkit browsers */
.container {
    overflow-y: auto;  /* Allow vertical scrolling */
  }
  
  /* Target the scrollbar for Webkit browsers */
  .container::-webkit-scrollbar {
    width: 4px;  /* Thin vertical scrollbar */
    height: 4px; /* Thin horizontal scrollbar */
  }
  
  .container::-webkit-scrollbar-thumb {
    background-color: #888; /* Color of the scrollbar thumb */
    border-radius: 4px;      /* Rounded corners for the thumb */
  }
  
  .container::-webkit-scrollbar-thumb:hover {
    background-color: #555;  /* Color when hovering */
  }
  
  .container::-webkit-scrollbar-track {
    background: transparent; /* Transparent track */
  }
  
  /* For Firefox */
  .container {
    scrollbar-width: thin; /* Thin scrollbar */
  }