'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { ChevronDown, Crown, Palette, Text as TextIcon } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { Badge } from '@/app/(app)/_component/Badge'
import { Input } from '@/app/(app)/_component/Input'
import LockComponent from '@/app/(app)/_component/LockComponent'
import { Slider } from '@/app/(app)/_component/Slider'
import { Switch } from '@/app/(app)/_component/Switch'
import Text from '@/app/(app)/_component/Text'
import FlexContainer, { AlignItems, FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import { ApiResponse } from '@/app/(app)/_localApi/util'
import { useAuthUser } from '@/app/(app)/_provider/AuthProvider'
import { useCreditStore } from '@/app/(app)/_state/creditStoreState'
import { ImageGenerationResponseT2I } from '@/app/(app)/api/image-generation/text-to-image/route'
import { Feature } from '@/payload-types'
import { FeaturesType } from '@/utilities/local/enums'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../../../_component/Accordion'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import { RadioGroup, RadioGroupItem } from '../../../_component/RadioGroup'
import { Textarea } from '../../../_component/Textarea'
import Title, { HeaderLevel } from '../../../_component/Title'
import PromptGuidance from '../PromptGuidance'

interface Props {
  generateImage: UseMutationResult<
    ApiResponse<ImageGenerationResponseT2I>,
    Error,
    { prompt: string; aspectRatio: string; numberOfImages: number; highQuality: boolean },
    unknown
  >
  userFeatures: string[]
  features: Feature[]
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 items-center">
      <TextIcon />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Text to Coloring Page
      </Title>
    </div>
  )
}

const mainLink = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

function TextImageGeneratorCard(props: Props) {
  const { generateImage, userFeatures, features } = props
  const [prompt, setPrompt] = useState('')
  const [disableGenerate, setDisableGenerate] = useState(true)
  const [aspectRatio, setAspectRatio] = useState('1:1')
  const [imageRepetition, setImageRepetition] = useState(1)
  const [openGuidedPrompt, setOpenGuidedPrompt] = useState(false)
  const [highQuality, setHighQuality] = useState(false)

  const maxImageReptition = 3
  const authUser = useAuthUser()
  const creditStore = useCreditStore()

  const highNativeQualityCreditCost = features.filter(
    (f) => f.feature === FeaturesType.HIGH_NATIVE_QUALITY,
  )[0].creditCost
  const imageRepetitionCreditCost = features.filter(
    (f) => f.feature === FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION,
  )[0].creditCost

  const onGenerateImage = async () => {
    if (prompt.trim() === '') {
      toast.error('Please enter a prompt', {
        position: 'top-right',
      })
      setPrompt('')
      return
    }
    if (imageRepetition > maxImageReptition || imageRepetition < 1) {
      toast.error('Image Repetition must be between 1 and ' + maxImageReptition, {
        position: 'top-right',
      })
      setImageRepetition(1)
      return
    }
    await generateImage.mutateAsync({
      prompt: prompt,
      aspectRatio: aspectRatio,
      numberOfImages: imageRepetition,
      highQuality: highQuality,
    })
  }

  useEffect(() => {
    if (prompt.trim() !== '' && imageRepetition <= maxImageReptition && imageRepetition > 0) {
      setDisableGenerate(false)
    } else {
      setDisableGenerate(true)
    }
  }, [prompt, imageRepetition])

  const onSetHighQuality = (checked: boolean) => {
    if (!checked) {
      setHighQuality(checked)
    } else {
      setHighQuality(checked)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12 gap-4">
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex flex-row justify-between items-center">
            <Text variant="emphasis">Add your prompts:</Text>
            <PromptGuidance
              open={openGuidedPrompt}
              setOpen={setOpenGuidedPrompt}
              onSubmit={setPrompt}
            />
          </div>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <Textarea
              value={prompt}
              rows={4}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Your prompts should be as descriptive as possible for the best result. Consult our guide if you need help."
              className="w-full"
            />
            <Text size="sm" variant="description">
              Need some ideas? Take a look at our{' '}
              <Link
                className="text-accent2 hover:text-accent2-lighter underline"
                href={`${mainLink}/dashboard/image-library`}
              >
                image library~
              </Link>
            </Text>
          </FlexContainer>
        </div>
        <FlexContainer className="flex flex-col w-full col-span-12 gap-8 mt-5">
          <Accordion className="w-full" type="single" collapsible>
            <AccordionItem className="max-w-full" value="item-1">
              <AccordionTrigger>Image Aspect Ratio</AccordionTrigger>
              <AccordionContent>
                <div className="flex flex-col gap-4">
                  <Label>Pick an Aspect Ratio the image will be generated in.</Label>
                  <RadioGroup
                    defaultValue="square"
                    value={aspectRatio}
                    onValueChange={(e) => setAspectRatio(e)}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="1:1" id="square" />
                      <Label htmlFor="square">1:1 (Square)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="2:3" id="portrait" />
                      <Label htmlFor="portrait">2:3 (Portrait)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="3:2" id="landscape" />
                      <Label htmlFor="landscape">3:2 (Landscape)</Label>
                    </div>
                  </RadioGroup>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <LockComponent
            feature={FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION}
            userFeatures={userFeatures}
            creditCost={imageRepetitionCreditCost}
            mode="disable"
            costMode="onMount"
            className="flex flex-row gap-4 w-full col-span-12 items-center"
            multiplier={imageRepetition * (highQuality ? highNativeQualityCreditCost : 1)}
          >
            <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full">
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 w-full"
                align={AlignItems.CENTER}
              >
                <Label>
                  <Text variant="emphasis">Image Repetition:</Text>
                </Label>
                {authUser.user?.role.toLowerCase() === 'free' ? (
                  <Badge variant="destructive">
                    <Crown />
                    Premium Only
                  </Badge>
                ) : (
                  <></>
                )}
              </FlexContainer>
              <Slider
                value={[imageRepetition]}
                max={3}
                min={1}
                step={1}
                onValueChange={(e) => setImageRepetition(e[0])}
              />
              <Input
                type="number"
                min={1}
                max={3}
                value={imageRepetition}
                onChange={(e) =>
                  setImageRepetition(e.target.value !== '' ? parseInt(e.target.value) : 0)
                }
              />
              <Text size="sm" variant="description">
                {`We cannot guarantee the first generated image is perfect, therefore it's recommended
                to generate more than 1.`}
              </Text>
            </FlexContainer>
          </LockComponent>
        </FlexContainer>
        <LockComponent
          feature={FeaturesType.HIGH_NATIVE_QUALITY}
          chosenPlanFilter={['MUSE_PLAN', 'ARIA_PLAN']}
          userFeatures={userFeatures}
          creditCost={
            features.filter((f) => f.feature === FeaturesType.HIGH_NATIVE_QUALITY)[0].creditCost
          }
          mode="disable"
          costMode="none"
          className="flex flex-row gap-4 w-full col-span-12 items-center"
        >
          <Switch checked={highQuality} onCheckedChange={onSetHighQuality} />{' '}
          <Text variant="emphasis">High Native Quality</Text>
        </LockComponent>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={disableGenerate}
          onClick={onGenerateImage}
        >
          <Palette />
          Generate Image (-{creditStore.getTotal()})
        </Button>
      </div>
    </Card>
  )
}

export default TextImageGeneratorCard
