
/* Style the scrollbar */
.scrollContainer::-webkit-scrollbar {
    width: 8px; /* Makes the scrollbar thin */
    height: 8px; /* For horizontal scrollbar */
  }
  
  /* Style the scrollbar track */
  .scrollContainer::-webkit-scrollbar-track {
    background: transparent; /* Transparent track */
    border-radius: 20px; /* Rounded track */
  }
  
  /* Style the scrollbar thumb (the draggable part) */
  .scrollContainer::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2); /* Translucent thumb */
    border-radius: 20px; /* Rounded thumb */
  }
  
  /* Hover effect for scrollbar thumb */
  .scrollContainer::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5); /* Darker thumb on hover */
  }
  
  /* Style the scrollbar in Firefox */
  .scrollContainer {
    scrollbar-width: thin; /* Make the scrollbar thin */
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* Translucent thumb, transparent track */
  }