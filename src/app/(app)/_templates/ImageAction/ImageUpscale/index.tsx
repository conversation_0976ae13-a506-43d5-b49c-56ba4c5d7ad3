'use client'
import { UseMutationResult } from '@tanstack/react-query'
import { ImageUpscale, Terminal } from 'lucide-react'
import { useEffect, useState } from 'react'
import { v4 } from 'uuid'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/app/(app)/_component/Accordion'
import { Alert, AlertDescription, AlertTitle } from '@/app/(app)/_component/Alert'
import { RadioGroup, RadioGroupItem } from '@/app/(app)/_component/RadioGroup'
import Text from '@/app/(app)/_component/Text'
import UploadBox from '@/app/(app)/_component/UploadBox'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'

import { ApiResponse } from '@/app/(app)/_localApi/util'
import { useCreditStore } from '@/app/(app)/_state/creditStoreState'
import { ImageMetadata } from '@/app/(app)/_types/ImageMetadata'
import { ImageUpscaleResponse } from '@/app/(app)/api/image-generation/image-upscale/route'
import { ActionType } from '@/utilities/local/enums'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardApplication'
import { Label } from '../../../_component/Label'
import Title, { HeaderLevel } from '../../../_component/Title'

interface Props {
  generateImage: UseMutationResult<
    ApiResponse<ImageUpscaleResponse>,
    Error,
    {
      src: string
      upscale: number
    },
    void
  >
  extImage: ImageMetadata | null
}

interface AlertMessage {
  title: string
  variant: 'default' | 'positive' | 'negative' | 'destructive' | null | undefined
  message: string
}

const CardHeader = () => {
  return (
    <div className="w-full h-full flex flex-row gap-2 items-center">
      <ImageUpscale />
      <Title className="text-dark" level={HeaderLevel.H4}>
        Image Upscaler
      </Title>
    </div>
  )
}

function ImageUpscaler(props: Props) {
  const { generateImage, extImage } = props
  const creditStore = useCreditStore()
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>({
    id: v4(),
    src: extImage?.src || '',
    width: 50,
    height: 50,
    actionType: ActionType.AI_UPSCALER,
  })
  const [alertMessage, setAlertMessage] = useState<AlertMessage>({
    title: 'Something went wrong',
    variant: 'default',
    message: '',
  })
  const [showAlert, setShowAlert] = useState(false)
  const [upscaleMultiplier, setUpscaleMultiplier] = useState<string>('1.25')

  useEffect(() => {
    if (extImage) {
      setSelectedImage({
        id: v4(),
        src: extImage.src,
        width: extImage.width,
        height: extImage.height,
        actionType: ActionType.AI_UPSCALER,
      })
    }
  }, [extImage])

  const onGenerateImage = async () => {
    const result = await generateImage.mutateAsync({
      src: selectedImage.src,
      upscale: Number.parseFloat(upscaleMultiplier),
    })
  }

  useEffect(() => {
    console.log('Alert Message Updated:', alertMessage)
  }, [alertMessage])

  useEffect(() => {
    console.log('Show Alert Updated:', showAlert)
  }, [showAlert])

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()

      reader.onload = () => {
        if (reader.result) {
          const img = new Image()
          img.onload = () => {
            setSelectedImage({
              id: v4(),
              src: reader.result as string,
              width: img.width,
              height: img.height,
              actionType: ActionType.AI_UPSCALER,
            })
          }
          img.src = reader.result as string
        }
      }

      reader.readAsDataURL(file)
    }
  }

  return (
    <Card title={<CardHeader />}>
      <div className="w-full grid grid-cols-12 gap-4">
        {showAlert && (
          <Alert className={'mb-5 col-span-12'} variant={alertMessage.variant}>
            <Terminal className="h-4 w-4" />
            <AlertTitle>{alertMessage.title}</AlertTitle>
            <AlertDescription>{alertMessage.message}</AlertDescription>
          </Alert>
        )}
        <div className="flex flex-col w-full col-span-12 gap-3">
          <div className="flex items-center justify-start gap-3 flex-row sm:flex-col sm:items-start">
            <Text variant="emphasis">Upload your image:</Text>
            <FlexContainer
              direction={FlexDirection.ROW}
              justify={JustifyContent.BETWEEN}
              align={AlignItems.CENTER}
              className={'gap-2 w-full'}
            >
              <UploadBox existingImage={selectedImage.src} handleChange={handleImageChange} />
            </FlexContainer>
          </div>
        </div>
        <Accordion className="w-full col-span-12" type="single" collapsible>
          <AccordionItem className="max-w-full" value="item-1">
            <AccordionTrigger>Upscale Multiplier</AccordionTrigger>
            <AccordionContent>
              <div className="flex flex-col gap-4">
                <Label>Choose a upscale multiplier to upscale your image to:</Label>
                <RadioGroup
                  defaultValue="square"
                  value={upscaleMultiplier}
                  onValueChange={(e) => setUpscaleMultiplier(e)}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="1.25" id="1.25" />
                    <Label htmlFor="1.25">1.25</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="1.5" id="1.5" />
                    <Label htmlFor="1.5">1.5</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="1.75" id="1.75" />
                    <Label htmlFor="1.75">1.75</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="2" id="2" />
                    <Label htmlFor="2">2</Label>
                  </div>
                </RadioGroup>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <Button
          className="mt-5 w-full col-span-12"
          variant="emphasis"
          disabled={!selectedImage.src}
          onClick={onGenerateImage}
        >
          <ImageUpscale />
          Upscale Image (-{creditStore.getTotal()})
        </Button>
      </div>
    </Card>
  )
}

export default ImageUpscaler
