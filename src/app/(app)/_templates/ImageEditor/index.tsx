import * as fabric from 'fabric'
import { useEffect, useRef, useState } from 'react'
import { Label } from '../../_component/Label'
import { RadioGroup, RadioGroupItem } from '../../_component/RadioGroup'
import Text from '../../_component/Text'
import FlexContainer, { FlexDirection } from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { ImageMetadata } from '../../_types/ImageMetadata'
import DialogImageGrid from '../DialogImageGrid'

enum PaintSelection {
  None = 'none',
  Inpainting = 'inpainting',
  Outpainting = 'outpainting',
}

function ImageEditor() {
  const canvasEl = useRef<HTMLCanvasElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null)
  const [selectedImage, setSelectedImage] = useState<ImageMetadata>()
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 })
  const [paintSelection, setPaintSelection] = useState<PaintSelection>(PaintSelection.None)
  const [canvasDimensions, setCanvasDimensions] = useState({ width: 800, height: 800 })

  const maxWidth = 1208

  async function handleImageSelection(image: ImageMetadata) {
    setSelectedImage(image)
    const fabricImage = await fabric.FabricImage.fromURL(selectedImage?.src || '')
    const dimensions = {
      width: fabricImage.width,
      height: fabricImage.height,
    }
    setOriginalDimensions(dimensions)
    const containerWidth = containerRef.current?.clientWidth || window.innerWidth
    const targetWidth = Math.min(containerWidth, maxWidth, image.width)
    const scale = targetWidth / image.width
    const targetHeight = image.height * scale
    setCanvasDimensions({ width: targetWidth, height: targetHeight })
    fabricImage.scaleToWidth(canvasDimensions.width)
    fabricImage.set({
      selectable: false,
      evented: false,
    })
    canvas?.clear()
    canvas?.add(fabricImage)
    canvas?.renderAll()
  }

  useEffect(() => {
    if (canvasEl.current) {
      const canvas = new fabric.Canvas(canvasEl.current, {
        width: canvasDimensions.width,
        height: canvasDimensions.height,
      })

      canvas.backgroundColor = `white`
      canvas.renderAll()

      setCanvas(canvas)

      return () => {
        canvas.dispose()
      }
    }
  }, [])

  return (
    <GridContainer>
      <GridItem
        colSpan={ColSpan.SPAN_3}
        className="bg-main-lightest border-2 border-black rounded h-full"
      >
        <FlexContainer direction={FlexDirection.COL} className="h-full">
          <FlexContainer
            direction={FlexDirection.COL}
            className="w-full p-4 gap-2 bg-main-lightest border-b-2"
          >
            <Text variant="emphasis">Choose your Image:</Text>
            <DialogImageGrid onImageSelected={handleImageSelection} isGenerationHistorySelection />
          </FlexContainer>
          <FlexContainer
            direction={FlexDirection.COL}
            className="w-full p-4 gap-2 bg-main-lightest border-b-2"
          >
            <Text variant="emphasis">Enhancement:</Text>
            <FlexContainer direction={FlexDirection.ROW} className="gap-2">
              <RadioGroup
                defaultValue={PaintSelection.None}
                value={paintSelection}
                onValueChange={(e) => setPaintSelection(e as PaintSelection)}
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value={PaintSelection.Inpainting} id="inpainting" />
                  <Label htmlFor="inpainting">Inpainting</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value={PaintSelection.Outpainting} id="outpainting" />
                  <Label htmlFor="outpainting">Outpainting</Label>
                </div>
              </RadioGroup>
            </FlexContainer>
          </FlexContainer>
        </FlexContainer>
      </GridItem>
      <GridItem ref={containerRef} colSpan={ColSpan.SPAN_9}>
        <canvas id="canvas" ref={canvasEl} />
      </GridItem>
    </GridContainer>
  )
}

export default ImageEditor
