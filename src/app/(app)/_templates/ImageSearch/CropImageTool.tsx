import * as fabric from 'fabric'
import { Crop, RectangleHorizontal, RectangleVertical, Square, XIcon } from 'lucide-react'
import { useEffect, useRef, useState } from 'react'

import { cn } from '@/lib/utils'
import { Button } from '../../_component/Button'
import { Ta<PERSON>, TabsList, TabsTrigger } from '../../_component/Tabs'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'

interface Props {
  src: string
  className?: string
  onCropClose?: () => void
  onCropSuccess?: (image: HTMLImageElement) => void
}

type CropValue = 'square' | 'portrait' | 'landscape'

function CropImageTool(props: Props) {
  const { src, className, onCropSuccess, onCropClose } = props

  const divContainerRef = useRef<HTMLDivElement>(null)
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null)
  const fabricCanvasRef = useRef<HTMLCanvasElement>(null)
  const selectionRectRef = useRef<fabric.Rect>(null)
  const currentImageRef = useRef<fabric.FabricImage>(null)
  const [originalDimension, setOriginalDimension] = useState({
    width: 800,
    height: 800,
  })
  const [canvasDimension, setCanvasDimension] = useState({
    width: 800,
    height: 800,
  })
  const [cropValue, setCropValue] = useState<CropValue>('square')

  const handleLoadingImage = async () => {
    if (!fabricCanvas) return
    if (!divContainerRef.current) return

    const dpr = window.devicePixelRatio || 1

    const canvas = fabricCanvas
    canvas.clear()

    const imgElement = new Image()
    imgElement.crossOrigin = 'anonymous'

    const imageLoadPromise = new Promise((resolve, reject) => {
      imgElement.onload = () => resolve(imgElement)
      imgElement.onerror = reject
      imgElement.src = src
    })

    const loadedImg = await imageLoadPromise

    const image = new fabric.FabricImage(loadedImg as HTMLImageElement)
    setOriginalDimension({
      width: image.width,
      height: image.height,
    })

    if (!image) {
      console.error('Failed to create fabric image')
      return
    }

    const containerWidth = divContainerRef.current.clientWidth
    const targetWidth = Math.min(containerWidth, image.width)
    const scale = targetWidth / image.width
    let targetHeight = originalDimension.height * scale
    if (image.width > 0 && image.height > 0) {
      targetHeight = (image.height / image.width) * targetWidth
    }
    setCanvasDimension({
      width: targetWidth,
      height: targetHeight,
    })
    canvas.setDimensions({ width: targetWidth, height: targetHeight })
    image.scaleToWidth(targetWidth)
    canvas.add(image)
    canvas.centerObject(image)
    currentImageRef.current = image
    addSelectionRect(canvas, image)
  }

  useEffect(() => {
    if (fabricCanvas && src) {
      handleLoadingImage()
    }
  }, [src, fabricCanvas])

  //handle onLoad
  useEffect(() => {
    if (fabricCanvas) return
    if (fabricCanvasRef.current) {
      const canvas = new fabric.Canvas(fabricCanvasRef.current, {
        preserveObjectStacking: true,
      })

      setFabricCanvas(canvas)

      return () => {
        canvas.dispose()
        setFabricCanvas(null)
      }
    }
  }, [])

  const handleResize = () => {
    if (fabricCanvas && src) {
      handleLoadingImage()
    }
  }

  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [fabricCanvas, src])

  const handleCropValueChange = (cropValue: CropValue) => {
    setCropValue(cropValue)
    addSelectionRect(fabricCanvas!, currentImageRef.current!, cropValue)
  }

  const addSelectionRect = (
    canvas: fabric.Canvas,
    image: fabric.FabricImage,
    cropValue: CropValue = 'square',
  ) => {
    if (image && selectionRectRef.current) {
      canvas.remove(selectionRectRef.current)
      canvas.renderAll()
    }

    const size = Math.min(image.width ?? 100, image.height ?? 100)
    const selectionRect = new fabric.Rect({
      fill: 'rgba(0,0,0,0.3)',
      originX: 'left',
      originY: 'top',
      // stroke: 'black',
      stroke: undefined,
      strokeWidth: 0,
      opacity: 1,
      width: image.width ?? 100,
      height: image.height ?? 100,
      hasRotatingPoint: false,
      transparentCorners: false,
      cornerColor: 'white',
      cornerStrokeColor: 'black',
      borderColor: 'black',
      lockUniScaling: false,
      lockRotation: true,
      lockScalingFlip: true,
    })
    selectionRect.setControlsVisibility({
      mtr: false, // hide rotation handle
    })

    let widthMultiplier = 1
    let heightMultiplier = 1

    if (cropValue === 'portrait') {
      widthMultiplier = 2
      heightMultiplier = 3
    } else if (cropValue === 'landscape') {
      widthMultiplier = 3
      heightMultiplier = 2
    }

    const initialSize = Math.min(300, size)
    selectionRect.set({
      width: initialSize * widthMultiplier,
      height: initialSize * heightMultiplier,
      scaleX: 1,
      scaleY: 1,
    })

    selectionRect.on('moving', function (this: fabric.Rect, e) {
      const obj = this
      const imgBounds = {
        left: image.left || 0,
        top: image.top || 0,
        right: (image.left || 0) + (image.width || 0) * (image.scaleX || 1),
        bottom: (image.top || 0) + (image.height || 0) * (image.scaleY || 1),
      }

      // Calculate selection bounds
      const selWidth = obj.width * (obj.scaleX || 1)
      const selHeight = obj.height * (obj.scaleY || 1)

      // Constrain to image boundaries
      if (obj.left < imgBounds.left) obj.left = imgBounds.left
      if (obj.top < imgBounds.top) obj.top = imgBounds.top
      if (obj.left + selWidth > imgBounds.right) obj.left = imgBounds.right - selWidth
      if (obj.top + selHeight > imgBounds.bottom) obj.top = imgBounds.bottom - selHeight
    })

    selectionRect.on('scaling', function (this: fabric.Rect, e) {
      const obj = this

      // Ensure square aspect ratio by using the same scale for both dimensions
      if (cropValue === 'square') {
        const maxScale = Math.max(obj.scaleX || 1, obj.scaleY || 1)
        obj.set({
          scaleX: maxScale,
          scaleY: maxScale,
        })
      }

      let targetAspectRatio = 1 // square

      if (cropValue === 'portrait') {
        targetAspectRatio = 2 / 3 // width : height
      } else if (cropValue === 'landscape') {
        targetAspectRatio = 3 / 2 // width : height
      }

      const scaleX = obj.scaleX || 1

      if (cropValue !== 'square') {
        const width = obj.width!
        const newWidth = width * scaleX
        const newHeight = newWidth / targetAspectRatio
        const scaleY = newHeight / obj.height!

        obj.set({
          scaleY,
        })
      }

      // Get image boundaries
      const imgBounds = {
        left: image.left || 0,
        top: image.top || 0,
        right: (image.left || 0) + (image.width || 0) * (image.scaleX || 1),
        bottom: (image.top || 0) + (image.height || 0) * (image.scaleY || 1),
      }

      // Calculate selection bounds after scaling
      const selWidth = obj.width * (obj.scaleX || 1)
      const selHeight = obj.height * (obj.scaleY || 1)

      // Check if selection exceeds image boundaries
      let needsAdjustment = false

      if (obj.left < imgBounds.left) {
        obj.left = imgBounds.left
        needsAdjustment = true
      }

      if (obj.top < imgBounds.top) {
        obj.top = imgBounds.top
        needsAdjustment = true
      }

      if (obj.left + selWidth > imgBounds.right) {
        needsAdjustment = true
      }

      if (obj.top + selHeight > imgBounds.bottom) {
        needsAdjustment = true
      }

      // If selection exceeds boundaries, adjust scale to fit
      if (needsAdjustment) {
        const maxWidthScale = (imgBounds.right - obj.left) / obj.width
        const maxHeightScale = (imgBounds.bottom - obj.top) / obj.height
        const maxAllowedScale = Math.min(maxWidthScale, maxHeightScale)

        obj.set({
          scaleX: Math.min(obj.scaleX || 1, maxAllowedScale),
          scaleY: Math.min(obj.scaleY || 1, maxAllowedScale),
        })
      }
    })

    canvas.centerObject(selectionRect)
    canvas.add(selectionRect)
    canvas.setActiveObject(selectionRect)
    canvas.renderAll()
    selectionRectRef.current = selectionRect
  }

  const enforceAspectRatio = (width: number, height: number) => {
    // 2 : 3
    if (cropValue === 'portrait') {
      let newWidth = width
      if (width % 2 !== 0) {
        newWidth += 1
      }
      const height = (newWidth / 2) * 3
      return { width: newWidth, height }
    }
    // 3 : 2
    if (cropValue === 'landscape') {
      let newHeight = height
      if (height % 2 !== 0) {
        newHeight += 1
      }
      const width = (newHeight / 2) * 3
      return { width, height: newHeight }
    }
    return { width, height }
  }

  const handleCrop = () => {
    const currentImage = currentImageRef.current
    const selectionRect = selectionRectRef.current
    const canvas = fabricCanvas

    if (!canvas || !currentImage || !selectionRect) return

    canvas.remove(selectionRect)
    canvas.renderAll()

    try {
      // Get the coordinates of the selection rectangle
      const enforcedDimensions = enforceAspectRatio(
        Math.round(selectionRect.getScaledWidth()),
        Math.round(selectionRect.getScaledHeight()),
      )

      const cropRect = {
        left: selectionRect.left || 0,
        top: selectionRect.top || 0,
        width: enforcedDimensions.width,
        height: enforcedDimensions.height,
      }

      // Create a new canvas for the cropped image
      const dpr = window.devicePixelRatio || 1
      const tempCanvas = document.createElement('canvas')
      const tempCtx = tempCanvas.getContext('2d')

      if (!tempCtx) return

      // Set the temp canvas dimensions to match the crop area
      tempCanvas.width = cropRect.width * dpr
      tempCanvas.height = cropRect.height * dpr

      // Get the main canvas element and its context
      // const mainCanvas = fabricCanvasRef.current
      // if (!mainCanvas) return

      // // Draw only the cropped portion to the temp canvas
      // const mainCtx = mainCanvas.getContext('2d')
      // if (!mainCtx) return
      // mainCtx.scale(dpr, dpr)

      const fullRenderedCanvas = canvas.toCanvasElement()

      tempCtx.drawImage(
        fullRenderedCanvas,
        cropRect.left * dpr,
        cropRect.top * dpr,
        cropRect.width * dpr,
        cropRect.height * dpr,
        0,
        0,
        cropRect.width * dpr,
        cropRect.height * dpr,
      )

      // Create a new image from the temp canvas
      const croppedImage = new Image()
      croppedImage.src = tempCanvas.toDataURL('image/png')

      croppedImage.onload = () => {
        // Clear the main canvas
        canvas.clear()

        // Create a new fabric image from the cropped image
        const croppedFabricImage = new fabric.FabricImage(croppedImage, {
          selectable: true,
        })

        // Resize to fit the canvas
        const canvasWidth = canvas.getWidth()
        const canvasHeight = canvas.getHeight()
        const maxDisplayWidth = canvasWidth * 0.9
        const maxDisplayHeight = canvasHeight * 0.9
        const scaleX = maxDisplayWidth / croppedFabricImage.width
        const scaleY = maxDisplayHeight / croppedFabricImage.height
        const scale = Math.min(scaleX, scaleY, 1)

        croppedFabricImage.scale(scale)
        canvas.centerObject(croppedFabricImage)
        canvas.add(croppedFabricImage)
        canvas.renderAll()

        if (onCropSuccess) {
          onCropSuccess(croppedImage)
        }

        selectionRectRef.current = null
      }
    } catch (error) {
      console.error('Error during crop operation:', error)
    }
  }

  return (
    <div
      className={cn(
        'fixed inset-0 z-999 bg-black/50 backdrop-blur-xs p-4 flex flex-col items-center justify-center',
        className,
      )}
    >
      <div className="bg-accent1-lighter pr-4 rounded-xl shadow-xl w-[95%] max-w-[90vw] max-h-[90vh] overflow-auto">
        <FlexContainer
          direction={FlexDirection.ROW}
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
          wrap
          className="sticky top-0 z-500 w-full bg-accent1-lighter p-4 opacity-85 gap-2"
        >
          <Button
            variant="negative"
            size="icon"
            className={'absolute top-4 right-0 rounded-full'}
            onClick={onCropClose}
          >
            <XIcon />
          </Button>
          <Tabs
            value={cropValue}
            onValueChange={(cropValue) => handleCropValueChange(cropValue as CropValue)}
          >
            <TabsList className="py-8">
              <TabsTrigger value="square">
                <FlexContainer
                  direction={FlexDirection.COL}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                >
                  <Square /> 1:1 Crop
                </FlexContainer>
              </TabsTrigger>
              <TabsTrigger value="portrait">
                <FlexContainer
                  direction={FlexDirection.COL}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                >
                  <RectangleVertical /> 2:3 Crop
                </FlexContainer>
              </TabsTrigger>
              <TabsTrigger value="landscape">
                <FlexContainer
                  direction={FlexDirection.COL}
                  justify={JustifyContent.CENTER}
                  align={AlignItems.CENTER}
                >
                  <RectangleHorizontal /> 3:2 Crop
                </FlexContainer>
              </TabsTrigger>
            </TabsList>
          </Tabs>
          <FlexContainer></FlexContainer>
          <Button onClick={handleCrop} variant="emphasis" className="py-8">
            <FlexContainer
              direction={FlexDirection.COL}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
            >
              <Crop /> Apply Crop
            </FlexContainer>
          </Button>
        </FlexContainer>
        <FlexContainer
          ref={divContainerRef}
          direction={FlexDirection.COL}
          className="gap-4 relative w-full overflow-auto p-4"
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
        >
          <canvas ref={fabricCanvasRef} id="canvas" />
        </FlexContainer>
      </div>
    </div>
  )
}

export default CropImageTool
