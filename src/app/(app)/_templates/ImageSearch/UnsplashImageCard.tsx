import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useRef, useState } from 'react'
import { v4 } from 'uuid'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/(app)/_component/Dialog'
import { cn } from '@/lib/utils'
import { ActionType } from '@/utilities/local/enums'
import { calculateAspectRatio } from '@/utilities/local/image'
import CropImageTool from './CropImageTool'
import { UnsplashPhoto } from '../../_backend/common/service/UnsplashService/types'
import { Alert, AlertDescription, AlertTitle } from '../../_component/Alert'
import { Badge } from '../../_component/Badge'
import { Button } from '../../_component/Button'
import ImageWithSkeleton from '../../_component/ImageWithSkeleton'
import { Label } from '../../_component/Label'
import Text from '../../_component/Text'
import Title, { HeaderLevel } from '../../_component/Title'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { useImageTransfer } from '../../_state/imageTransfer'
import ImageLightBox from '../ImageAction/ImageLightbox'

interface Props {
  photo: UnsplashPhoto
  className?: string
}

function UnsplashImageCard(props: Props) {
  const { photo, className } = props
  const [currentPhoto, setCurrentPhoto] = useState(photo.urls.regular)
  const [currentPhotoDimensions, setCurrentPhotoDimensions] = useState({
    width: photo.width,
    height: photo.height,
  })
  const currentPhotoRaw = useRef<string>(photo.urls.regular)
  const [modified, setModified] = useState(false)
  const [openCropUI, setOpenCropUI] = useState(false)
  const [openImageLightbox, setOpenImageLightbox] = useState(false)

  const router = useRouter()

  const { setTransferImage } = useImageTransfer()

  const handleCropSuccess = (image: HTMLImageElement) => {
    setCurrentPhoto(image.src)
    setModified(true)
    setCurrentPhotoDimensions({
      width: image.width,
      height: image.height,
    })
    setOpenCropUI(false)
  }

  const handleRevert = () => {
    setModified(false)
    setCurrentPhoto(currentPhotoRaw.current)
    setCurrentPhotoDimensions({
      width: photo.width,
      height: photo.height,
    })
  }

  const handleGenerateImage = async () => {
    const attributionId = photo.links.download_location.split('https://api.unsplash.com/photos/')[1]
    setTransferImage({
      id: v4(),
      src: currentPhoto,
      width: currentPhotoDimensions.width,
      height: currentPhotoDimensions.height,
      actionType: ActionType.IMAGE_TO_COLOR,
      unsplashSrc: attributionId,
    })
    router.push('/dashboard/image-generation')
  }

  const handleBackgroundRemover = async () => {
    const attributionId = photo.links.download_location.split('https://api.unsplash.com/photos/')[1]
    setTransferImage({
      id: v4(),
      src: currentPhoto,
      width: currentPhotoDimensions.width,
      height: currentPhotoDimensions.height,
      actionType: ActionType.AI_BACKGROUND_REMOVER,
      unsplashSrc: attributionId,
    })

    router.push('/dashboard/image-generation')
  }

  const handleSketchToImage = async () => {
    setTransferImage({
      id: v4(),
      src: currentPhoto,
      width: currentPhotoDimensions.width,
      height: currentPhotoDimensions.height,
      actionType: ActionType.DRAWING_TO_SKETCH,
    })

    router.push('/dashboard/image-generation')
  }

  const checkAspectRatio = (width: number, height: number) => {
    const result = calculateAspectRatio(width, height)
    if (result === '1 : 1' || result === '2 : 3' || result === '3 : 2') {
      return false
    }
    return true
  }

  return (
    <Dialog>
      {openCropUI && (
        <CropImageTool
          src={currentPhotoRaw.current}
          onCropClose={() => setOpenCropUI(false)}
          onCropSuccess={handleCropSuccess}
          className={`${openCropUI ? 'pointer-events-auto' : 'pointer-events-none'}`}
        />
      )}
      {openImageLightbox && (
        <ImageLightBox
          image={{
            id: v4(),
            src: currentPhoto,
            width: currentPhotoDimensions.width,
            height: currentPhotoDimensions.height,
            actionType: ActionType.IMAGE_TO_COLOR,
          }}
          onClose={() => setOpenImageLightbox(false)}
          className={`${openImageLightbox ? 'pointer-events-auto' : 'pointer-events-none'}`}
        />
      )}
      <DialogTrigger asChild>
        <div
          className={cn(
            `group w-full overflow-hidden rounded-base border-2 border-border dark:border-dark-border bg-main font-base shadow-light dark:shadow-dark`,
            className,
          )}
        >
          <div className="relative w-full">
            <ImageWithSkeleton
              src={currentPhoto}
              width={photo.width}
              height={photo.height}
              alt={photo.description ?? ''}
              className="w-full h-auto object-cover rounded-base"
            />
            {/* <ImageWithSkeleton
              src={currentPhoto}
              fill
              alt={photo.description ?? ''}
              className="object-cover"
            /> */}
            <div className="absolute inset-0 flex flex-col justify-end p-3 text-white bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Text className="truncate text-white">{photo.description || 'Untitled'}</Text>
              <Text size="sm" variant="caption">
                by {photo.user?.name || 'Unknown'}
              </Text>
            </div>
          </div>
        </div>
      </DialogTrigger>
      <DialogContent
        className={`font-[Outfit] w-[90%] lg:w-[85%] xl:w-[80%] 2xl:w-[70%] z-401`}
        onInteractOutside={(e) => {
          if (openCropUI) {
            e.preventDefault()
          }
          if (openImageLightbox) {
            e.preventDefault()
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>Image Viewer</DialogTitle>
          <DialogDescription>Take a look at your photo!</DialogDescription>
        </DialogHeader>
        <GridContainer className="w-full gap-5">
          <GridItem colSpan={ColSpan.SPAN_6}>
            <FlexContainer direction={FlexDirection.COL} className="gap-2">
              {checkAspectRatio(currentPhotoDimensions.width, currentPhotoDimensions.height) && (
                <Alert variant="negative">
                  <FlexContainer align={AlignItems.CENTER} className="gap-4" wrap={false}>
                    <CircleAlert size="24px" className="shrink-0" />
                    <FlexContainer direction={FlexDirection.COL} className="grow">
                      <AlertTitle>Unsupported Aspect Ratio!</AlertTitle>
                      <AlertDescription>
                        {`We only support: 1:1, 2:3 and 3:2 Aspect Ratio. Please use our crop tool to enforce the correct aspect ratio~`}
                      </AlertDescription>
                    </FlexContainer>
                  </FlexContainer>
                </Alert>
              )}
              <div className="relative aspect-square w-full h-full">
                <ImageWithSkeleton
                  src={currentPhoto}
                  fill
                  alt={photo.description ?? ''}
                  blurDataURL={photo.blur_hash}
                  className="object-contain"
                  onClick={() => setOpenImageLightbox(true)}
                />
              </div>
              {/* <Button onClick={() => setOpenCropUI(true)}>Crop Image</Button> */}
            </FlexContainer>
          </GridItem>
          <GridItem colSpan={ColSpan.SPAN_6}>
            <FlexContainer direction={FlexDirection.COL}>
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 mb-2"
                justify={JustifyContent.START}
                align={AlignItems.CENTER}
              >
                <GridContainer className="gap-4">
                  <GridItem colSpan={ColSpan.SPAN_12}>
                    <FlexContainer
                      direction={FlexDirection.ROW}
                      align={AlignItems.CENTER}
                      className="gap-2"
                    >
                      <Title level={HeaderLevel.H4}>Image Metadata</Title>
                      <Badge variant="default">{modified ? 'Modified' : 'Original'}</Badge>
                    </FlexContainer>
                    <Text size="sm" variant="description">
                      Author:{' '}
                      <Link
                        className="text-accent2 hover:text-accent2-lighter underline"
                        href={`${photo.user.links.html}/?utm_source=ColorAria&utm_medium=referral`}
                        target="_blank"
                      >
                        {photo.user?.name ?? 'Anonymous'}
                      </Link>
                      {' on '}
                      <Link
                        className="text-accent2 hover:text-accent2-lighter underline"
                        href={`https://unsplash.com/?utm_source=ColorAria&utm_medium=referral`}
                        target="_blank"
                      >
                        Unsplash
                      </Link>
                    </Text>
                  </GridItem>
                  <GridItem colSpan={ColSpan.SPAN_6}>
                    <FlexContainer direction={FlexDirection.COL} className="gap-2">
                      <Label>Original Size:</Label>
                      <Text variant="description" size="sm">
                        {photo.width} x {photo.height}
                      </Text>
                    </FlexContainer>
                  </GridItem>
                  <GridItem colSpan={ColSpan.SPAN_6}>
                    <FlexContainer direction={FlexDirection.COL} className="gap-2">
                      <Label>Original Aspect Ratio:</Label>
                      <Text variant="description" size="sm">
                        {calculateAspectRatio(photo.width, photo.height)}
                      </Text>
                    </FlexContainer>
                  </GridItem>
                  <GridItem colSpan={ColSpan.SPAN_12}>
                    <FlexContainer direction={FlexDirection.COL} className="gap-2">
                      <Label>Description:</Label>
                      <Text variant="description" size="sm">
                        {photo.description || 'No description provided'}
                      </Text>
                    </FlexContainer>
                  </GridItem>
                </GridContainer>
              </FlexContainer>
              {modified && (
                <>
                  <FlexContainer
                    direction={FlexDirection.ROW}
                    align={AlignItems.CENTER}
                    className="gap-2"
                  >
                    <Title level={HeaderLevel.H4} className="mt-2 mb-2">
                      Modified Image Metadata
                    </Title>
                    <Button
                      size="sm"
                      round="round"
                      variant="negative"
                      onClick={() => handleRevert()}
                      disabled={!modified}
                    >
                      <Undo />
                      Revert
                    </Button>
                  </FlexContainer>
                  <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full mt-2">
                    <GridContainer className="gap-4 w-[50%]">
                      <GridItem colSpan={ColSpan.SPAN_6}>
                        <FlexContainer direction={FlexDirection.COL} className="gap-2">
                          <Label>Size:</Label>
                          <Text variant="description" size="sm">
                            {currentPhotoDimensions.width} x {currentPhotoDimensions.height}
                          </Text>
                        </FlexContainer>
                      </GridItem>
                      <GridItem colSpan={ColSpan.SPAN_6}>
                        <FlexContainer direction={FlexDirection.COL} className="gap-2">
                          <Label>Aspect Ratio:</Label>
                          <Text variant="description" size="sm">
                            {calculateAspectRatio(
                              currentPhotoDimensions.width,
                              currentPhotoDimensions.height,
                            )}
                          </Text>
                        </FlexContainer>
                      </GridItem>
                    </GridContainer>
                  </FlexContainer>
                </>
              )}

              <Title level={HeaderLevel.H4} className="mt-2 mb-2">
                Image Actions
              </Title>
              <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full">
                <FlexContainer direction={FlexDirection.ROW} className="gap-2" wrap={true}>
                  <Button onClick={() => setOpenCropUI(true)}>
                    <Crop className="mr-2" />
                    Crop Image
                  </Button>
                  <Button onClick={handleGenerateImage}>
                    <Palette className="mr-2" />
                    Generate Coloring Art
                  </Button>
                  {/* <Button onClick={handleSketchToImage}>
                    <DraftingCompass className="mr-2" />
                    Generate Sketch Art
                  </Button> */}
                  <Button onClick={handleBackgroundRemover}>
                    <ImageMinus className="mr-2" />
                    Remove Background
                  </Button>
                </FlexContainer>
              </FlexContainer>
            </FlexContainer>
          </GridItem>
        </GridContainer>
      </DialogContent>
    </Dialog>
  )
}

export default UnsplashImageCard
