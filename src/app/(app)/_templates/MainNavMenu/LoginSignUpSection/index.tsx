import { ClassValue } from 'clsx'
import { LogIn, CircleUser, LayoutDashboard } from 'lucide-react'
import Link from 'next/link'
import { BeatLoader } from 'react-spinners'
import AttentionGlowButton from '@/app/(app)/_component/Button/variants/AttentionGlowButton'
import { NavigationMenuLinkItem } from '@/app/(app)/_component/NavigationMenuV2'
import Text from '@/app/(app)/_component/Text'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { useAuthUser } from '@/app/(app)/_provider/AuthProvider'
import { cn } from '@/lib/utils'

interface Props {
  isCenter?: boolean
  className?: ClassValue
  nonLoggedClassName?: ClassValue
  loggedClassName?: ClassValue
}

function LoginSignUpSection(props: Props) {
  const { className, isCenter = false, loggedClassName, nonLoggedClassName } = props
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const { user, isRehydrated, isLoggedIn, isLoading } = useAuthUser()

  console.log(user)
  console.log('is user logged in?', isLoggedIn)

  const LoggedInSection = () => {
    return (
      <FlexContainer
        className={cn(`w-full gap-12`, className, loggedClassName)}
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        justify={isCenter ? JustifyContent.CENTER : JustifyContent.END}
      >
        <NavigationMenuLinkItem
          link="dashboard/image-generation"
          className="bg-white border-2"
          label={
            <FlexContainer
              direction={FlexDirection.ROW}
              className="gap-2"
              align={AlignItems.CENTER}
              wrap={false}
            >
              <LayoutDashboard />
              Dashboard
            </FlexContainer>
          }
        ></NavigationMenuLinkItem>
      </FlexContainer>
    )
  }

  const NonLoggedSection = () => {
    return (
      <FlexContainer
        className={cn(`w-full gap-12`, className, nonLoggedClassName)}
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        justify={isCenter ? JustifyContent.CENTER : JustifyContent.END}
      >
        <Link href={`${HOST_URL}/login`}>
          <AttentionGlowButton>
            <FlexContainer
              className="gap-2"
              direction={FlexDirection.ROW}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
            >
              <LogIn />
              {/* <Text variant="emphasis" className="text-white hover:text-gray-800 hover:underline"> */}
              Login
              {/* </Text> */}
            </FlexContainer>
          </AttentionGlowButton>
        </Link>
        {/* <Link href={`${HOST_URL}/register`}>
          <AttentionGlowButton>
            <FlexContainer
              className="gap-2"
              direction={FlexDirection.ROW}
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
            >
              <CircleUser />{' '}
              <Text variant="body" className="text-white">
                Sign Up
              </Text>
            </FlexContainer>
          </AttentionGlowButton>
        </Link> */}
      </FlexContainer>
    )
  }

  const LoggedOrNotLogged = () => {
    if (user && isLoggedIn) {
      if (isLoading) return <div>Loading...</div>
      return <LoggedInSection />
    } else {
      return <NonLoggedSection />
    }
  }

  return !isRehydrated ? (
    <FlexContainer
      className={`w-full h-full gap-12`}
      direction={FlexDirection.ROW}
      align={AlignItems.CENTER}
      justify={JustifyContent.END}
    >
      <BeatLoader />
    </FlexContainer>
  ) : (
    <LoggedOrNotLogged />
  )
}

export default LoginSignUpSection
