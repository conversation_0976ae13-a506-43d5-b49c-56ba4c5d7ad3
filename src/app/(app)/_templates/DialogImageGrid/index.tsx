'use client'

import NextImage from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { v4 } from 'uuid'
import { cn } from '@/lib/utils'
import { fileToBase64, getImageDimensions } from '@/utilities/local/image'
import { <PERSON><PERSON> } from '../../_component/Button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../_component/Dialog'
import NoImageIllustratedMessage from '../../_component/IllustratedMessage/NoImage'
import { Input } from '../../_component/Input'
import ClientPagination from '../../_component/Pagination/ClientPagination'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../_component/Tabs'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { useImagesPagination } from '../../_hooks/useImagesPagination'
import { ImageMetadata } from '../../_types/ImageMetadata'

interface Props {
  onImageSelected?: (image: ImageMetadata) => void
  isDefaultSelection?: boolean
  isGenerationHistorySelection?: boolean
  isUserSelection?: boolean
  pageSize?: number
}

function DialogImageGrid(props: Props) {
  const {
    onImageSelected,
    isDefaultSelection,
    isGenerationHistorySelection,
    isUserSelection,
    pageSize = 5,
  } = props

  const [openDialog, setOpenDialog] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [selectedImage, setSelectedImage] = useState<string>()
  const [numberOfTabs, setNumberOfTabs] = useState<string>('grid-cols-3')
  const [imageFile, setImageFile] = useState<ImageMetadata | null>(null)
  const [activeTab, setActiveTab] = useState<string>('generationHistory')
  const [currentPage, setCurrentPage] = useState(1)
  const [currentPageSize, setCurrentPageSize] = useState(pageSize)

  // Fetch images using the shared hook
  const { images, pagination, isLoading } = useImagesPagination({
    page: currentPage,
    pageSize: currentPageSize,
    enabled: openDialog && activeTab === 'generationHistory', // Only fetch when dialog is open and on generation history tab
  })

  useEffect(() => {
    let tabCount = 0
    if (isGenerationHistorySelection) tabCount++
    if (isDefaultSelection) tabCount++
    if (isUserSelection) tabCount++

    if (tabCount === 1) {
      setNumberOfTabs('grid-cols-1')
    } else if (tabCount === 2) {
      setNumberOfTabs('grid-cols-2')
    } else {
      setNumberOfTabs('grid-cols-3')
    }

    // Set default active tab
    if (isGenerationHistorySelection) {
      setActiveTab('generationHistory')
    } else if (isDefaultSelection) {
      setActiveTab('predefined')
    } else if (isUserSelection) {
      setActiveTab('byobImage')
    }
  }, [isGenerationHistorySelection, isDefaultSelection, isUserSelection])

  const handleImageSelection = (imageId: string) => {
    setSelectedImage(imageId)
  }

  const handleUserUploadImage = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const base64 = await fileToBase64(file)
      const dimensions = await getImageDimensions(base64)
      const imageMetadata: ImageMetadata = {
        id: v4(),
        src: base64,
        width: dimensions.w,
        height: dimensions.h,
        actionType: 'user-upload',
      }
      setImageFile(imageMetadata)
      setSelectedImage(imageMetadata.id)
    } catch (error) {
      console.error('Error processing uploaded image:', error)
    }
  }

  const handlePaginationChange = (page: number, newPageSize: number) => {
    if (newPageSize !== currentPageSize) {
      setCurrentPageSize(newPageSize)
      setCurrentPage(1) // Reset to page 1 when page size changes
    } else {
      setCurrentPage(page)
    }
  }

  const handleConfirmSelection = () => {
    let selectedImageData: ImageMetadata | undefined

    if (activeTab === 'generationHistory') {
      selectedImageData = images.find((img) => img.id === selectedImage)
    } else if (activeTab === 'byobImage' && imageFile) {
      selectedImageData = imageFile
    }

    if (selectedImageData && onImageSelected) {
      onImageSelected(selectedImageData)
    }

    cleanupOnOpenCloseDialog(false)
  }

  const cleanupOnOpenCloseDialog = (isOpen: boolean) => {
    if (!isOpen) {
      setSelectedImage('')
      setImageFile(null)
      setCurrentPage(1)
      setOpenDialog(false)
    } else {
      setOpenDialog(true)
    }
  }

  const ImageGrid = () => {
    return images?.map((image, index) => {
      const isActive = selectedImage === image.id
      const activeBorder = isActive
        ? 'border-accent2 shadow-accent2-lighter shadow-md'
        : 'border-black'
      return (
        <div
          key={index}
          className={`relative w-full aspect-square border-4 rounded cursor-pointer ${activeBorder}`}
        >
          <NextImage
            key={image.id}
            style={{
              objectFit: 'contain',
              borderRadius: '20px',
            }}
            src={image.src}
            fill
            alt={''}
            onClick={() => handleImageSelection(image.id)}
          />
        </div>
      )
    })
  }

  const DisplayGenerationHistory = () => {
    if (isLoading) {
      return (
        <FlexContainer
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
          className="w-full h-32"
        >
          <div>Loading images...</div>
        </FlexContainer>
      )
    }

    if (images.length === 0) {
      return (
        <FlexContainer
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
          className="w-full h-32"
        >
          <div>No images found. Try generating some images first!</div>
        </FlexContainer>
      )
    }

    return (
      <FlexContainer direction={FlexDirection.COL} className="gap-4">
        <ClientPagination
          totalCount={pagination.totalCount}
          currentPage={currentPage}
          currentPageSize={currentPageSize}
          setCurrentPageSize={(newPageSize) => {
            setCurrentPageSize(newPageSize)
            handlePaginationChange(1, newPageSize)
          }}
          setCurrentPage={handlePaginationChange}
        />
        <GridContainer
          ref={containerRef}
          className="w-full h-full overflow-auto gap-5 p-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
        >
          <ImageGrid />
        </GridContainer>
      </FlexContainer>
    )
  }

  return (
    <Dialog open={openDialog} onOpenChange={cleanupOnOpenCloseDialog}>
      <Button variant="positive" onClick={() => setOpenDialog(true)}>
        Select an Image
      </Button>
      <DialogContent className="font-[Outfit] w-[95%] md:w-[80%] lg:w-[70%] z-501 max-h-[90%] mx-auto">
        <DialogHeader>
          <DialogTitle>Image Gallery</DialogTitle>
          <DialogDescription>Select an image</DialogDescription>
        </DialogHeader>
        <Tabs value={activeTab} onValueChange={(val) => setActiveTab(val)}>
          <TabsList className={cn(`grid w-full grid-cols-3`, numberOfTabs)}>
            {isGenerationHistorySelection && (
              <TabsTrigger value="generationHistory">Generation History</TabsTrigger>
            )}
            {isDefaultSelection && <TabsTrigger value="predefined">Pre-defined Images</TabsTrigger>}
            {isUserSelection && <TabsTrigger value="byobImage">Bring your own image</TabsTrigger>}
          </TabsList>
          <TabsContent value="generationHistory" className="w-full">
            <div className="max-h-[70vh] overflow-y-auto p-4">
              <DisplayGenerationHistory />
            </div>
          </TabsContent>
          <TabsContent value="byobImage" className="w-full">
            <FlexContainer className="gap-2 w-full" direction={FlexDirection.COL}>
              <Input type="file" accept="image/*" onChange={handleUserUploadImage} />
              {imageFile !== null ? (
                <FlexContainer
                  className="relative w-full h-[500px] aspect-auto"
                  align={AlignItems.CENTER}
                  justify={JustifyContent.CENTER}
                >
                  <NextImage style={{ objectFit: 'contain' }} src={imageFile.src} fill alt={''} />
                </FlexContainer>
              ) : (
                <NoImageIllustratedMessage
                  className="w-[35%] h-[15%] mx-auto my-auto"
                  title="No image selected"
                  description="Upload your image and it'll appear here!"
                />
              )}
            </FlexContainer>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button variant="secondary" onClick={() => cleanupOnOpenCloseDialog(false)}>
            Cancel
          </Button>
          <Button variant="emphasis" onClick={handleConfirmSelection} disabled={!selectedImage}>
            Select Image
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default DialogImageGrid
