import { PropsWithChildren, ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import WaveBackgroundV2, { WaveBackgroundV2SVG } from '../../_component/Design/WaveBackgroundV2'
import Title, { HeaderLevel } from '../../_component/Title'
import FlexContainer, { AlignItems, FlexDirection } from '../../_cssComp/FlexContainer'

interface Props {
  title?: string
  value?: string
  icon?: ReactNode
  className?: string
}

function HighlightNumericCard(props: PropsWithChildren<Props>) {
  const { className, title, value, icon, children } = props
  return (
    <div
      style={{
        backgroundImage: `url("data:image/svg+xml,${encodeURIComponent(WaveBackgroundV2SVG())}")`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}
      className={cn(
        'w-full h-full border-black border-2 rounded-md shadow-[8px_8px_0px_rgba(0,0,0,1)] bg-white',
        className,
      )}
    >
      <FlexContainer
        direction={FlexDirection.ROW}
        className={`gap-2 ${Spacing.ContentPadding}`}
        align={AlignItems.CENTER}
      >
        {icon}
        <FlexContainer direction={FlexDirection.COL}>
          <Title level={HeaderLevel.H4}>{title}</Title>
          <Title>{value}</Title>
          <div className="text-left h-full">{children}</div>
        </FlexContainer>
      </FlexContainer>
    </div>
  )
}

export default HighlightNumericCard
