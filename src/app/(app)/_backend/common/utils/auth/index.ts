import * as jose from 'jose'
import { InvalidToken, TokenExpired } from '../../exception'

export interface JWTType extends jose.JWTPayload {
  id: string
  email: string
  firstName: string
  lastName: string
  role: string
  collection: string
  freeCredits: number
  paidCredits: number
  rolloverCredits: number
  iat: number
  exp: number
}

export async function _verifyJWT(token: string | undefined | null) {
  if (token == null) {
    throw new InvalidToken()
  }
  const secret = new TextEncoder().encode(process.env.PAYLOAD_HASHED_SECRET)

  try {
    const { payload } = await jose.jwtVerify(token, secret)
    return payload as JWTType
  } catch (error) {
    if (error instanceof jose.errors.JWTExpired) {
      throw new TokenExpired()
    }
    throw new InvalidToken()
  }
}
