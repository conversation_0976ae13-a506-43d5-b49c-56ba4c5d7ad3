import { ExternalServiceError } from '../../exception'

export async function backendApiFetch(url: string, options?: RequestInit, messageOnFail?: string) {
  try {
    const response = await fetch(url, options)
    if (!response.ok) {
      throw new ExternalServiceError(
        `${messageOnFail} Status Code: ${response.status}`,
        response.status,
      )
    }
    return response
  } catch (error) {
    throw new ExternalServiceError((error as Error).message)
  }
}
