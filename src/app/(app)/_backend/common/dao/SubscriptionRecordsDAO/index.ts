import { BasePayload } from 'payload'
import { SubscriptionRecord } from '@/payload-types'

class SubscriptionRecordsDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async addSubscription(
    subscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    customerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string | null,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    const result = await this.payload.create({
      collection: 'subscription-records',
      data: {
        subscriptionId: subscriptionId,
        user: userId,
        subscriptionPlan: subscriptionPlanId,
        customerId: customerId,
        status: status as SubscriptionRecord['status'],
        currentPeriodStart: new Date(Number(currentPeriodStart) * 1000).toISOString(),
        currentPeriodEnd: new Date(Number(currentPeriodEnd) * 1000).toISOString(),
        stripeComment: stripeComment,
        cancelAt: cancelAt ? new Date(Number(cancelAt) * 1000).toISOString() : null,
        canceledAt: canceledAt ? new Date(Number(canceledAt) * 1000).toISOString() : null,
      },
    })

    return result
  }

  async updateSubscriptionRecord(
    id: string,
    subscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    customerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string | null,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    const result = await this.payload.update({
      collection: 'subscription-records',
      id: id,
      data: {
        subscriptionId: subscriptionId,
        user: userId,
        subscriptionPlan: subscriptionPlanId,
        customerId: customerId,
        status: status as SubscriptionRecord['status'],
        currentPeriodStart: new Date(Number(currentPeriodStart) * 1000).toISOString(),
        currentPeriodEnd: new Date(Number(currentPeriodEnd) * 1000).toISOString(),
        stripeComment: stripeComment,
        cancelAt: cancelAt ? new Date(Number(cancelAt) * 1000).toISOString() : null,
        canceledAt: canceledAt ? new Date(Number(canceledAt) * 1000).toISOString() : null,
      },
    })

    return result
  }
}

export default SubscriptionRecordsDAO
