import { BasePayload } from 'payload'

class SubscriptionPlanDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getAllSubscriptionPlans() {
    const result = await this.payload.find({
      collection: 'subscription-plans',
    })

    return result
  }

  async getSubscriptionPlanById(id: string) {
    const result = await this.payload.findByID({
      collection: 'subscription-plans',
      id: id,
    })

    return result
  }

  async updateSubscriptionPlan(id: string, data: any) {
    const result = await this.payload.update({
      collection: 'subscription-plans',
      data: data,
      where: {
        id: {
          equals: id,
        },
      },
    })

    return result
  }

  async retrieveSubscriptionByName(
    subscriptionName: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.payload.find({
      collection: 'subscription-plans',
      where: {
        and: [
          {
            name: {
              equals: subscriptionName,
            },
          },
          {
            subscription_duration: {
              equals: subscriptionDuration,
            },
          },
        ],
      },
    })
    const resultData = result.docs[0]

    return resultData
  }

  async retrieveSubscriptionByProductId(
    productId: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.payload.find({
      collection: 'subscription-plans',
      where: {
        and: [
          {
            id: {
              equals: productId,
            },
          },
          {
            subscription_duration: {
              equals: subscriptionDuration,
            },
          },
        ],
      },
    })
    const resultData = result.docs[0]

    return resultData
  }

  async retrieveSubscriptionByPriceId(stripePriceId: string) {
    const result = await this.payload.find({
      collection: 'subscription-plans',
      where: {
        stripePriceId: {
          equals: stripePriceId,
        },
      },
    })
    const resultData = result.docs[0]
    return resultData
  }
}

export default SubscriptionPlanDAO
