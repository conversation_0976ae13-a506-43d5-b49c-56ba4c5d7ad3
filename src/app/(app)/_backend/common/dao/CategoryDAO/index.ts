import { BasePayload, Sort, Where } from 'payload'
import { Category } from '@/payload-types'

class CategoryDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getAllCategories() {
    const categories = await this.payload.find({
      collection: 'categories',
      pagination: false,
    })

    return categories
  }
}

export default CategoryDAO
