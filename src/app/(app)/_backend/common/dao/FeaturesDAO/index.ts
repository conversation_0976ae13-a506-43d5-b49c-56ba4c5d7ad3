import { BasePayload, Sort, Where } from 'payload'
import { Feature } from '@/payload-types'

class FeatureDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getSelectedFeatures(features: string[]) {
    const where: Where = {
      feature: {
        in: features.join(','),
      },
    }
    const response = await this.payload.find({
      collection: 'features',
      pagination: false,
      where,
    })

    return response
  }

  async getAllFeatures() {
    const response = await this.payload.find({
      collection: 'features',
      pagination: false,
    })

    return response.docs
  }
}

export default FeatureDAO
