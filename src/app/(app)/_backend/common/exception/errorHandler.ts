import { StatusCodes } from 'http-status-codes'
import { HTTPException } from '.'

// errorHandler.ts
export const errorHandler = async (err: unknown): Promise<Response> => {
  if (err instanceof HTTPException) {
    return new Response(JSON.stringify({ message: err.message }), {
      status: err.statusCode,
      headers: { 'Content-Type': 'application/json' },
    })
  }

  return new Response(JSON.stringify({ message: 'Internal Server Error' }), {
    status: StatusCodes.INTERNAL_SERVER_ERROR,
    headers: { 'Content-Type': 'application/json' },
  })
}
