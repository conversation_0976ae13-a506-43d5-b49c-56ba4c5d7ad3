import { PublicUser, SubscriptionPlan, SubscriptionRecord } from '@/payload-types'

export interface FilteredSubscriptionRecord {
  id: number
  currentPeriodStart: string
  currentPeriodEnd: string
  status:
    | 'active'
    | 'canceled'
    | 'past_due'
    | 'incomplete'
    | 'incomplete_expired'
    | 'trialing'
    | 'unpaid'
    | 'paused'
  subscriptionPlan: string | SubscriptionPlan | null | undefined
}

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  freeCredits: number
  paidCredits: number
  rolloverCredits: number
  subscriptionRecord: FilteredSubscriptionRecord | number | null | undefined
  role: PublicUser['role']
  verified: boolean
}

export interface UserWithAuth {
  exp?: number
  token?: string
  user: User
}

export class UserDTO {
  publicUser: PublicUser
  constructor(publicUser: PublicUser) {
    this.publicUser = publicUser
  }
  isSubscriptionRecord(value: number | SubscriptionRecord | null | undefined) {
    if (typeof value === 'object' && value !== null && 'id' in value) {
      return {
        id: value.id,
        currentPeriodStart: value.currentPeriodStart,
        currentPeriodEnd: value.currentPeriodEnd,
        status: value.status,
        subscriptionPlan: value.subscriptionPlan,
      }
    } else {
      return value
    }
  }
  distil(): User {
    return {
      id: this.publicUser.id,
      email: this.publicUser.email,
      firstName: this.publicUser.firstName,
      lastName: this.publicUser.lastName,
      freeCredits: this.publicUser.freeCredits,
      paidCredits: this.publicUser.paidCredits,
      rolloverCredits: this.publicUser.rolloverCredits,
      subscriptionRecord: this.isSubscriptionRecord(this.publicUser.subscription),
      role: this.publicUser.role,
      verified: this.publicUser.verified ?? false,
    }
  }
}
