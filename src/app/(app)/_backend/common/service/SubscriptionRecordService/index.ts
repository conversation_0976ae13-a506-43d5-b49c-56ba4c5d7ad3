import SubscriptionRecordsDAO from '../../dao/SubscriptionRecordsDAO'

class SubscriptionRecordService {
  private subscriptionRecordsDAO: SubscriptionRecordsDAO

  constructor(subscriptionRecordsDAO: SubscriptionRecordsDAO) {
    this.subscriptionRecordsDAO = subscriptionRecordsDAO
  }

  async addSubscription(
    subscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    customerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    return this.subscriptionRecordsDAO.addSubscription(
      subscriptionId,
      userId,
      subscriptionPlanId,
      customerId,
      status,
      currentPeriodStart,
      currentPeriodEnd,
      stripeComment,
      cancelAt,
      canceledAt,
    )
  }

  async updateSubscription(
    id: string,
    subscriptionId: string,
    userId: string,
    subscriptionPlanId: string,
    customerId: string,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    return this.subscriptionRecordsDAO.updateSubscriptionRecord(
      id,
      subscriptionId,
      userId,
      subscriptionPlanId,
      customerId,
      status,
      currentPeriodStart,
      currentPeriodEnd,
      stripeComment,
      cancelAt,
      canceledAt,
    )
  }
}

export default SubscriptionRecordService
