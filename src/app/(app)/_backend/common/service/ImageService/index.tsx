import { StatusCodes } from 'http-status-codes'
import sharp from 'sharp'
import { v4 } from 'uuid'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import { s3Client } from '@/lib/s3-client'
import { GeneratedImageStatus } from '@/types/GeneratedImageStatus'
import { TaskType } from '@/types/TaskType'
import { b64toBlob, getMimeTypeFromBase64 } from '@/utilities/local/image'
import { HTTPException } from '../../exception'

type Quality = 'LOW_QUALITY' | 'HIGH_QUALITY'
export type T2IAspectRatio = '1:1' | '2:3' | '3:2'

interface ImageMetadata {
  image_id: string
  width: number
  height: number
  size: number
}

interface ImagesResponse {
  images: ImageMetadata[]
}

class ImageService {
  private UPSCALE_CAP = 2
  private IMAGE_REPETITION_CAP = 3

  private ASPECT_RATIO_MAP = {
    LOW_QUALITY: {
      '1:1': {
        width: 512,
        height: 512,
      },
      '2:3': {
        width: 512,
        height: 768,
      },
      '3:2': {
        width: 768,
        height: 512,
      },
    },
    HIGH_QUALITY: {
      '1:1': {
        width: 1024,
        height: 1024,
      },
      '2:3': {
        width: 1024,
        height: 1536,
      },
      '3:2': {
        width: 1536,
        height: 1024,
      },
    },
  }

  private generatedImageDao: GeneratedImageDAO

  constructor(generatedImageDao: GeneratedImageDAO) {
    this.generatedImageDao = generatedImageDao
  }

  async upscaleImage(imageB64: string, upscale: number, userId: string) {
    const imageId = v4()
    await this.generatedImageDao.createGeneratingEntries([imageId], TaskType.UPSCALE, userId)
    const imageUrl = await s3Client.generateUploadUrl(imageId, undefined, `${userId}/`)

    try {
      if (upscale > this.UPSCALE_CAP) {
        throw new HTTPException('Upscale value exceeds allowance', StatusCodes.BAD_REQUEST)
      }

      const mimeType = getMimeTypeFromBase64(imageB64)
      const base64Data = imageB64.replace(/^data:.*?;base64,/, '')
      const imageBuffer = Buffer.from(base64Data, 'base64')

      const metadata = await sharp(imageBuffer).metadata()
      const width = metadata.width ?? 0
      const height = metadata.height ?? 0

      const newWidth = Math.round(width * upscale)
      const newHeight = Math.round(height * upscale)

      const newImage = await sharp(imageBuffer)
        .resize({ width: newWidth, height: newHeight })
        .png({ palette: true })
        .toBuffer()

      // upload image to r2
      fetch(imageUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': mimeType,
        },
        body: newImage,
      })

      const imagesResponse: ImagesResponse = {
        images: [
          {
            image_id: imageId,
            width: newWidth,
            height: newHeight,
            size: newImage.length,
          },
        ],
      }
      this._handleTaskResponse(imagesResponse)
      return
    } catch (error) {
      await this.generatedImageDao.updateEntriesStatus([imageId], GeneratedImageStatus.FAILED)
      throw new HTTPException((error as Error).message, StatusCodes.INTERNAL_SERVER_ERROR)
    }
  }

  async contrastImage(imageB64: string, userId: string) {
    const imageId = v4()
    await this.generatedImageDao.createGeneratingEntries([imageId], TaskType.CONTRAST, userId)
    const imageUrl = await s3Client.generateUploadUrl(imageId, undefined, `${userId}/`)

    try {
      const mimeType = getMimeTypeFromBase64(imageB64)
      const base64Data = imageB64.replace(/^data:.*?;base64,/, '')
      const imageBuffer = Buffer.from(base64Data, 'base64')

      const metadata = await sharp(imageBuffer).metadata()
      const width = metadata.width ?? 0
      const height = metadata.height ?? 0

      const newImage = await sharp(imageBuffer).png({ palette: true }).toBuffer()

      // upload image to r2
      fetch(imageUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': mimeType,
        },
        body: newImage,
      })

      const imagesResponse: ImagesResponse = {
        images: [
          {
            image_id: imageId,
            width: width,
            height: height,
            size: newImage.length,
          },
        ],
      }
      this._handleTaskResponse(imagesResponse)
      return
    } catch (error) {
      await this.generatedImageDao.updateEntriesStatus([imageId], GeneratedImageStatus.FAILED)
      throw new HTTPException((error as Error).message, StatusCodes.INTERNAL_SERVER_ERROR)
    }
  }

  async textToImage(
    prompt: string,
    aspectRatio: T2IAspectRatio,
    quality: Quality,
    numberOfImages: number,
    userId: string,
  ) {
    const imageIds: string[] = []
    const imageUrls: string[] = []

    if (numberOfImages > this.IMAGE_REPETITION_CAP || numberOfImages <= 0) {
      throw new HTTPException(
        `Number of images must be less than or equal to ${this.IMAGE_REPETITION_CAP}`,
        StatusCodes.BAD_REQUEST,
      )
    }

    for (let i = 0; i < numberOfImages; i++) {
      const imageId = `${v4()}`
      imageIds.push(imageId)
      imageUrls.push(await s3Client.generateUploadUrl(imageId, undefined, `${userId}/`))
    }

    if (await this._hasGeneratingTask(userId)) {
      throw new HTTPException(
        'A generation task is already in progress for this user.',
        StatusCodes.CONFLICT,
      )
    }

    await this.generatedImageDao.createGeneratingEntries(
      imageIds,
      TaskType.TEXT_TO_IMAGE,
      userId,
      prompt,
    )

    const { width, height } = this.ASPECT_RATIO_MAP[quality][aspectRatio]
    const response = await fetch(`${process.env.MODAL_URL}/t2i`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Authorization: `Bearer ${process.env.MODAL_API_KEY}`,
      },
      body: JSON.stringify({
        prompt: prompt,
        width: width,
        height: height,
        batch_size: numberOfImages,
        image_urls: imageUrls,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      await this.generatedImageDao.updateEntriesStatus(imageIds, GeneratedImageStatus.FAILED)
      throw new HTTPException(
        `Error occured during t2i image generation! status: ${response.status}, message: ${errorText}`,
        response.status,
      )
    }

    const imagesResponse: ImagesResponse = await response.json()
    this._handleTaskResponse(imagesResponse)

    return
  }

  async imageToImage(
    prompt: string,
    imageBase64: string,
    aspectRatio: T2IAspectRatio,
    quality: Quality,
    numberOfImages: number,
    userId: string,
  ) {
    if (!prompt) throw new HTTPException('Prompt is required', StatusCodes.BAD_REQUEST)
    if (!imageBase64) throw new HTTPException('Image is required', StatusCodes.BAD_REQUEST)
    if (numberOfImages > this.IMAGE_REPETITION_CAP || numberOfImages <= 0) {
      throw new HTTPException(
        `Number of images must be less than or equal to ${this.IMAGE_REPETITION_CAP}`,
        StatusCodes.BAD_REQUEST,
      )
    }

    const imageIds: string[] = []
    const imageUrls: string[] = []
    for (let i = 0; i < numberOfImages; i++) {
      const imageId = `${v4()}`
      imageIds.push(imageId)
      imageUrls.push(await s3Client.generateUploadUrl(imageId, undefined, `${userId}/`))
    }

    if (await this._hasGeneratingTask(userId)) {
      throw new HTTPException(
        'A generation task is already in progress for this user.',
        StatusCodes.CONFLICT,
      )
    }

    await this.generatedImageDao.createGeneratingEntries(
      imageIds,
      TaskType.IMAGE_TO_IMAGE,
      userId,
      prompt,
    )

    const { width, height } = this.ASPECT_RATIO_MAP[quality][aspectRatio]
    const mimeType = getMimeTypeFromBase64(imageBase64)
    const base64Data = imageBase64.replace(/^data:.*?;base64,/, '')
    const imageBlob = b64toBlob(base64Data, mimeType)

    const form = new FormData()
    form.append('image', imageBlob, 'input_image')
    form.append('prompt', prompt)
    form.append('width', width.toString())
    form.append('height', height.toString())
    form.append('image_urls', imageUrls.join(','))
    form.append('batch_size', numberOfImages.toString())

    const response = await fetch(`${process.env.MODAL_URL}/i2i`, {
      method: 'POST',
      body: form,
    })

    if (!response.ok) {
      const errorText = await response.text()
      await this.generatedImageDao.updateEntriesStatus(imageIds, GeneratedImageStatus.FAILED)
      throw new HTTPException(
        `Error occured during i2i image generation! status: ${response.status}, message: ${errorText}`,
      )
    }

    const imagesResponse: ImagesResponse = await response.json()
    this._handleTaskResponse(imagesResponse)

    return
  }

  async removeBackground(imageBase64: string, aspectRatio: T2IAspectRatio, userId: string) {
    // const { width, height } = this.ASPECT_RATIO_MAP[quality][aspectRatio]

    if (await this._hasGeneratingTask(userId)) {
      throw new HTTPException(
        'A generation task is already in progress for this user.',
        StatusCodes.CONFLICT,
      )
    }

    const imageIds: string[] = []
    const imageId = `${v4()}`
    imageIds.push(imageId)
    const imageUrl = await s3Client.generateUploadUrl(imageId, undefined, `${userId}/`)

    await this.generatedImageDao.createGeneratingEntries(
      imageIds,
      TaskType.REMOVE_BACKGROUND,
      userId,
    )

    const form = new FormData()
    //Need to grab the mimetype from the image
    const mimeType = getMimeTypeFromBase64(imageBase64)
    const base64Data = imageBase64.replace(/^data:.*?;base64,/, '')
    const imageBlob = b64toBlob(base64Data, mimeType)
    form.append('image', imageBlob, 'image.png')
    form.append('image_url', imageUrl)

    const response = await fetch(`${process.env.MODAL_URL}/rmbg`, {
      method: 'POST',
      body: form,
    })

    if (!response.ok) {
      const errorText = await response.text()
      await this.generatedImageDao.updateEntriesStatus(imageIds, GeneratedImageStatus.FAILED)
      throw new HTTPException(
        `Error occured during rmbg image generation! status: ${response.status}, message: ${errorText}`,
      )
    }

    const imagesResponse: ImagesResponse = await response.json()
    this._handleTaskResponse(imagesResponse)

    return
  }

  async _handleTaskResponse(imagesResponse: ImagesResponse) {
    for (const image of imagesResponse.images) {
      this.generatedImageDao.updateCompletedImage(image.image_id, image.width, image.height)
    }
  }

  /**
   * Checks if the user has any images currently in 'generating' status
   */
  private async _hasGeneratingTask(userId: string): Promise<boolean> {
    return this.generatedImageDao.hasGeneratingTask(userId)
  }
}

export default ImageService
