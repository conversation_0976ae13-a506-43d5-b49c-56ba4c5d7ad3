import { StatusCodes } from 'http-status-codes'
import { Feature, Scope, SubscriptionPlan, SubscriptionRecord } from '@/payload-types'
import { FeaturesType } from '@/utilities/local/enums'
import FeatureDAO from '../../dao/FeaturesDAO'
import UserDAO from '../../dao/UserDAO'
import { HTTPException, OutOfScopeError } from '../../exception'

class FeatureScopeService {
  private userDAO: UserDAO
  private featuresDAO: FeatureDAO
  private defaultFeatures = [
    FeaturesType.TEXT_TO_COLOR,
    FeaturesType.TEXT_TO_COLOR_IMAGE_REPETITION,
  ]

  constructor(userDAO: UserDAO, featureDAO: FeatureDAO) {
    this.featuresDAO = featureDAO
    this.userDAO = userDAO
  }

  async retrieveUserFeatures(userId: string): Promise<string[]> {
    //User (0) -> SusbcriptionRecord (1) -> SubscriptionPlan (2) -> Scope (3) -> Features (4)
    try {
      const user = await this.userDAO.getUserById(userId, 4)

      const subscriptionRecord = user.subscription as SubscriptionRecord | null
      const isSubscriptionActive = subscriptionRecord?.status === 'active'

      const subscriptionPlan = subscriptionRecord?.subscriptionPlan as SubscriptionPlan | null
      const scope = subscriptionPlan?.scope as Scope | null
      const features = scope?.features as Feature[] | undefined

      if (!Array.isArray(features) || features.length === 0 || !isSubscriptionActive) {
        return this.defaultFeatures
      }

      return features.map((feature) => feature.feature)
    } catch (error) {
      console.error('Error retrieving user features:', error)
      throw new HTTPException('Failed to retrieve user features', StatusCodes.INTERNAL_SERVER_ERROR)
    }
  }

  async retrieveRelevantFeatures(features: string[]): Promise<Feature[]> {
    const relevantFeatures = await this.featuresDAO.getSelectedFeatures(features)
    return relevantFeatures.docs
  }

  async retrieveAllFeatures(): Promise<Feature[]> {
    const allFeatures = await this.featuresDAO.getAllFeatures()
    return allFeatures
  }

  async _validateFeatureMatch(userId: string, featuresToUse: string[]) {
    const userFeatures = await this.retrieveUserFeatures(userId)
    const areBothEqual = featuresToUse.every((feature) => userFeatures.includes(feature))
    return areBothEqual
  }

  async costTabulation(userId: string, featuresToUse: string[]) {
    const validateUserFeatures = await this._validateFeatureMatch(userId, featuresToUse)

    if (!validateUserFeatures) {
      throw new OutOfScopeError()
    }

    const relevantFeatures = await this.retrieveRelevantFeatures(featuresToUse)
    const featuresToUseSet = new Set(featuresToUse)
    const relevantFeaturesSet = new Set(relevantFeatures.map((f) => f.feature))

    if (
      featuresToUseSet.size !== relevantFeaturesSet.size ||
      [...featuresToUseSet].some((f) => !relevantFeaturesSet.has(f))
    ) {
      throw new HTTPException('Features mismatch', StatusCodes.BAD_REQUEST)
    }

    const costTabulation = relevantFeatures.reduce((acc, feature) => acc + feature.creditCost, 0)
    return costTabulation
  }
}

export default FeatureScopeService
