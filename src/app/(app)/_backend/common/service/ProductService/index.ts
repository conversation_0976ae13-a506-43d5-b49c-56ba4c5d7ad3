import SubscriptionPlanDAO from '../../dao/SubscriptionPlanDAO'

class ProductService {
  private SubscriptionPlanDAO: SubscriptionPlanDAO

  constructor(SubscriptionPlanDAO: SubscriptionPlanDAO) {
    this.SubscriptionPlanDAO = SubscriptionPlanDAO
  }

  async getAllSubscriptionPlans() {
    const subscriptionPlans = await this.SubscriptionPlanDAO.getAllSubscriptionPlans()
    return subscriptionPlans.docs
  }
}

export default ProductService
