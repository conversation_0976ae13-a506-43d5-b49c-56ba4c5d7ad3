import SubscriptionPlanDAO from '../../dao/SubscriptionPlanDAO'
import { NotFound } from '../../exception'

class SubscriptionPlanService {
  private subscriptionPlanDAO: SubscriptionPlanDAO

  constructor(subscriptionPlanDAO: SubscriptionPlanDAO) {
    this.subscriptionPlanDAO = subscriptionPlanDAO
  }

  async getAllSubscriptionPlans() {
    const subscriptionPlans = await this.subscriptionPlanDAO.getAllSubscriptionPlans()
    return subscriptionPlans.docs
  }

  async getSubscriptionPlanById(id: string) {
    const subscriptionPlan = await this.subscriptionPlanDAO.getSubscriptionPlanById(id)
    if (subscriptionPlan == null) {
      throw new NotFound('Subscription plan not found')
    }
    return subscriptionPlan
  }

  async updateSubscriptionPlan(id: string, data: any) {
    const result = await this.subscriptionPlanDAO.updateSubscriptionPlan(id, data)
    return result
  }

  async retrieveSubscriptionByName(
    subscriptionName: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByName(
      subscriptionName,
      subscriptionDuration,
    )
    if (result == null) {
      throw new NotFound('Subscription by subscriptionName is not found')
    }
    return result
  }

  async retrieveSubscriptionByProductId(
    productId: string,
    subscriptionDuration: string, //year / month
  ) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByProductId(
      productId,
      subscriptionDuration,
    )
    return result
  }

  async retrieveSubscriptionByPriceId(priceId: string) {
    const result = await this.subscriptionPlanDAO.retrieveSubscriptionByPriceId(priceId)
    if (result == null) {
      throw new NotFound('Subscription cannot be found')
    }
    return result
  }
}

export default SubscriptionPlanService
