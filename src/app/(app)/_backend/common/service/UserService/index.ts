import { StatusCodes } from 'http-status-codes'
import * as <PERSON><PERSON><PERSON><PERSON> from 'otpauth'
import { SendEmailOptions } from 'payload'
import crypto from 'crypto'
import { DEFAULT_FREE_PLAN } from '@/app/(app)/pricing/subscriptionMap'
import { mailgun } from '@/lib/mailgun'
import { PublicUser, SubscriptionPlan, SubscriptionRecord } from '@/payload-types'
import { generateOTPHTMLTemplate } from '@/utilities/emailTemplates/minifiedOtpHtmlTemplate'
import UserDAO from '../../dao/UserDAO'
import { SubscriptionPlanDTO } from '../../dto/subscriptionPlan/SubscriptionPlanDTO'
import { User, UserDTO } from '../../dto/user/UserDTO'
import {
  FingerprintMatchUser,
  HTTPException,
  NotEnoughCredits,
  UnauthenticatedUser,
  UnauthorizedUser,
  UserAlreadyExists,
  UserNotFound,
} from '../../exception'
import { backendApiFetch } from '../../utils/api/apiHelper'
import { _verifyJWT } from '../../utils/auth'

/**
 * UserService class
 * Register a new user
 *  - email/password flow (need email verification)
 *  - Google OAuth flow (no need email verification)
 * Login a user
 *  - email/password flow
 *  - Google OAuth flow
 * Get user info
 *  - Get user info by email
 *  - Get user info by id
 * Update a user?
 */

interface TokenRefreshResponse {
  message: string
  refreshedToken: string
  exp: number
  user: {
    email: string
    id: string
    collection: string
  }
}

class UserService {
  private userDAO: UserDAO
  private NEW_USER_FREE_CREDIT = 4
  private OTP_COOLDOWN_SECONDS = 55

  constructor(userDAO: UserDAO) {
    this.userDAO = userDAO
  }

  async register(
    email: string,
    firstName: string,
    lastName: string,
    oauth?: boolean,
    password?: string,
    networkFingerprintHash?: string,
    fingerprintHash?: string,
  ) {
    const users = await this.userDAO.getUsersByEmailId(email)
    if (users.docs.length > 0) {
      if (users.docs[0].verified) {
        throw new UserAlreadyExists()
      } else {
        // user created but not verified, resend verification email
        await this.sendVerificationEmail(users.docs[0].id, email)
        return new UserDTO(users.docs[0]).distil()
      }
    }

    const checkIfUserExistWithFingerprint =
      await this.userDAO.fetchUserWithBrowserAndNetworkFingerprint(
        fingerprintHash,
        networkFingerprintHash,
      )
    if (checkIfUserExistWithFingerprint.length > 0) {
      throw new FingerprintMatchUser()
    }

    let newUser: PublicUser
    if (oauth) {
      newUser = await this._oauthRegister(
        email,
        firstName,
        lastName,
        networkFingerprintHash,
        fingerprintHash,
      )
    } else {
      newUser = await this._emailRegister(
        email,
        firstName,
        lastName,
        password as string,
        networkFingerprintHash,
        fingerprintHash,
      )
    }

    const userDTO = new UserDTO(newUser).distil()
    return userDTO
  }

  async _oauthRegister(
    email: string,
    firstName: string,
    lastName: string,
    networkFingerprintHash?: string,
    fingerprintHash?: string,
  ) {
    const randomPassword = crypto.randomBytes(20).toString('hex')
    const newUser = await this.userDAO.createNewUser(
      email,
      randomPassword,
      firstName,
      lastName,
      this.NEW_USER_FREE_CREDIT,
      0,
      0,
      'free',
      true,
      '',
      true,
      networkFingerprintHash,
      fingerprintHash,
    )
    return newUser
  }

  async _generateVerificationToken() {
    const secret = new OTPAuth.Secret({ size: 20 })
    return secret.base32
  }

  async _generateOtp(secret: string) {
    const totp = new OTPAuth.TOTP({
      algorithm: 'SHA512',
      digits: 6,
      period: 120,
      secret: secret,
    })
    const token = totp.generate()
    return token
  }

  async _sendVerificationEmail(userId: string, recipient: string, token: string) {
    const currentOtpSent = await this.userDAO.fetchLastOtpSent(userId)
    console.log('curent otp sent', currentOtpSent)
    if (currentOtpSent) {
      const lastSent = new Date(currentOtpSent).getTime()
      const now = new Date().getTime()
      if (now - lastSent < this.OTP_COOLDOWN_SECONDS * 1000) {
        throw new HTTPException(
          `Please wait a while before sending another OTP.`,
          StatusCodes.TOO_MANY_REQUESTS,
        )
      }
    }
    const emailOptions: SendEmailOptions = {
      to: recipient,
      from: '<EMAIL>',
      subject: 'Welcome to ColorAria!',
      // text: `Thank you very much for your registration! Your OTP code is: ${token}. Start your journey with ColorAria today!`,
      html: generateOTPHTMLTemplate(token),
    }
    mailgun.sendEmail(emailOptions)
    await this.userDAO.genericUpdateUser(userId, {
      lastOtpSent: new Date().toISOString(),
    })
  }

  async sendVerificationEmail(userId: string, email: string) {
    const verificationToken = await this.userDAO.getUserVerificationToken(userId)
    const token = await this._generateOtp(verificationToken as string)
    await this._sendVerificationEmail(userId, email, token)
  }

  async _emailRegister(
    email: string,
    firstName: string,
    lastName: string,
    password: string,
    ipAddress?: string,
    fingerprintHash?: string,
  ) {
    const verificationToken = await this._generateVerificationToken()
    const newUser = await this.userDAO.createNewUser(
      email,
      password,
      firstName,
      lastName,
      this.NEW_USER_FREE_CREDIT,
      0,
      0,
      'free',
      false,
      verificationToken,
      false,
      ipAddress,
      fingerprintHash,
    )

    const token = await this._generateOtp(verificationToken)
    await this._sendVerificationEmail(newUser.id, email, token)
    return newUser
  }

  async verifyOTPCode(userId: string, code: string) {
    const secret = await this.userDAO.getUserVerificationToken(userId)

    if (!secret) {
      throw new UnauthorizedUser()
    }

    try {
      const totp = new OTPAuth.TOTP({
        algorithm: 'SHA512',
        digits: 6,
        period: 120,
        secret: secret,
      })
      const isValid = totp.validate({ token: code, window: 1 })
      console.log(`OTP ${code} is valid: ${isValid}`)
      return isValid !== null
    } catch (err) {
      // Possible errors: malformed token, verification failed
      console.error('OTP verification error:', err)
      return false
    }
  }

  async loginUser(email: string, password: string) {
    try {
      const userLogin = await this.userDAO.loginUser(email, password)
      return userLogin
    } catch (error) {
      throw new UnauthenticatedUser()
    }
  }

  async verifyUser(token: string | undefined | null) {
    const verified = await _verifyJWT(token)
    return verified
  }

  async getUserByEmailId(email: string) {
    const user = await this.userDAO.getUsersByEmailId(email)
    if (user.docs.length === 0) {
      return null
    } else if (user.docs.length > 1) {
      throw new Error('Multiple users found with the same email')
    }
    const userDTO = new UserDTO(user.docs[0]).distil()
    return userDTO
  }

  async getUserById(userId: string, depth = 0) {
    const user = await this.userDAO.getUserById(userId, depth)
    if (!user) {
      throw new UserNotFound()
    }
    const userDTO = new UserDTO(user).distil()
    return userDTO
  }

  //Will be updated in the future with more fields if necessary
  async updateUser(id: string, firstName: string, lastName: string) {
    const getUser = await this.userDAO.updateUserOnUser(id, firstName, lastName)
    const userDTO = new UserDTO(getUser).distil()
    return userDTO
  }

  async updateUserSubscriptionRecord(
    id: string,
    subscriptionRecordId: number,
    plan: 'admin' | 'editor' | 'subscriber' | 'free' | undefined,
    credit: number,
  ) {
    const getUser = await this.userDAO.genericUpdateUser(id, {
      subscription: subscriptionRecordId,
      role: plan,
      freeCredits: credit,
    })
    const userDTO = new UserDTO(getUser).distil()
    return userDTO
  }

  async deductCredit(userId: string, credit: number) {
    const fetchUser = await this.userDAO.getUserById(userId)
    if (!fetchUser) {
      throw new UserNotFound()
    }
    //For now we only look at FREE CREDITS.
    const userCredits = fetchUser.freeCredits
    if (userCredits < credit) {
      throw new NotEnoughCredits()
    }
    const remainderCredits = userCredits - credit
    const getUser = await this.userDAO.genericUpdateUser(userId, {
      freeCredits: remainderCredits,
    })
    const userDTO = new UserDTO(getUser).distil()
    return userDTO
  }

  async refreshToken() {
    const headers = {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    } as RequestInit
    const response = await backendApiFetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/public-users/refresh-token`,
      headers,
      'Something went wrong refreshing token.',
    )
    const result = (await response.json()) as TokenRefreshResponse
    return result
  }

  async getUserWithSubscriptionPlan(userId: string) {
    const user = await this.userDAO.getUserById(userId, 2)
    if (!user) {
      throw new UserNotFound()
    }

    if (user.role === 'free') {
      return {
        user: new UserDTO(user).distil(),
        subscriptionPlan: DEFAULT_FREE_PLAN,
        subscriptionRecord: {
          startDate: null,
          endDate: null,
          status: 'unpaid',
        },
      }
    }

    const subscriptionRecord = user.subscription as SubscriptionRecord | null | undefined
    if (!subscriptionRecord) {
      throw new HTTPException('Subscription record not found for user.', 404)
    }

    const subscriptionPlan = subscriptionRecord?.subscriptionPlan as
      | SubscriptionPlan
      | null
      | undefined
    if (!subscriptionPlan) {
      throw new HTTPException('Subscription plan not found for user.', 404)
    }

    const minimalInfoSubscriptionPlan = new SubscriptionPlanDTO(subscriptionPlan).minimalInfo()

    const result = {
      user: new UserDTO(user).distil(),
      subscriptionPlan: minimalInfoSubscriptionPlan,
      subscriptionRecord: {
        startDate: subscriptionRecord.currentPeriodStart,
        endDate: subscriptionRecord.currentPeriodEnd,
        status: subscriptionRecord.status,
      },
    }

    return result
  }
}

export default UserService
