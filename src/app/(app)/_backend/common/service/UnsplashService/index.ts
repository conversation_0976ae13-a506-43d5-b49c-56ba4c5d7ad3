import { StatusCodes } from 'http-status-codes'
import { SearchUnsplashPhotosResponse, UnsplashPhoto } from './types'
import { ExternalServiceError } from '../../exception'
import { backendApiFetch } from '../../utils/api/apiHelper'

class UnsplashService {
  private accessKey = process.env.UNSPLASH_ACCESS_KEY
  private baseUrl = 'https://api.unsplash.com'

  async getPhotos(page: number, perPage: number): Promise<UnsplashPhoto[]> {
    const url = `${this.baseUrl}/photos?page=${page}&per_page=${perPage}`
    const headers = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Client-ID ${this.accessKey}`,
      },
    } as RequestInit

    const response = await backendApiFetch(url, headers, 'Something went wrong fetching photos.')
    const result = await response.json()

    return result
  }

  async getPhoto(id: string): Promise<UnsplashPhoto> {
    const url = `${this.baseUrl}/photos/${id}`
    const headers = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Client-ID ${this.accessKey}`,
      },
    } as RequestInit

    const response = await backendApiFetch(
      url,
      headers,
      `Something went wrong fetching a photo id: ${id}`,
    )
    const result = await response.json()
    return result
  }

  async searchPhotos(
    query: string,
    page: number,
    perPage: number,
    orderBy: string,
    orientation?: string,
  ): Promise<SearchUnsplashPhotosResponse> {
    const buildQuery = () => {
      if (query.trim() === '') {
        throw new ExternalServiceError('Query cannot be an empty string', StatusCodes.BAD_REQUEST)
      }

      let url = `${this.baseUrl}/search/photos?query=${query}&page=${page}&per_page=${perPage}&order_by=${orderBy}`
      if (orientation || orientation !== '') {
        url += `&orientation=${orientation}`
      }
      return url
    }
    const url = buildQuery()
    const headers = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Client-ID ${this.accessKey}`,
      },
    } as RequestInit

    const response = await backendApiFetch(
      url,
      headers,
      `Something went wrong searching photos with query: ${query}`,
    )
    const result = await response.json()
    return result
  }

  async attributionLink(attributionId: string) {
    //example; "https://api.unsplash.com/photos/fJ8gSvKDHUo/download?ixid=M3w3MzY2NjJ8MHwxfHNlYXJjaHwxOXx8QW5pbWFsc3xlbnwwfHx8fDE3NDg2NzQ3NTl8MA"
    const headers = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Client-ID ${this.accessKey}`,
      },
    } as RequestInit
    const res = await backendApiFetch(
      `https://api.unsplash.com/photos/${attributionId}`,
      headers,
      'Something went wrong getting attribution link.',
    )
    const result = await res.json()
    return result
  }
}

export default UnsplashService
