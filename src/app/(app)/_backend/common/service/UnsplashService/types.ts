export interface SearchUnsplashPhotosResponse {
  total: number
  total_pages: number
  results: UnsplashPhoto[]
}

export interface UnsplashPhoto {
  id: string
  created_at: string
  updated_at: string
  width: number
  height: number
  color: string
  blur_hash: string
  likes: number
  liked_by_user: boolean
  description: string | null
  user: UnsplashUser
  current_user_collections: UnsplashUserCollection[]
  urls: UnsplashPhotoUrls
  links: UnsplashPhotoLinks
}

export interface UnsplashUser {
  id: string
  username: string
  name: string
  portfolio_url: string | null
  bio: string | null
  location: string | null
  total_likes: number
  total_photos: number
  total_collections: number
  instagram_username: string | null
  twitter_username: string | null
  profile_image: UnsplashUserProfileImage
  links: UnsplashUserLinks
}

export interface UnsplashUserProfileImage {
  small: string
  medium: string
  large: string
}

export interface UnsplashUserLinks {
  self: string
  html: string
  photos: string
  likes: string
  portfolio: string
}

export interface UnsplashUserCollection {
  id: number
  title: string
  published_at: string
  last_collected_at: string
  updated_at: string
  cover_photo: null // If needed, can be `UnsplashPhoto | null`
  user: null // If needed, can be `UnsplashUser | null`
}

export interface UnsplashPhotoUrls {
  raw: string
  full: string
  regular: string
  small: string
  thumb: string
}

export interface UnsplashPhotoLinks {
  self: string
  html: string
  download: string
  download_location: string
}
