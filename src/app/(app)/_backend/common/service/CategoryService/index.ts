import CategoryDAO from '../../dao/CategoryDAO'
import { CategoryDTO } from '../../dto/category/CategoryDTO'

class CategoryService {
  private categoryDAO: CategoryDAO

  constructor(categoryDAO: CategoryDAO) {
    this.categoryDAO = categoryDAO
  }

  async getAllCategories() {
    const categories = await this.categoryDAO.getAllCategories()
    const categoryDTO = new CategoryDTO(categories).convertToArray()
    return categoryDTO
  }
}

export default CategoryService
