import { Category, Post } from '@/payload-types'
import PostDAO from '../../dao/PostDAO'
import { InvalidSlug } from '../../exception'

class PostService {
  private postDAO: PostDAO

  constructor(postDAO: PostDAO) {
    this.postDAO = postDAO
  }

  _constructWhere(queryParam: Record<string, unknown>) {
    const where: Record<string, any> = {
      or: [],
    }
    Object.keys(queryParam).forEach((key: string) => {
      if (typeof queryParam[key] === 'string') {
        where['or'].push({
          [key]: {
            equals: queryParam[key],
          },
        })
      } else if (typeof queryParam[key] === 'number') {
        where['or'].push({
          [key]: {
            equals: String(queryParam[key]),
          },
        })
      } else if (Array.isArray(queryParam[key]) && queryParam[key].length === 1) {
        where['or'].push({
          [key]: {
            equals: queryParam[key][0],
          },
        })
      } else if (Array.isArray(queryParam[key]) && queryParam[key].length > 1) {
        where['or'].push(
          queryParam[key].map((item: string) => {
            return {
              [key]: {
                equals: item,
              },
            }
          }),
        )
        // where['or'].push({
        //   [key]: {
        //     in: queryParam[key].join(',')
        //   }
        // })
      }
    })
    return where
  }

  async getAllPages(
    currentPage: number,
    pageSize: number,
    queryParam: Record<string, any> | undefined = undefined,
    publishedOnly = true,
  ) {
    let where = {} as Record<string, any>
    if (queryParam) {
      where = this._constructWhere(queryParam)
    }
    if (publishedOnly) {
      where.publishDate = {
        less_than_equal: new Date().toISOString(),
      }
    }
    console.log(where)
    return await this.postDAO.getAllPages(currentPage, pageSize, where)
  }

  async getPostBySlug(slug: string, onlyPublished = true) {
    const post = await this.postDAO.getPostBySlug(slug, onlyPublished)
    console.log(post.docs)
    if (post.docs.length === 0) {
      return null
    }
    return post.docs[0]
  }

  async getPostByCategory(category: Category[]) {
    const posts = await this.postDAO.getPostByCategories(category)
    return posts
  }

  async getPostsDynamically(queryParam: Record<string, unknown>, onlyPublished = true) {
    const where = this._constructWhere(queryParam)
    if (onlyPublished) {
      where.publishDate = {
        less_than_equal: new Date().toISOString(),
      }
    }
    const posts = await this.postDAO.getPostsDynamically(where, '-createdAt')
    return posts
  }
}

export default PostService
