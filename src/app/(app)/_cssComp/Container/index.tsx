import { PropsWithChildren } from 'react'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'

interface Props {
  className?: string
}

const Container = (props: PropsWithChildren<Props>) => {
  return (
    <div
      className={cn(
        `${Spacing.ContentPadding} mx-auto px-4 max-w-full sm:max-w-[95%] md:max-w-[90%] lg:max-w-[80%] xl:max-w-[70%] 2xl:max-w-[55%]`,
        props.className,
      )}
    >
      {props.children}
    </div>
  )
}

export default Container
