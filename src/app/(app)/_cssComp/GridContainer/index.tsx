import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface GridContainerProps {
  columns?: number
  rows?: number
  contentAlign?: GridContentAlignment
  contentJustify?: GridContentAlignment
  autoResponsive?: boolean
  minColumnWidth?: string
  children?: ReactNode
  className?: string
  style?: React.CSSProperties
  ref?: React.Ref<HTMLDivElement>
}

interface GridChildProps {
  colSpan?: string
  rowSpan?: RowSpan
  justifySelf?: GridItemAlignment
  alignSelf?: GridItemAlignment
  children?: ReactNode
  className?: string
  ref?: React.Ref<HTMLDivElement>
}

const GridContainer: React.FC<GridContainerProps> = ({
  columns = 12,
  rows = 1,
  contentAlign = GridContentAlignment.NORMAL,
  contentJustify = GridContentAlignment.NORMAL,
  children,
  className = '',
  style = {},
  ref,
}) => {
  return (
    <div
      ref={ref}
      style={style}
      className={cn(
        `grid grid-cols-${columns} grid-rows-${rows} grid-rows-[auto] content-${contentAlign} justify-${contentJustify}`,
        className,
      )}
    >
      {children}
    </div>
  )
}

export enum GridContentAlignment {
  NORMAL = 'normal',
  CENTER = 'center',
  START = 'start',
  END = 'end',
}

export enum GridItemAlignment {
  AUTO = 'auto',
  CENTER = 'center',
  START = 'start',
  END = 'end',
}

export enum ColSpan {
  SPAN_1 = 'col-span-1',
  SPAN_2 = 'col-span-2',
  SPAN_3 = 'col-span-3',
  SPAN_4 = 'col-span-4',
  SPAN_5 = 'col-span-5',
  SPAN_6 = 'col-span-6',
  SPAN_7 = 'col-span-7',
  SPAN_8 = 'col-span-8',
  SPAN_9 = 'col-span-9',
  SPAN_10 = 'col-span-10',
  SPAN_11 = 'col-span-11',
  SPAN_12 = 'col-span-12',
}

export enum RowSpan {
  SPAN_1 = 'row-span-1',
  SPAN_2 = 'row-span-2',
  SPAN_3 = 'row-span-3',
  SPAN_4 = 'row-span-4',
  SPAN_5 = 'row-span-5',
  SPAN_6 = 'row-span-6',
  SPAN_7 = 'row-span-7',
  SPAN_8 = 'row-span-8',
  SPAN_9 = 'row-span-9',
  SPAN_10 = 'row-span-10',
  SPAN_11 = 'row-span-11',
  SPAN_12 = 'row-span-12',
}

export enum GridItemJustify {}

export const GridItem: React.FC<GridChildProps> = ({
  colSpan = '',
  rowSpan = RowSpan.SPAN_1,
  justifySelf = GridItemAlignment.AUTO,
  alignSelf = GridItemAlignment.AUTO,
  children,
  className = '',
  ref,
}) => {
  const colSpanTw = colSpan !== '' ? colSpan : ''
  return (
    <div
      ref={ref}
      className={`${colSpanTw} ${rowSpan} justify-self-${justifySelf} self-${alignSelf} ${className}`}
    >
      {children}
    </div>
  )
}

export default GridContainer
