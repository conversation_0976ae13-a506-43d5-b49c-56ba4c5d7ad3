import { PropsWithChildren } from 'react'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'

interface Props {
  className?: string
}

const Container = (props: PropsWithChildren<Props>) => {
  return (
    <div
      className={cn(
        `${Spacing.ContentPadding} mx-auto px-4 max-w-full w-[95%] sm:max-w-[90%] md:max-w-[70%] lg:max-w-[60%] xl:max-w-[50%] 2xl:max-w-[40%]`,
        props.className,
      )}
    >
      {props.children}
    </div>
  )
}

export default Container
