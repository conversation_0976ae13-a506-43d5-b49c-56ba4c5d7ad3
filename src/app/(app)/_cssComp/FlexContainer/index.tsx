import { ReactNode, forwardRef } from 'react'

export enum FlexDirection {
  ROW = 'flex-row',
  COL = 'flex-col',
}

export enum JustifyContent {
  START = 'justify-start',
  CENTER = 'justify-center',
  BETWEEN = 'justify-between',
  END = 'justify-end',
}

export enum AlignItems {
  START = 'items-start',
  CENTER = 'items-center',
  END = 'items-end',
}

interface FlexContainerProps {
  direction?: FlexDirection
  justify?: JustifyContent
  align?: AlignItems
  children?: ReactNode
  className?: string
  wrap?: boolean
  onClick?: () => void
  onMouseLeave?: () => void
  onMouseEnter?: () => void
}

const FlexContainer = forwardRef<HTMLDivElement, FlexContainerProps>(
  (
    {
      onClick,
      onMouseLeave,
      onMouseEnter,
      direction = FlexDirection.ROW,
      justify = JustifyContent.START,
      align = AlignItems.START,
      wrap = true,
      children,
      className = '',
    },
    ref,
  ) => {
    const flexClasses = `flex ${direction} ${justify} ${align}`
    return (
      <div
        ref={ref}
        className={`${flexClasses} ${className} ${wrap ? 'flex-wrap' : ''}`}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        {children}
      </div>
    )
  },
)

// Optional: Set display name for debugging
FlexContainer.displayName = 'FlexContainer'

export default FlexContainer
