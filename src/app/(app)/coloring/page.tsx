import { <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'
import { Metadata } from 'next'
import GenerateArt from '../../../../public/media/static/illustration/generateArt.svg'
import ImageCard from '../_component/ImageCard'
import Text from '../_component/Text'
import Title, { HeaderLevel } from '../_component/Title'
import Container from '../_cssComp/Container'
import FlexContainer, { AlignItems, FlexDirection, JustifyContent } from '../_cssComp/FlexContainer'
import GridContainer from '../_cssComp/GridContainer'
import ColoringCanvas from '../_templates/ColoringCanvas'
import MainFooter from '../_templates/MainFooter'
import MainNavMenu from '../_templates/MainNavMenu'

export const metadata: Metadata = {
  title: 'Online Coloring App | ColorAria',
  description: `Color the coloring images you've generated! Relieve stress by coloring! Give it a try!`,
}

function ColoringPage() {
  return (
    <div className={`flex flex-col min-h-screen w-full`}>
      <div className="bg-main sticky top-0 w-full z-500">
        <MainNavMenu />
      </div>
      <GridContainer className={`w-full grid-cols-1 bg-accent4-lighter`}>
        <FlexContainer
          className="h-[15dvh] w-full ${Spacing.ContentPadding} gap-8 border-b-2 border-black"
          justify={JustifyContent.CENTER}
          align={AlignItems.CENTER}
        >
          <Container>
            <FlexContainer
              className="w-full h-full gap-3"
              justify={JustifyContent.CENTER}
              align={AlignItems.CENTER}
              direction={FlexDirection.COL}
            >
              <Title level={HeaderLevel.H1} className="text-5xl text-center">
                Online Coloring
              </Title>
              <Text className="text-center" variant="description" size="xl">
                Relieve stress by coloring! Give it a try!
              </Text>
            </FlexContainer>
          </Container>
        </FlexContainer>
        <FlexContainer
          className="w-full h-full gap-3 border-b-2 border-black bg-accent1-lighter"
          justify={JustifyContent.CENTER}
          align={AlignItems.CENTER}
          direction={FlexDirection.COL}
        >
          <div className="w-full md:w-full lg:w-[95%] xl:w-[90%] 2xl:w-[85%]">
            <ColoringCanvas />
          </div>
        </FlexContainer>
        <FlexContainer className="w-full p-4 bg-accent6-lighter min-h-[30vh] border-b-2 border-black">
          <Container className="h-full">
            <GridContainer className="grid-cols-1 md:grid-cols-2 h-full">
              <div>PLACEHOLDER</div>
              <FlexContainer
                direction={FlexDirection.COL}
                className="gap-4 justify-self-center self-center"
              >
                <FlexContainer
                  className="w-full gap-4"
                  direction={FlexDirection.ROW}
                  align={AlignItems.CENTER}
                >
                  <Rainbow size="2.5rem" />
                  <Title className="text-center mb-2">Color your way!~</Title>
                </FlexContainer>
                <Text size="lg">{`Thank you for checking out our in-built coloring app! 
                We've built this feature so there's no need for you to bring out the 
                crayons or color pencils to enjoy a great coloring experience.`}</Text>
                <Text size="lg">{`But of course, for the authentic experience, it's still best to color on a paper!`}</Text>
              </FlexContainer>
            </GridContainer>
          </Container>
        </FlexContainer>
        <FlexContainer className="w-full p-4 bg-white min-h-[30vh]">
          <Container className="h-full">
            <FlexContainer className="w-full">
              <Title className="text-center mb-6">How do I get started with coloring?</Title>
              <GridContainer className="w-full grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 content-center">
                <ImageCard
                  titleLevel={HeaderLevel.H4}
                  title={
                    <FlexContainer
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                      className="gap-2"
                    >
                      <Sparkles /> Step 1: Choose an image
                    </FlexContainer>
                  }
                  image={GenerateArt}
                  caption={'Step 1: Choose your image'}
                  className="bg-white"
                >
                  Pick the image you have generated!
                </ImageCard>
                <ImageCard
                  titleLevel={HeaderLevel.H4}
                  title={
                    <FlexContainer align={AlignItems.CENTER} className="gap-2">
                      <Sparkles /> Step 2: Pick your brush
                    </FlexContainer>
                  }
                  image={GenerateArt}
                  caption={'Step 2: Pick your brush'}
                  className="bg-white"
                >
                  Plenty of brushes to choose from! You can also opt to use the paint bucket brush
                  to smartly color your image!
                </ImageCard>
                <ImageCard
                  titleLevel={HeaderLevel.H4}
                  title={
                    <FlexContainer align={AlignItems.CENTER} className="gap-2">
                      <Sparkles /> Step 3: Save and Share!
                    </FlexContainer>
                  }
                  image={GenerateArt}
                  caption={'Step 2: Pick your brush'}
                  className="bg-white"
                >
                  Like your coloring image? Save it and share it with the world!
                </ImageCard>
              </GridContainer>
            </FlexContainer>
          </Container>
        </FlexContainer>
      </GridContainer>
      <div className="bg-main bottom-0 w-full">
        <MainFooter />
      </div>
    </div>
  )
}

export default ColoringPage
