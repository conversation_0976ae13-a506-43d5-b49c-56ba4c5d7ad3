import AuthProvider from '../_provider/AuthProvider'
import DashboardNavMenu from '../_templates/DashboardNavMenu'
import MainFooter from '../_templates/MainFooter'

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <div className="flex flex-col min-h-screen bg-bg">
        <div className="bg-main sticky top-0 z-400">
          <DashboardNavMenu />
        </div>

        <div className="w-full pb-4 mx-auto flex-auto md:overflow-hidden bg-bg">{children}</div>
        <div className="bg-main bottom-0 w-full">
          <MainFooter />
        </div>
      </div>
    </AuthProvider>
  )
}
