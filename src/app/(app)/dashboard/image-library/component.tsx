'use client'

import { useQuery, keepPreviousData } from '@tanstack/react-query'
import {
  Camera,
  Car,
  Cat,
  CupSoda,
  Flower,
  House,
  Search,
  SearchCheck,
  TriangleAlert,
} from 'lucide-react'
import { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON>oader } from 'react-spinners'
import { toast } from 'sonner'
import { UnsplashPhoto } from '../../_backend/common/service/UnsplashService/types'
import { Button } from '../../_component/Button'
import { Input } from '../../_component/Input'
import MasonryGrid from '../../_component/Masonry'
import PillTag from '../../_component/PillTag'
import Text from '../../_component/Text'
import Title, { HeaderLevel } from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, {
  ColSpan,
  GridContentAlignment,
  GridItem,
} from '../../_cssComp/GridContainer'
import { searchPhotos } from '../../_localApi/searchImages'
import UnsplashImageCard from '../../_templates/ImageSearch/UnsplashImageCard'

function ImageLibraryComponent() {
  const [currentPage, setCurrentPage] = useState(1)
  const [liveQuery, setLiveQuery] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [imageStore, setImageStore] = useState<UnsplashPhoto[]>([])
  const previousQuery = useRef(searchQuery)
  const isSameQuery = previousQuery.current === searchQuery
  if (!isSameQuery) {
    previousQuery.current = searchQuery
  }
  const unsplashImages = useQuery({
    queryKey: ['unsplashImages', searchQuery, currentPage],
    enabled: searchQuery !== '',
    queryFn: () => searchPhotos(searchQuery, currentPage, 24, 'relevant'),
    placeholderData: isSameQuery ? keepPreviousData : undefined,
    staleTime: 1000 * 60 * 10,
  })

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchQuery('Animals')
      setLiveQuery('Animals')
    }
  }, [searchQuery])

  const handleSearch = () => {
    if (liveQuery.trim() === searchQuery) return
    if (liveQuery.trim() !== '') {
      setSearchQuery(liveQuery)
      setImageStore([])
      setCurrentPage(1)
    } else {
      toast.error('Please enter a search query, it cannot be empty.', {
        duration: 0,
        position: 'top-right',
        icon: <TriangleAlert />,
      })
    }
  }
  const handleLoadMore = () => {
    setCurrentPage((page) => page + 1)
  }
  const handleRecommendedTagSearch = (tag: string) => {
    if (tag.trim() === searchQuery) return
    setLiveQuery(tag)
    setSearchQuery(tag)
    setImageStore([])
    setCurrentPage(1)
  }
  useEffect(() => {
    if (unsplashImages.data) {
      setImageStore((oldImageStore) => [...oldImageStore, ...unsplashImages.data.data.results])
    }
  }, [unsplashImages.data])
  return (
    <GridContainer columns={1} className="gap-4">
      <div className="bg-accent6-lighter border-b-2">
        <div className="p-4 mx-auto w-[95%] md:w-[90%] lg:w-[85%] xl:w-[80%] 2xl:w-[70%]">
          <GridContainer className="grid-cols-1 gap-2 py-4 lg:grid-cols-2 lg:gap-8 w-full">
            <FlexContainer
              direction={FlexDirection.COL}
              className="gap-2 mb-4"
              align={AlignItems.START}
              justify={JustifyContent.CENTER}
            >
              <Title>🖼️ Image Library</Title>
              <Text variant="description">
                No idea where to start? <br />
                Explore our images for more 🌟 coloring inspiration! 🌟
              </Text>
              <GridContainer
                contentAlign={GridContentAlignment.CENTER}
                contentJustify={GridContentAlignment.CENTER}
                className="gap-2 mt-4 w-full"
              >
                <GridItem colSpan={ColSpan.SPAN_12}>
                  <Input
                    startIcon={Search}
                    onChange={(e) => setLiveQuery(e.target.value)}
                    value={liveQuery}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch()
                      }
                    }}
                    placeholder="Search for an image"
                  />
                </GridItem>
                <GridItem colSpan={ColSpan.SPAN_12}>
                  <Button className="w-full" variant="positive" onClick={handleSearch}>
                    <SearchCheck /> Search
                  </Button>
                </GridItem>
              </GridContainer>
            </FlexContainer>

            <FlexContainer direction={FlexDirection.COL}>
              <FlexContainer className="mb-4">
                <Title level={HeaderLevel.H5} className="text-bold">
                  Check out some of our favorite categories~!
                </Title>
              </FlexContainer>
              <FlexContainer wrap className="gap-4 w-full">
                <PillTag
                  isButton
                  className="p-2"
                  textClassName="text-sm"
                  onClick={() => {
                    handleRecommendedTagSearch('Street Photography')
                  }}
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <Camera /> Street Photography
                    </FlexContainer>
                  }
                />
                <PillTag
                  isButton
                  onClick={() => {
                    handleRecommendedTagSearch('Nature')
                  }}
                  className="p-2"
                  textClassName="text-sm"
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <Flower /> Nature
                    </FlexContainer>
                  }
                />
                <PillTag
                  isButton
                  className="p-2"
                  textClassName="text-sm"
                  onClick={() => {
                    handleRecommendedTagSearch('Animals')
                  }}
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <Cat /> Animals
                    </FlexContainer>
                  }
                />
                <PillTag
                  isButton
                  className="p-2"
                  textClassName="text-sm"
                  onClick={() => {
                    handleRecommendedTagSearch('Food & Drinks')
                  }}
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <CupSoda /> Food & Drinks
                    </FlexContainer>
                  }
                />
                <PillTag
                  isButton
                  className="p-2"
                  textClassName="text-sm"
                  onClick={() => {
                    handleRecommendedTagSearch('Cars')
                  }}
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <Car /> Cars
                    </FlexContainer>
                  }
                />
                <PillTag
                  isButton
                  className="p-2"
                  textClassName="text-sm"
                  onClick={() => {
                    handleRecommendedTagSearch('House')
                  }}
                  text={
                    <FlexContainer
                      className="gap-2"
                      align={AlignItems.CENTER}
                      direction={FlexDirection.ROW}
                    >
                      <House /> House
                    </FlexContainer>
                  }
                />
              </FlexContainer>
            </FlexContainer>
          </GridContainer>
        </div>
      </div>
      <div className="mx-auto w-[95%] md:w-[90%] lg:w-[85%] xl:w-[80%] 2xl:w-[70%]">
        <GridContainer className="mb-4 items-center">
          <GridItem colSpan={ColSpan.SPAN_6}>
            <FlexContainer direction={FlexDirection.COL} className="gap-2">
              <Title>Image Results</Title>
            </FlexContainer>
          </GridItem>
          <GridItem colSpan={ColSpan.SPAN_6}>
            <FlexContainer justify={JustifyContent.END} align={AlignItems.CENTER}>
              {unsplashImages.isFetched && (
                <Text>
                  {imageStore.length} - {unsplashImages.data?.data.total} results fetched
                </Text>
              )}
            </FlexContainer>
          </GridItem>
        </GridContainer>
        {/* <div
          ref={masonryContainer}
          className={'w-full gap-4 grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4'}
        >
          {imageStore.map((image) => (
            <UnsplashImageCard photo={image} key={image.id} className="" />
          ))}
        </div> */}
        <MasonryGrid>
          {imageStore.map((image) => (
            <UnsplashImageCard photo={image} key={image.id} className="" />
          ))}
        </MasonryGrid>
        <FlexContainer
          className="w-full my-4"
          justify={JustifyContent.CENTER}
          align={AlignItems.CENTER}
        >
          {unsplashImages.isFetching && <MoonLoader />}
        </FlexContainer>
        <Button
          variant="emphasis"
          className="w-full"
          onClick={handleLoadMore}
          disabled={unsplashImages.isFetching}
        >
          Load More
        </Button>
      </div>
    </GridContainer>
  )
}

export default ImageLibraryComponent
