import { headers } from 'next/headers'
import { getPayload } from 'payload'
import { FeaturesType } from '@/utilities/local/enums'
import config from '@payload-config'
import ImageLibraryComponent from './component'
import FeatureDAO from '../../_backend/common/dao/FeaturesDAO'
import UserDAO from '../../_backend/common/dao/UserDAO'
import FeatureScopeService from '../../_backend/common/service/FeatureScopeService'
import { _verifyJWT } from '../../_backend/common/utils/auth'
import SubscriptionRedux from '../../_component/LockComponent/SubscriptionRedux'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'

export const metadata = {
  title: 'Image Library | ColorAria',
  description: `Get inspired by our image gallery! Browse through our curated collection of images and find inspiration for your next coloring page!`,
}

async function ImageLibraryPage() {
  const payload = await getPayload({ config })
  const userDAO = new UserDAO(payload)
  const featuresDAO = new FeatureDAO(payload)
  const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)
  const headersList = await headers()
  const userHeader = headersList.get('x-user')
  const user = JSON.parse(userHeader!)
  const userFeaturesData = await featureScopeService.retrieveUserFeatures(user.id)
  if (userFeaturesData.includes(FeaturesType.IMAGE_LIBRARY)) {
    return <ImageLibraryComponent />
  } else {
    return (
      <FlexContainer
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
        justify={JustifyContent.CENTER}
        className="w-full h-[90vh] flex-auto"
      >
        <SubscriptionRedux
          className="w-full h-[85vh]"
          chosenPlanFilter={['ARIA_PLAN', 'MUSE_PLAN']}
        />
      </FlexContainer>
    )
  }
}

export default ImageLibraryPage
