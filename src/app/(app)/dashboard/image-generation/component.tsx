'use client'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Contrast,
  Dna,
  ImageMinus,
  ImageUpscale,
  Palette,
  Text as TextIcon,
  Wallpaper,
} from 'lucide-react'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

import { Feature } from '@/payload-types'
import { ActionType, FeaturesType } from '@/utilities/local/enums'
import LockComponent from '../../_component/LockComponent'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../_component/Select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../_component/Tabs'
import Text from '../../_component/Text'
import Title, { HeaderLevel } from '../../_component/Title'
import FlexContainer, { AlignItems, FlexDirection } from '../../_cssComp/FlexContainer'
import GridContainer, { GridItem } from '../../_cssComp/GridContainer'
import {
  fetchImageBackgroudRemover,
  fetchImageContrast,
  fetchImageGenerationText,
  fetchImageUpscale,
  fetchImageToImage,
} from '../../_localApi/imageGeneration'
import { useAuthState } from '../../_state/authState'
import { useImageTransfer } from '../../_state/imageTransfer'
import ImageBackgroundRemover from '../../_templates/ImageAction/ImageBackground'
import ImageContrast from '../../_templates/ImageAction/ImageContrast'
import ImageGrid from '../../_templates/ImageAction/ImageGrid'
import ImageToImage from '../../_templates/ImageAction/ImageToImage'
import ImageUpscaler from '../../_templates/ImageAction/ImageUpscale'
import TextImageGeneratorCard from '../../_templates/ImageAction/TextImageGeneratorCard'
import { ImageMetadata } from '../../_types/ImageMetadata'

interface Props {
  userFeatures: string[]
  features: Feature[]
}

function ImageGenerationComponent(props: Props) {
  const { userFeatures, features } = props

  const queryClient = useQueryClient()
  const [currentSelectedImage, setCurrentSelectedImage] = useState<ImageMetadata | null>(null)
  const [tabValue, setTabValue] = useState('generateImage')
  const [selectedOption, setSelectedOption] = useState<string>(ActionType.TEXT_TO_COLOR)
  const [selectedOptionGeneration, setSelectedOptionGeneration] = useState<string>(
    ActionType.TEXT_TO_COLOR,
  )
  const [selectedOptionModification, setSelectedOptionModification] = useState<string>(
    ActionType.CONTRAST,
  )
  // Signal to trigger refetch in ImageGrid
  const [refetchTrigger, setRefetchTrigger] = useState(0)
  const { user, setUser } = useAuthState()
  const { transferImage, setTransferImage } = useImageTransfer()

  // Handle transferImage from other components
  useEffect(() => {
    if (transferImage) {
      onImageActionSelectionChange(transferImage.actionType, transferImage)
      setTransferImage(null)
    }
  }, [transferImage, setTransferImage])

  const onImageGenerationSelectionChange = useCallback(
    (action: string, image: ImageMetadata | null = null) => {
      setCurrentSelectedImage(image)
      setSelectedOption(action)
      setSelectedOptionGeneration(action)
    },
    [],
  )

  const onImageModificationSelectionChange = useCallback(
    (action: string, image: ImageMetadata | null = null) => {
      setCurrentSelectedImage(image)
      setSelectedOption(action)
      setSelectedOptionModification(action)
    },
    [],
  )

  const onImageActionSelectionChange = (action: string, image: ImageMetadata | null = null) => {
    if (
      action === ActionType.TEXT_TO_COLOR ||
      action === ActionType.IMAGE_TO_COLOR ||
      action === ActionType.DRAWING_TO_SKETCH
    ) {
      setSelectedOptionGeneration(action)
      setTabValue('generateImage')
    } else {
      setSelectedOptionModification(action)
      setTabValue('modifyImage')
    }
    setCurrentSelectedImage(image)
    setSelectedOption(action)
  }

  const onTabValueChange = (tabValue: string) => {
    if (tabValue === 'generateImage') {
      setSelectedOption(selectedOptionGeneration)
      setTabValue('generateImage')
    } else {
      setSelectedOption(selectedOptionModification)
      setTabValue('modifyImage')
    }
  }

  // Handlers specifically for Select components (only take action string)
  const handleGenerationSelectChange = (action: string) => {
    onImageGenerationSelectionChange(action, null)
  }

  const handleModificationSelectChange = (action: string) => {
    onImageModificationSelectionChange(action, null)
  }

  const ImageActions = ({
    selectedAction,
  }: {
    selectedAction: string
    currentSelectedImage: ImageMetadata | null
  }) => {
    switch (selectedAction) {
      case ActionType.IMAGE_TO_COLOR:
        return (
          <LockComponent
            feature={FeaturesType.IMAGE_TO_COLOR}
            userFeatures={userFeatures}
            creditCost={
              features.filter((f) => f.feature === FeaturesType.IMAGE_TO_COLOR)[0].creditCost
            }
            mode="hide"
          >
            <ImageToImage
              generateImage={useImageToImageMutation}
              extImage={currentSelectedImage}
              userFeatures={userFeatures}
              features={features}
            />
          </LockComponent>
        )
      case ActionType.AI_BACKGROUND_REMOVER:
        return (
          <LockComponent
            feature={FeaturesType.AI_BACKGROUND_REMOVER}
            userFeatures={userFeatures}
            creditCost={
              features.filter((f) => f.feature === FeaturesType.AI_BACKGROUND_REMOVER)[0].creditCost
            }
            mode="hide"
          >
            <ImageBackgroundRemover
              extImage={currentSelectedImage}
              generateImage={useBackgroundRemoverMutation}
            />
          </LockComponent>
        )
      case ActionType.AI_UPSCALER:
        return (
          <LockComponent
            feature={FeaturesType.TRADITIONAL_UPSCALER}
            userFeatures={userFeatures}
            creditCost={
              features.filter((f) => f.feature === FeaturesType.TRADITIONAL_UPSCALER)[0].creditCost
            }
            mode="hide"
          >
            <ImageUpscaler
              extImage={currentSelectedImage}
              generateImage={useImageUpscaleMutation}
            />
          </LockComponent>
        )
      case ActionType.CONTRAST:
        return (
          <ImageContrast
            extImage={currentSelectedImage}
            removeImage={() => setCurrentSelectedImage(null)}
            saveImage={(image: ImageMetadata) => {
              useContrastGeneration.mutate({ src: image.src })
            }}
          />
        )
      default:
        return (
          <LockComponent
            feature={FeaturesType.TEXT_TO_COLOR}
            userFeatures={userFeatures}
            creditCost={
              features.filter((f) => f.feature === FeaturesType.TEXT_TO_COLOR)[0].creditCost
            }
            mode="hide"
          >
            <TextImageGeneratorCard
              generateImage={useImageGenerationText}
              userFeatures={userFeatures}
              features={features}
            />
          </LockComponent>
        )
    }
  }

  //Will use onMutate to trigger a 'load animation'
  const useImageGenerationText = useMutation({
    mutationFn: async (data: {
      prompt: string
      aspectRatio: string
      numberOfImages: number
      highQuality: boolean
    }) => {
      const response = await fetchImageGenerationText(
        data.prompt,
        data.aspectRatio,
        data.numberOfImages,
        data.highQuality,
      )
      if (!response.success) {
        toast.error('Image generation operation failed', {
          position: 'top-right',
          description: response.message.message,
        })
        throw new Error('Failed to generate images')
      }
      // Trigger refetch in ImageGrid after successful generation
      setRefetchTrigger((prev) => prev + 1)
      return response
    },
    onSuccess: async (response) => {
      // Images will be fetched by React Query polling
      if (user) {
        const copyUser = { ...user }
        copyUser.freeCredits = response.data.remainingCredits
        setUser(copyUser)
        queryClient.invalidateQueries({ queryKey: ['user'] })
        toast.success('Image generation started successfully')
      }
    },
  })

  const useContrastGeneration = useMutation({
    mutationFn: async (data: { src: string }) => {
      const response = await fetchImageContrast(data.src)
      if (!response.success) {
        toast.error('Image contrast operation failed', {
          position: 'top-right',
          description: response.message.message,
        })
        throw new Error('Failed to generate images')
      }
      // Trigger refetch in ImageGrid after successful generation
      setRefetchTrigger((prev) => prev + 1)
      return response
    },
    onSuccess: async () => {
      toast.success('Image processing started successfully')
    },
  })

  const useImageToImageMutation = useMutation<
    any,
    Error,
    {
      prompt: string
      src: string
      aspectRatio: string
      numberOfImages: number
      unsplashSrc?: string
    },
    void
  >({
    mutationFn: async (data: {
      prompt: string
      src: string
      aspectRatio: string
      numberOfImages: number
      unsplashSrc?: string
    }) => {
      const response = await fetchImageToImage(
        data.prompt,
        data.src,
        data.aspectRatio,
        data.numberOfImages,
        data.unsplashSrc,
      )
      if (!response.success) {
        toast.error('Image generation operation failed', {
          position: 'top-right',
          description: response.message.message,
        })
        throw new Error('Failed to generate images')
      }

      // Trigger refetch in ImageGrid after successful generation
      setRefetchTrigger((prev) => prev + 1)
      return response
    },
    onSuccess: async (response) => {
      // Images will be fetched by React Query polling
      if (user) {
        const copyUser = { ...user }
        copyUser.freeCredits = response.data.remainingCredits
        setUser(copyUser)
        queryClient.invalidateQueries({ queryKey: ['user'] })
        toast.success('Image generation started successfully')
      }
    },
  })

  const useBackgroundRemoverMutation = useMutation<any, Error, { src: string }, void>({
    mutationFn: async (data: { src: string }) => {
      const response = await fetchImageBackgroudRemover(data.src)
      if (!response.success) {
        toast.error('Remove background operation failed', {
          position: 'top-right',
          description: response.message.message,
        })
        throw new Error('Failed to generate images')
      }

      // Trigger refetch in ImageGrid after successful generation
      setRefetchTrigger((prev) => prev + 1)
      return response
    },
    onSuccess: async (response) => {
      // Images will be fetched by React Query polling
      if (user) {
        const copyUser = { ...user }
        copyUser.freeCredits = response.data.remainingCredits
        setUser(copyUser)
        queryClient.invalidateQueries({ queryKey: ['user'] })
        toast.success('Background removal started successfully')
      }
    },
  })

  const useImageUpscaleMutation = useMutation<any, Error, { src: string; upscale: number }, void>({
    mutationFn: async (data: { src: string; upscale: number }) => {
      const response = await fetchImageUpscale(data.src, data.upscale)
      if (!response.success) {
        toast.error('Upscale operation failed', {
          position: 'top-right',
          description: response.message.message,
        })
        throw new Error('Failed to generate images')
      }

      // Trigger refetch in ImageGrid after successful generation
      setRefetchTrigger((prev) => prev + 1)
      return response
    },
    onSuccess: async () => {
      toast.success('Image upscaling started successfully')
    },
  })

  return (
    <>
      <div className="w-[95%] ml-auto mr-auto mt-4 relative">
        <GridContainer className="w-full h-full gap-8 grid-cols-1 lg:grid-cols-2">
          <GridItem className="h-full sticky">
            <div className="flex flex-col gap-4 mb-4 w-full">
              <Tabs
                value={tabValue}
                onValueChange={(tabValue) => onTabValueChange(tabValue)}
                className=""
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="generateImage">Generate Image</TabsTrigger>
                  <TabsTrigger value="modifyImage">Modify Image</TabsTrigger>
                </TabsList>
                <TabsContent value="generateImage">
                  <div className="flex flex-col gap-3 pt-4">
                    <FlexContainer direction={FlexDirection.COL}>
                      <FlexContainer
                        direction={FlexDirection.ROW}
                        align={AlignItems.CENTER}
                        className="gap-2"
                      >
                        <Palette />
                        <Title level={HeaderLevel.H3}>Create your coloring page</Title>
                      </FlexContainer>
                      <Text variant="description" size="sm">
                        {`Choose the type of coloring page you'll like to create:`}
                      </Text>
                    </FlexContainer>
                    <div>
                      <Select
                        defaultValue={selectedOptionGeneration}
                        value={selectedOptionGeneration}
                        onValueChange={handleGenerationSelectChange}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ActionType.TEXT_TO_COLOR}>
                            <FlexContainer align={AlignItems.CENTER} className="gap-2">
                              <TextIcon size={'18px'} /> Text to Coloring Page
                            </FlexContainer>
                          </SelectItem>
                          <SelectItem value={ActionType.IMAGE_TO_COLOR}>
                            <FlexContainer align={AlignItems.CENTER} className="gap-2">
                              <Wallpaper size={'18px'} /> Image to Coloring Page
                            </FlexContainer>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="modifyImage">
                  <div className="flex flex-col gap-3 pt-4">
                    <FlexContainer direction={FlexDirection.COL}>
                      <FlexContainer
                        direction={FlexDirection.ROW}
                        align={AlignItems.CENTER}
                        className="gap-2"
                      >
                        <Dna />
                        <Title level={HeaderLevel.H3}>Image Modification</Title>
                      </FlexContainer>
                      <Text variant="description" size="sm">
                        {`Choose the modification you'll like to make:`}
                      </Text>
                    </FlexContainer>
                    <div>
                      <Select
                        defaultValue={selectedOptionModification}
                        value={selectedOptionModification}
                        onValueChange={handleModificationSelectChange}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={ActionType.CONTRAST}>
                            <FlexContainer align={AlignItems.CENTER} className="gap-2">
                              <Contrast size={'18px'} /> Improve Image Contrast
                            </FlexContainer>
                          </SelectItem>
                          <SelectItem value={ActionType.AI_BACKGROUND_REMOVER}>
                            <FlexContainer align={AlignItems.CENTER} className="gap-2">
                              <ImageMinus size={'18px'} /> Background Remover
                            </FlexContainer>
                          </SelectItem>
                          <SelectItem value={ActionType.AI_UPSCALER}>
                            <FlexContainer align={AlignItems.CENTER} className="gap-2">
                              <ImageUpscale size={'18px'} /> Image Upscaler
                            </FlexContainer>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
              <div className="">
                <ImageActions
                  selectedAction={selectedOption}
                  currentSelectedImage={currentSelectedImage}
                />
              </div>
            </div>
          </GridItem>
          <GridItem className=" max-h-[90vh] overflow-y-auto">
            <FlexContainer direction={FlexDirection.COL} className="">
              <ImageGrid
                imageActionChange={onImageActionSelectionChange}
                refetchTrigger={refetchTrigger}
              />
            </FlexContainer>
          </GridItem>
        </GridContainer>
      </div>
    </>
  )
}

export default ImageGenerationComponent
