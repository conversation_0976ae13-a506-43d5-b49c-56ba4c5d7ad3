import { headers } from 'next/headers'
import { getPayload } from 'payload'
import config from '@payload-config'
import ImageGenerationComponent from './component'
import FeatureDAO from '../../_backend/common/dao/FeaturesDAO'
import UserDAO from '../../_backend/common/dao/UserDAO'
import FeatureScopeService from '../../_backend/common/service/FeatureScopeService'
import { _verifyJWT } from '../../_backend/common/utils/auth'

export const metadata = {
  title: 'Image Generation Dashboard | ColorAria',
  description: `The place where you can generate images.`,
}

//TODO: Find a way to gracefully fail, if featureData is empty
async function ImageGenerationPage() {
  const payload = await getPayload({ config })
  const userDAO = new UserDAO(payload)
  const featuresDAO = new FeatureDAO(payload)
  const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)
  const headersList = await headers()
  const userHeader = headersList.get('x-user')
  const user = JSON.parse(userHeader!)
  const userFeaturesData = await featureScopeService.retrieveUserFeatures(user.id)
  const features = await featureScopeService.retrieveAllFeatures()
  return <ImageGenerationComponent userFeatures={userFeaturesData} features={features} />
}

export default ImageGenerationPage
