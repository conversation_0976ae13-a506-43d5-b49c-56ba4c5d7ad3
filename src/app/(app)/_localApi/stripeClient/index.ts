import { SessionInfo } from '../../api/session/[sessionId]/route'
import { apiFetch, ApiResponse } from '../util'

interface StripeSessionCheckoutResponse {
  url: string
}

const HOST_URL = process.env.NEXT_PUBLIC_HOST

export async function createStripeCheckoutSession(
  userId: string,
  subscriptionName: string,
  subscriptionDuration: string,
): Promise<ApiResponse<StripeSessionCheckoutResponse>> {
  const headers = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId, subscriptionName, subscriptionDuration }),
  } as RequestInit

  const response = await apiFetch<StripeSessionCheckoutResponse>(`/api/checkout-session`, headers)

  return response
}

export async function getStripeCheckoutSession(sessionId: string) {
  const headers = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<SessionInfo>(`${HOST_URL}/api/session/${sessionId}`, headers)

  return response
}
