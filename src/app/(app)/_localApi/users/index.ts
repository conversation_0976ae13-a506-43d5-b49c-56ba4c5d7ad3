import { Thumbmark } from '@thumbmarkjs/thumbmarkjs'
import { User, UserWithAuth } from '../../_backend/common/dto/user/UserDTO'
import { RegisterResponse } from '../../api/user/(register)/register/route'
import { ResendCodeResponse } from '../../api/user/(register)/resendCode/route'
import { VerifyCodeResponse } from '../../api/user/(register)/verifyCode/route'
import { UserFeaturesResponse } from '../../api/user/features/route'
import { LogoutResponse } from '../../api/user/logout/route'
import { UserWithSubscriptionPlan } from '../../api/user/subscriptionPlan/route'
import { VerifyUserResult } from '../../api/user/verify/route'
import { ApiResponse, apiFetch } from '../util'

const HOST_URL = process.env.NEXT_PUBLIC_HOST

export async function registerUsers(
  firstName: string,
  lastName: string,
  email: string,
  password: string,
  fingerprintHash?: string,
): Promise<ApiResponse<RegisterResponse>> {
  const headers = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      firstName,
      lastName,
      email,
      password,
      fingerprintHash: fingerprintHash,
    }),
  } as RequestInit

  const response = await apiFetch<RegisterResponse>(`/api/user/register`, headers)

  return response
}

export async function loginUser(
  email: string,
  password: string,
  recaptchaToken: string,
): Promise<ApiResponse<UserWithAuth>> {
  const headers = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password, recaptchaToken }),
  } as RequestInit

  const response = await apiFetch<UserWithAuth>(`/api/user/login`, headers)

  return response
}

export async function fetchSelfUser(): Promise<ApiResponse<User>> {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<User>(`/api/user/getInfo`, headers)

  return response
}

export async function fetchSelfUserWithSubscriptionPlan(): Promise<
  ApiResponse<UserWithSubscriptionPlan>
> {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<UserWithSubscriptionPlan>(`/api/user/subscriptionPlan`, headers)

  return response
}

export async function fetchUserFeatures(): Promise<ApiResponse<UserFeaturesResponse>> {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<UserFeaturesResponse>(`${HOST_URL}/api/user/features`, headers)
  return response
}

export async function verifyUser(): Promise<ApiResponse<VerifyUserResult>> {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<VerifyUserResult>(`/api/user/verify`, headers)

  return response
}

export async function updateUser(firstName: string, lastName: string) {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ firstName, lastName }),
  } as RequestInit
  const response = await apiFetch<User>(`/api/user/updateUser`, headers)

  return response
}

export async function verifyCode(
  userId: string,
  code: string,
): Promise<ApiResponse<VerifyCodeResponse>> {
  const response = await apiFetch<VerifyCodeResponse>('/api/user/verifyCode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ userId, code }),
  } as RequestInit)

  return response
}

export async function resendCode(userId: string): Promise<ApiResponse<ResendCodeResponse>> {
  const response = await apiFetch<ResendCodeResponse>(`/api/user/resendCode?userId=${userId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit)

  return response
}

export async function logoutUser() {
  const response = await apiFetch<LogoutResponse>('/api/user/logout', {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit)

  return response
}
