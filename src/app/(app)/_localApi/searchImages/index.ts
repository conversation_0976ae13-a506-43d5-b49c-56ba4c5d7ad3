import {
  SearchUnsplashPhotosResponse,
  UnsplashPhoto,
} from '../../_backend/common/service/UnsplashService/types'
import { apiFetch } from '../util'

export async function fetchPhotos(page: number, perPage: number) {
  const headers = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch<UnsplashPhoto[]>(
    `/api/image-search/get-photos?page=${page}&per_page=${perPage}`,
    headers,
  )
  return response
}

export async function searchPhotos(
  query: string,
  page: number,
  perPage: number,
  orderBy?: string,
  orientation?: string,
) {
  const requestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  let url = `/api/image-search/search-photos?query=${query}&page=${page}&per_page=${perPage}`
  if (orderBy && orderBy !== '') {
    url += `&order_by=${orderBy}`
  }
  if (orientation && orientation !== '') {
    url += `&orientation=${orientation}`
  }
  const response = await apiFetch<SearchUnsplashPhotosResponse>(url, requestInit)

  return response
}

export async function attributionLink(attributionId: string) {
  const requestInit = {
    method: 'GET',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  } as RequestInit
  const response = await apiFetch(
    `/api/image-search/photo-attribution?attributionId=${encodeURIComponent(attributionId)}`,
    requestInit,
  )
  return response
}
