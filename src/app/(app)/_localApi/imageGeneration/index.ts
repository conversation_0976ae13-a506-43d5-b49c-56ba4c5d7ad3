import { imageUrlToBase64, isBase64Str } from '@/utilities/local/image'
import { ImageContrastResponse } from '../../api/image-generation/image-contrast/route'
import { ImageGenerationResponseI2I } from '../../api/image-generation/image-to-image/route'
import { ImageUpscaleResponse } from '../../api/image-generation/image-upscale/route'
import { RemoveBackgroundResponse } from '../../api/image-generation/remove-background/route'
import { ImageGenerationResponseT2I } from '../../api/image-generation/text-to-image/route'
import { GeneratedImagesResponse } from '../../api/images/route'
import { apiFetch, ApiResponse } from '../util'

export interface FetchImageToImage {
  b64: string
  cost: number
  seed: number
  prompt: string
}

export async function fetchUserImages(
  page: number = 1,
  limit: number = 20,
): Promise<ApiResponse<GeneratedImagesResponse>> {
  const headers = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  }

  const queryParams = new URLSearchParams()
  queryParams.set('page', page.toString())
  queryParams.set('limit', limit.toString())

  const url = `/api/images?${queryParams.toString()}`
  const response = await apiFetch<GeneratedImagesResponse>(url, headers)
  return response
}

export async function fetchCompletedImages(
  page: number = 1,
  limit: number = 20,
): Promise<ApiResponse<GeneratedImagesResponse>> {
  const headers = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  }

  const queryParams = new URLSearchParams({
    status: 'completed',
    page: page.toString(),
    limit: limit.toString(),
  })

  const url = `/api/images?${queryParams.toString()}`
  const response = await apiFetch<GeneratedImagesResponse>(url, headers)
  return response
}

export async function fetchImageToImage(
  prompt: string,
  src: string,
  aspectRatio: string,
  numberOfImages: number,
  unsplashSrc: string = '',
): Promise<ApiResponse<ImageGenerationResponseI2I>> {
  let b64Str = src
  if (!isBase64Str(src)) {
    b64Str = await imageUrlToBase64(src)
  }
  const headers = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ prompt, imageBase64: b64Str, aspectRatio, numberOfImages, unsplashSrc }),
  }

  const response = await apiFetch<ImageGenerationResponseI2I>(
    `/api/image-generation/image-to-image`,
    headers,
  )

  return response
}

export async function fetchImageGenerationText(
  prompt: string,
  aspectRatio: string,
  numberOfImages: number,
  highQuality: boolean,
): Promise<ApiResponse<ImageGenerationResponseT2I>> {
  const headers = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ prompt, aspectRatio, numberOfImages, highQuality }),
  }

  const response = await apiFetch<ImageGenerationResponseT2I>(
    `/api/image-generation/text-to-image`,
    headers,
  )

  return response
}

export async function fetchImageBackgroudRemover(
  src: string,
): Promise<ApiResponse<RemoveBackgroundResponse>> {
  const url = `/api/image-generation/remove-background`
  let b64Str = src
  if (!isBase64Str(src)) {
    b64Str = await imageUrlToBase64(src)
  }
  const requestInit = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ imageBase64: b64Str }),
  } as RequestInit

  const response = await apiFetch<RemoveBackgroundResponse>(url, requestInit)
  return response
}

export async function fetchImageUpscale(src: string, upscale: number) {
  const url = `/api/image-generation/image-upscale`
  let b64Str = src
  if (!isBase64Str(src)) {
    b64Str = await imageUrlToBase64(src)
  }
  const requestInit = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ b64: b64Str, upscale }),
  } as RequestInit

  const response = await apiFetch<ImageUpscaleResponse>(url, requestInit)
  return response
}

export async function fetchImageContrast(src: string) {
  const url = `/api/image-generation/image-contrast`
  let b64Str = src
  if (!isBase64Str(src)) {
    b64Str = await imageUrlToBase64(src)
  }
  const requestInit = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ b64: b64Str }),
  } as RequestInit

  const response = await apiFetch<ImageContrastResponse>(url, requestInit)
  return response
}
