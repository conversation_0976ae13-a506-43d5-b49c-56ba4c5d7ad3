import { useEffect, useState } from 'react'

import <PERSON>lex<PERSON>ontaine<PERSON>, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { Button } from '../../Button'
import { Select, SelectTrigger, SelectContent, SelectValue, SelectItem } from '../../Select'
import Text from '../../Text'

interface Props {
  totalCount: number
  currentPage: number
  currentPageSize: number
  setCurrentPage?: (page: number, pageSize: number) => void
  setCurrentPageSize?: (pageSize: number) => void
}

interface PaginationButtonProps {
  currentPage: number
  totalPage: number
  setCurrentPage?: (page: number) => void
  range?: number
}

function PaginationButton(props: PaginationButtonProps) {
  const { currentPage, totalPage, setCurrentPage, range = 2 } = props
  const pages: (number | string)[] = []
  pages.push(1)
  if (currentPage > 3) {
    pages.push('...')
  }
  for (
    let i = Math.max(range, currentPage - 1);
    i <= Math.min(totalPage - 1, currentPage + 1);
    i++
  ) {
    pages.push(i)
  }
  if (currentPage < totalPage - range) {
    pages.push('...')
  }

  if (totalPage > 1) {
    pages.push(totalPage)
  }

  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      className="w-full gap-2"
      align={AlignItems.CENTER}
      justify={JustifyContent.END}
    >
      {pages.map((page, index) =>
        page === '...' ? (
          <span key={`dots-${index}`}>...</span>
        ) : (
          <Button
            onClick={() => setCurrentPage?.(Number(page))}
            key={page}
            variant={page === currentPage ? 'selected' : 'default'}
          >
            {/* <Text size="base">{page}</Text> */}
            {page}
          </Button>
        ),
      )}
    </FlexContainer>
  )
}

function ClientPagination(props: Props) {
  const { totalCount, currentPage, currentPageSize, setCurrentPageSize, setCurrentPage } = props
  const [selectedDocsToShow, setSelectedDocsToShow] = useState(currentPageSize.toString())
  const totalPage = Math.ceil(totalCount / Number(selectedDocsToShow))

  const handlePageChange = (page: number) => {
    if (setCurrentPage) {
      setCurrentPage(page, Number(selectedDocsToShow))
    }
  }

  const handlePageSizeChange = (pageSize: string) => {
    if (setCurrentPage && setCurrentPageSize) {
      setCurrentPage(1, Number(pageSize))
      setSelectedDocsToShow(pageSize)
      setCurrentPageSize(Number(pageSize))
    }
  }
  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      className="w-full gap-4"
      align={AlignItems.CENTER}
      justify={JustifyContent.END}
    >
      <FlexContainer className="gap-1" align={AlignItems.END} justify={JustifyContent.END}>
        <PaginationButton
          currentPage={currentPage}
          totalPage={totalPage}
          setCurrentPage={handlePageChange}
        />
      </FlexContainer>
      <Select value={selectedDocsToShow} onValueChange={handlePageSizeChange}>
        <SelectTrigger className={'w-[20%]'}>
          <SelectValue />
        </SelectTrigger>
        <SelectContent className="z-505">
          <SelectItem value={'5'}>5</SelectItem>
          <SelectItem value={'10'}>10</SelectItem>
          <SelectItem value={'15'}>15</SelectItem>
          <SelectItem value={'20'}>20</SelectItem>
        </SelectContent>
      </Select>
    </FlexContainer>
  )
}

export default ClientPagination
