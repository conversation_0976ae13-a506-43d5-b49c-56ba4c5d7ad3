'use client'

import Link from 'next/link'
import { useState } from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON>, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../../_cssComp/FlexContainer'
import { Button } from '../../Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../Select'

interface Props {
  totalPage: number
  currentPage: number
  slug: string
  queryKey: string
  queryParams: Record<string, any>
  setCurrentPage?: (page: number) => void
}

const PaginationButton = ({
  slug,
  queryParams,
  queryKey,
  currentPage,
  totalPage,
  range = 2,
}: {
  slug: string
  queryParams: Record<string, any>
  queryKey: string
  currentPage: number
  totalPage: number
  range?: number
}) => {
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'

  const pageQueryBuilder = (
    slug: string,
    queryParams: Record<string, any>,
    queryKey: string,
    chosenValue: string,
  ) => {
    const queryParamsReplace = { ...queryParams }
    queryParamsReplace[queryKey] = chosenValue
    const queryStr = new URLSearchParams(queryParamsReplace).toString()
    return `${slug}?${queryStr}`
  }

  const pages: (number | string)[] = [] // Holds page numbers and dots

  pages.push(1)

  if (currentPage > 3) {
    pages.push('...')
  }

  for (
    let i = Math.max(range, currentPage - 1);
    i <= Math.min(totalPage - 1, currentPage + 1);
    i++
  ) {
    pages.push(i)
  }

  if (currentPage < totalPage - range) {
    pages.push('...')
  }

  if (totalPage > 1) {
    pages.push(totalPage)
  }

  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      className="w-full gap-2"
      align={AlignItems.CENTER}
      justify={JustifyContent.END}
    >
      {pages.map((page, index) =>
        page === '...' ? (
          <span key={`dots-${index}`}>...</span>
        ) : (
          <Link
            key={index}
            href={`${HOST_URL}/${pageQueryBuilder(slug, queryParams, queryKey, String(page))}`}
          >
            <Button key={page} variant={page === currentPage ? 'selected' : 'default'}>
              {page}
            </Button>
          </Link>
        ),
      )}
    </FlexContainer>
  )
}

function ServerPagination(props: Props) {
  const { totalPage, currentPage, setCurrentPage, slug, queryKey, queryParams } = props
  const [selectedDocsToShow, setSelectedDocsToShow] = useState('5')

  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      className="w-full gap-4"
      align={AlignItems.CENTER}
      justify={JustifyContent.END}
    >
      <FlexContainer className="gap-1" align={AlignItems.END} justify={JustifyContent.END}>
        <PaginationButton
          currentPage={currentPage}
          totalPage={totalPage}
          slug={slug}
          queryKey={queryKey}
          queryParams={queryParams}
        />
      </FlexContainer>
      <Select defaultValue={'5'} value={selectedDocsToShow} onValueChange={setSelectedDocsToShow}>
        <SelectTrigger className={'w-[20%]'}>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={'5'}>5</SelectItem>
          <SelectItem value={'10'}>10</SelectItem>
          <SelectItem value={'15'}>15</SelectItem>
          <SelectItem value={'20'}>20</SelectItem>
        </SelectContent>
      </Select>
    </FlexContainer>
  )
}

export default ServerPagination
