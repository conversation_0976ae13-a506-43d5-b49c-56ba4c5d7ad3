import { File } from 'lucide-react'
import NextImage from 'next/image'
import React, { useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import { Label } from '../Label'
import Text from '../Text'
import Title, { HeaderLevel } from '../Title'

interface Props {
  title?: string
  existingImage?: string
  description?: string
  handleChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
}

function UploadBox(props: Props) {
  const { title, description, existingImage, handleChange } = props
  const [file, setFile] = useState<File | undefined | null>()
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleExistingImageOrFile = () => {
    if (file) {
      return URL.createObjectURL(file)
    } else if (existingImage) {
      return existingImage
    }

    return ''
  }

  const onFileAdded = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (handleChange) {
      handleChange(event)
    }
    handleShowImage(event.target.files?.[0])
  }

  const handleShowImage = (file: File | undefined | null) => {
    setFile(file)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(false)
    const dataTransfer = {
      target: { files: e.dataTransfer.files },
    } as unknown as React.ChangeEvent<HTMLInputElement>
    handleChange?.(dataTransfer)
    handleShowImage(e.dataTransfer.files[0])
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setDragOver(false)
  }

  return (
    <div
      tabIndex={0}
      className={cn('w-full', dragOver && 'cursor-copy')}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <FlexContainer
        className={cn(
          'w-full relative border-gray-400 hover:border-black border-2 border-dashed bg-main-lightest cursor-pointer gap-2',
          !file && !existingImage && 'p-8',
          dragOver && 'border-black border-solid',
        )}
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
        justify={JustifyContent.CENTER}
        onClick={handleClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={onFileAdded}
          accept="image/*"
          style={{ display: 'none' }}
        />
        {file || existingImage ? (
          <div className="relative w-full max-w-[500px] h-[300px]">
            <NextImage
              style={{
                objectFit: 'contain',
              }}
              fill
              src={handleExistingImageOrFile()}
              alt={'Image to sketch preview'}
            />
          </div>
        ) : (
          <>
            <File />
            <Title level={HeaderLevel.H4}>{title ? title : 'Upload Image'}</Title>
            <Text variant="description">Click or drag and drop onto this box to upload</Text>
            {description && <Label>{description}</Label>}
          </>
        )}
      </FlexContainer>
    </div>
  )
}

export default UploadBox
