import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import * as React from 'react'

import { cn } from '@/lib/utils'

// const buttonVariants = cva(
//   'inline-flex items-center text-text justify-center whitespace-nowrap rounded-base text-sm font-base ring-offset-white transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
//   {
//     variants: {
//       variant: {
//         default:
//           'bg-main border-2 border-border dark:border-dark-border shadow-light dark:shadow-dark hover:translate-x-box-shadow-x hover:translate-y-box-shadow-y hover:shadow-none dark:hover:shadow-none',
//         noShadow: 'bg-main border-2 border-border dark:border-dark-border',
//         neutral:
//           'bg-white dark:bg-secondary-black dark:text-dark-text border-2 border-border dark:border-dark-border shadow-light dark:shadow-dark hover:translate-x-box-shadow-x hover:translate-y-box-shadow-y hover:shadow-none dark:hover:shadow-none',
//         reverse:
//           'bg-main border-2 border-border dark:border-dark-border hover:translate-x-reverse-box-shadow-x hover:translate-y-reverse-box-shadow-y hover:shadow-light dark:hover:shadow-dark',
//       },
//       size: {
//         default: 'h-10 px-4 py-2',
//         sm: 'h-9 px-3',
//         lg: 'h-11 px-8',
//         icon: 'h-10 w-10',
//       },
//     },
//     defaultVariants: {
//       variant: 'default',
//       size: 'default',
//     },
//   },
// )

const buttonVariants = cva(
  'inline-flex items-center cursor-pointer text-text justify-center whitespace-nowrap text-sm font-base ring-offset-white transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-black focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'bg-sec-rust-orange border-2 border-border dark:border-dark-border hover:bg-sec-rust-orange-lighter dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
        secondary:
          'bg-white border-2 border-border dark:border-dark-border hover:bg-white-hover dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
        emphasis:
          'bg-black text-white border-2 border-border dark:border-dark-border hover:bg-white hover:text-black dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
        positive:
          'bg-accent3 border-2 border-border dark:border-dark-border hover:bg-accent3-lighter dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
        negative:
          'bg-accent5 border-2 border-border dark:border-dark-border hover:bg-accent2-lighter dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
        selected:
          'bg-black border-2 border-border text-white hover:bg-white hover:text-black shadow-none',
        love: 'bg-accent2 border-2 border-border text-black hover:bg-accent2-lighter hover:text-black dark:shadow-dark active:translate-x-box-shadow-x active:translate-y-box-shadow-y active:shadow-none dark:active:shadow-none',
      },
      round: {
        default: 'rounded-base',
        round: 'rounded-full',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 px-3',
        lg: 'h-11 px-8',
        xl: 'h-14 px-10',
        icon: 'h-10 w-10',
      },
      state: {
        active: 'shadow-none bg-black text-white',
        inactive: '',
      },
      shadow: {
        default: 'shadow-light',
        none: 'shadow-none',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      round: 'default',
      shadow: 'default',
      state: 'inactive',
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  active?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, round, shadow, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size, round, shadow, className }),
          `flex flex-row gap-2 items-center`,
        )}
        ref={ref}
        {...props}
      />
    )
  },
)
Button.displayName = 'Button'

export { Button, buttonVariants }
