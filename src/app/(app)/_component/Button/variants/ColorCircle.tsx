import { ClassValue } from 'clsx'
import { Dispatch, SetStateAction } from 'react'
import { cn } from '@/lib/utils'

interface Props {
  color?: string
  onClick?: () => void
  active?: boolean
  className?: ClassValue
  width?: string
  height?: string
}

function ColorCircle(props: Props) {
  const { color = 'bg-white', active, onClick, className, width = '24', height = '24' } = props

  return (
    <div
      className={cn(
        `${active ? 'border-black' : 'border-slate-400'} border-2 w-${width} h-${height} rounded-full`,
        className,
        color,
      )}
      onClick={() => onClick?.()}
    ></div>
  )
}

export default ColorCircle
