import { ClassValue } from 'clsx'
import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface Props {
  icon: ReactNode
  onClick?: () => void
  active?: boolean
  className?: ClassValue
  width?: string
  height?: string
}

function IconToggleButton(props: Props) {
  const { icon, active, onClick, className, width = '24', height = '24' } = props

  return (
    <div
      className={cn(
        `${active ? 'border-black' : 'border-slate-400'} bg-white p-2 border-2 w-${width} h-${height} rounded-full`,
        className,
      )}
      onClick={() => onClick?.()}
    >
      {icon}
    </div>
  )
}

export default IconToggleButton
