import NextImage, { StaticImageData } from 'next/image'
import { ReactNode } from 'react'
import { cn } from '@/lib/utils'

import FlexContainer, { FlexDirection } from '../../_cssComp/FlexContainer'
import Title, { HeaderLevel } from '../Title'

type Props = {
  image: StaticImageData
  title: ReactNode
  caption: string
  titleLevel?: HeaderLevel
  children?: ReactNode
  className?: string
}

export default function ImageCard({
  image,
  title,
  caption,
  titleLevel = HeaderLevel.H2,
  className,
  children,
}: Props) {
  return (
    <div
      className={cn(
        `overflow-hidden rounded-base border-2 border-border dark:border-dark-border bg-main font-base shadow-light dark:shadow-dark`,
        className,
      )}
    >
      <div className={`bg-white aspect-square w-full relative`}>
        {/* <NextImage
          style={{ maxWidth: '500px', height: '500px' }}
          src={image}
          width={image.width}
          height={image.height}
          alt={caption}
        /> */}
        <NextImage src={image} fill alt={caption} />
      </div>
      <FlexContainer
        direction={FlexDirection.COL}
        className="gap-2 border-t-2 text-text border-border dark:border-dark-border p-4"
      >
        <Title level={titleLevel}>{title}</Title>
        {children}
      </FlexContainer>
    </div>
  )
}
