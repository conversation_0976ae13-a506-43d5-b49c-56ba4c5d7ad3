'use client'

import { motion, MotionProps, useScroll } from 'motion/react'
import React from 'react'
import { cn } from '@/lib/utils'
interface ScrollProgressProps extends Omit<React.HTMLAttributes<HTMLElement>, keyof MotionProps> {}

export const ScrollProgress = React.forwardRef<HTMLDivElement, ScrollProgressProps>(
  ({ className, ...props }, ref) => {
    const { scrollYProgress } = useScroll()

    return (
      <motion.div
        ref={ref}
        className={cn(
          'inset-x-0 absolute bottom-0 z-50 h-px origin-left bg-gradient-to-r from-[#A97CF8] via-[#F38CB8] to-[#FDCC92]',
          className,
        )}
        style={{
          scaleX: scrollYProgress,
        }}
        {...props}
      />
    )
  },
)

ScrollProgress.displayName = 'ScrollProgress'
