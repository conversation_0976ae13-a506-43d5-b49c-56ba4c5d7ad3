//If user's permission does not match, lock up the component, and display something else?

import { DialogTrigger } from '@radix-ui/react-dialog'
import { ClassValue } from 'clsx'
import React, { useEffect } from 'react'
import { cn } from '@/lib/utils'
import { FeaturesType } from '@/utilities/local/enums'
import SusbcriptionRedux from './SubscriptionRedux'
import { useCreditStore } from '../../_state/creditStoreState'
import { Dialog, DialogContent, DialogTitle } from '../Dialog'

interface Props {
  className?: ClassValue
  feature: FeaturesType
  chosenPlanFilter?: ('PRELUDE_PLAN' | 'MUSE_PLAN' | 'ARIA_PLAN')[]
  userFeatures: string[]
  mode: 'hide' | 'disable'
  creditCost: number
  children: React.ReactNode
  costMode?: 'onMount' | 'none'
  multiplier?: number
}

function LockComponent(props: Props) {
  const {
    className,
    feature,
    chosenPlanFilter = ['PRELUDE_PLAN', 'MUSE_PLAN', 'ARIA_PLAN'],
    userFeatures,
    mode,
    creditCost,
    children,
    costMode = 'onMount',
    multiplier = 1,
  } = props
  const register = useCreditStore((s) => s.register)
  const deregister = useCreditStore((s) => s.deregister)

  useEffect(() => {
    if (userFeatures.includes(feature) && creditCost > 0 && costMode === 'onMount') {
      register(feature, creditCost * multiplier)
      return () => {
        deregister(feature)
      }
    }
  }, [feature, creditCost, costMode, userFeatures, multiplier])

  if (userFeatures.includes(feature)) {
    return <div className={cn(className)}>{props.children}</div>
  } else if (mode === 'hide') {
    return <SusbcriptionRedux chosenPlanFilter={chosenPlanFilter} />
  } else {
    return (
      <Dialog>
        <DialogTrigger asChild>
          <div className={cn(className, 'relative inline-block')}>
            <div className="absolute inset-0 z-10" />
            <div className={cn(className, 'pointer-events-none opacity-50 z-0')}>{children}</div>
          </div>
        </DialogTrigger>
        <DialogContent className={'font-[Outfit] w-[90%] lg:w-[85%] xl:w-[80%] 2xl:w-[70%] p-8'}>
          <DialogTitle className="sr-only">Unlock your features now!</DialogTitle>
          <SusbcriptionRedux chosenPlanFilter={chosenPlanFilter} />
        </DialogContent>
      </Dialog>
    )
  }
}

export default LockComponent
