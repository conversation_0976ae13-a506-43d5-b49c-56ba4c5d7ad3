'use client'

import { ClassValue } from 'clsx'
import { useState } from 'react'
import { cn } from '@/lib/utils'
import FlexContainer, { FlexDirection } from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { SubscriptionMap } from '../../pricing/subscriptionMap'
import { Badge } from '../Badge'
import SubscriptionButton from '../Button/variants/SubscriptionButton'
import CardSolo from '../Card/CardSolo'
import { Checkbox } from '../Checkbox'
import { Label } from '../Label'
import { RadioGroup, RadioGroupItem } from '../RadioGroup'
import { Tabs, TabsList, TabsTrigger } from '../Tabs'
import Text from '../Text'
import Title, { HeaderLevel } from '../Title'

interface Props {
  chosenPlanFilter?: ('PRELUDE_PLAN' | 'MUSE_PLAN' | 'ARIA_PLAN')[]
  className?: ClassValue
}

function SusbcriptionRedux(props: Props) {
  const { chosenPlanFilter = ['PRELUDE_PLAN', 'MUSE_PLAN', 'ARIA_PLAN'], className } = props
  const [subscriptionDuration, setSubscriptionDuration] = useState('monthly')
  const [chosenPlan, setChosenPlan] = useState<string>(chosenPlanFilter[0])
  return (
    <GridContainer
      style={{
        backgroundImage: "url('/media/static/illustration/coloraria_upgrade_dialog_bg.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
      className={cn(
        className,
        'w-full place-content-center grid-cols-1 px-6 py-8 bg-white border-2 rounded-sm shadow-[3px_3px_0px_rgba(254,110,148,1)]',
      )}
    >
      <FlexContainer direction={FlexDirection.ROW} className="gap-2" wrap={false}>
        <Title className="flex-shrink min-w-0">
          {`Looks like you've found a `}
          <span className="bg-linear-to-r from-purple-400 to-pink-600 bg-clip-text text-transparent">
            premium
          </span>
          {` feature!`}
        </Title>
      </FlexContainer>
      <Text>Upgrade now to use it!</Text>
      <Tabs value={subscriptionDuration} onValueChange={setSubscriptionDuration} className="my-4">
        <TabsList className="grid w-full grid-cols-2 mt-4">
          <TabsTrigger value="monthly">Monthly</TabsTrigger>
          <TabsTrigger value="yearly">
            Yearly <Badge className="ml-2">30% off!</Badge>
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <RadioGroup defaultValue={chosenPlan} value={chosenPlan} onValueChange={setChosenPlan}>
        <GridContainer className="w-full grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-4">
          <FlexContainer direction={FlexDirection.COL} className="w-full gap-2">
            {chosenPlanFilter.includes('PRELUDE_PLAN') && (
              <Label htmlFor="prelude_plan" className="w-full cursor-pointer">
                <CardSolo className="bg-accent4-lighter w-full">
                  <GridContainer className="grid-cols-4 w-full">
                    <GridItem colSpan={ColSpan.SPAN_3}>
                      <FlexContainer direction={FlexDirection.COL}>
                        <Text variant="emphasis" size="lg">
                          {
                            SubscriptionMap['PRELUDE_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].title
                          }
                        </Text>
                        <Text variant="description" size="sm">
                          {`${
                            SubscriptionMap['PRELUDE_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].price
                          } / month`}
                        </Text>
                      </FlexContainer>
                    </GridItem>
                    <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
                      <RadioGroupItem id="prelude_plan" value={'PRELUDE_PLAN'} />
                    </GridItem>
                  </GridContainer>
                </CardSolo>
              </Label>
            )}
            {chosenPlanFilter.includes('MUSE_PLAN') && (
              <Label htmlFor="muse_plan" className="w-full cursor-pointer">
                <CardSolo className="bg-accent4-lighter w-full">
                  <GridContainer className="grid-cols-4 w-full">
                    <GridItem colSpan={ColSpan.SPAN_3}>
                      <FlexContainer direction={FlexDirection.COL}>
                        <Text variant="emphasis" size="lg">
                          {
                            SubscriptionMap['MUSE_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].title
                          }
                        </Text>
                        <Text variant="description" size="sm">
                          {`${
                            SubscriptionMap['MUSE_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].price
                          } / month`}
                        </Text>
                      </FlexContainer>
                    </GridItem>
                    <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
                      <RadioGroupItem id="muse_plan" value="MUSE_PLAN" />
                    </GridItem>
                  </GridContainer>
                </CardSolo>
              </Label>
            )}
            {chosenPlanFilter.includes('MUSE_PLAN') && (
              <Label htmlFor="aria_plan" className="w-full cursor-pointer">
                <CardSolo className="bg-accent4-lighter w-full">
                  <GridContainer className="grid-cols-4 w-full">
                    <GridItem colSpan={ColSpan.SPAN_3}>
                      <FlexContainer direction={FlexDirection.COL}>
                        <Text variant="emphasis" size="lg">
                          {
                            SubscriptionMap['ARIA_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].title
                          }
                        </Text>
                        <Text variant="description" size="sm">
                          {`${
                            SubscriptionMap['ARIA_PLAN'][
                              subscriptionDuration as 'monthly' | 'yearly'
                            ].price
                          } / month`}
                        </Text>
                      </FlexContainer>
                    </GridItem>
                    <GridItem colSpan={ColSpan.SPAN_1} className="justify-self-end self-center">
                      <RadioGroupItem id="aria_plan" value="ARIA_PLAN" />
                    </GridItem>
                  </GridContainer>
                </CardSolo>
              </Label>
            )}
            <SubscriptionButton
              subscriptionName={
                SubscriptionMap[chosenPlan as 'PRELUDE_PLAN' | 'MUSE_PLAN' | 'ARIA_PLAN'][
                  subscriptionDuration as 'monthly' | 'yearly'
                ].title
              }
              subscriptionDuration={
                SubscriptionMap[chosenPlan as 'PRELUDE_PLAN' | 'MUSE_PLAN' | 'ARIA_PLAN'][
                  subscriptionDuration as 'monthly' | 'yearly'
                ].subscriptionDuration
              }
            />
          </FlexContainer>
          <FlexContainer
            direction={FlexDirection.COL}
            className="w-full gap-2 bg-white border-2 p-4 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)]"
          >
            <Title level={HeaderLevel.H4}>Available Features</Title>
            <ul className="flex gap-2 flex-col">
              {SubscriptionMap[
                chosenPlan as 'PRELUDE_PLAN' | 'MUSE_PLAN' | 'ARIA_PLAN'
              ].features.map((feature) => {
                if (feature.available) {
                  return <li key={feature.description}>{feature.description}</li>
                } else {
                  return (
                    <li key={feature.description} className="text-gray-500 line-through">
                      {feature.description}
                    </li>
                  )
                }
              })}
            </ul>
          </FlexContainer>
        </GridContainer>
      </RadioGroup>
    </GridContainer>
  )
}

export default SusbcriptionRedux
