import { ClassValue } from 'clsx'
import React, { PropsWithChildren } from 'react'

import { cn } from '@/lib/utils'

export enum HeaderLevel {
  H1 = 'h1',
  H2 = 'h2',
  H3 = 'h3',
  H4 = 'h4',
  H5 = 'h5',
  H6 = 'h6',
}

interface Props {
  level?: HeaderLevel
  className?: ClassValue
}

function Title(props: PropsWithChildren<Props>) {
  const { children, level, className } = props

  switch (level) {
    case HeaderLevel.H1:
      return (
        <h1 className={cn('text-4xl font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h1>
      )
    case HeaderLevel.H2:
      return (
        <h2 className={cn('text-3xl font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h2>
      )
    case HeaderLevel.H3:
      return (
        <h3 className={cn('text-2xl font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h3>
      )
    case HeaderLevel.H4:
      return (
        <h4 className={cn('text-xl font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h4>
      )
    case HeaderLevel.H5:
      return (
        <h5 className={cn('text-lg font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h5>
      )
    case HeaderLevel.H6:
      return (
        <h6 className={cn('text-base font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h6>
      )
    default:
      return (
        <h1 className={cn('text-4xl font-bold text-black dark:text-gray-200', className)}>
          {children}
        </h1>
      )
  }
}

export default Title
