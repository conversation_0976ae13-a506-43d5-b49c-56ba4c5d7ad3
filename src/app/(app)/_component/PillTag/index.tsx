import { ClassValue } from 'clsx'
import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import Text from '../Text'

interface Props {
  text?: string | ReactNode | null
  className?: string
  textClassName?: ClassValue
  isButton?: boolean
  onClick?: () => void
}

function PillTag(props: Props) {
  const { text, className, textClassName, isButton, onClick } = props
  return (
    <div
      onClick={onClick}
      className={cn(
        `border-black border-2 rounded-full shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-sec-rust-orange-lighter p-3`,
        `${isButton ? 'hover:bg-sec-rust-orange cursor-pointer active:shadow-none active:translate-x-box-shadow-x active:translate-y-box-shadow-y' : ''}`,
        className,
      )}
    >
      <Text variant="emphasis" className={cn(textClassName)}>
        {text}
      </Text>
    </div>
  )
}

export default PillTag
