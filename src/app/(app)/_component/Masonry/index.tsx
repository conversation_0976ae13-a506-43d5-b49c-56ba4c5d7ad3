import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface MasonryGridProps {
  children: React.ReactElement[] // easier if children are ReactElements
  className?: string
  getHeight?: (child: React.ReactElement) => number // function to estimate height
}

const MasonryGrid: React.FC<MasonryGridProps> = ({ children, className, getHeight }) => {
  const [columnCount, setColumnCount] = useState(1)

  const updateColumnCount = () => {
    const width = window.innerWidth
    if (width >= 1536) setColumnCount(4)
    else if (width >= 1024) setColumnCount(3)
    else if (width >= 768) setColumnCount(2)
    else setColumnCount(1)
  }

  useEffect(() => {
    updateColumnCount()
    window.addEventListener('resize', updateColumnCount)
    return () => window.removeEventListener('resize', updateColumnCount)
  }, [])

  const childrenArray = React.Children.toArray(children) as React.ReactElement[]

  // Initialize columns and heights
  const columns: React.ReactElement[][] = Array.from({ length: columnCount }, () => [])
  const columnsHeights: number[] = new Array(columnCount).fill(0)

  childrenArray.forEach((child) => {
    // Estimate height of the child, fallback to 300 if no getHeight provided
    const height = getHeight ? getHeight(child) : 300

    // Find the column with the minimum height
    const shortestColumnIndex = columnsHeights.indexOf(Math.min(...columnsHeights))

    // Assign child to that column
    columns[shortestColumnIndex].push(child)

    // Add height to that column
    columnsHeights[shortestColumnIndex] += height
  })

  return (
    <div className={cn('flex gap-4', className)}>
      {columns.map((column, idx) => (
        <div key={idx} className="flex flex-col gap-4 flex-1">
          {column.map((child) => (
            <React.Fragment key={child.key ?? undefined}>{child}</React.Fragment>
          ))}
        </div>
      ))}
    </div>
  )
}

export default MasonryGrid
