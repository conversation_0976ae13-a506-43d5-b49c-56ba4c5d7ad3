'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ReactNode, useRef, useState } from 'react'

import { cn, PropsWithChildrenAndClassName } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import Text from '../Text'

interface INavigationMenuLinkItem {
  link: string
  icon?: ReactNode
  label: ReactNode
}

interface INavigationMenuTriggerItem {
  icon?: ReactNode
  label: ReactNode
}

function NavigationMenu(props: PropsWithChildrenAndClassName) {
  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      align={AlignItems.CENTER}
      justify={JustifyContent.CENTER}
      className={cn('w-full h-full gap-4', props.className)}
    >
      {props.children}
    </FlexContainer>
  )
}

export function NavigationMenuLinkItem(
  props: PropsWithChildrenAndClassName<INavigationMenuLinkItem>,
) {
  const { link, label, icon } = props
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const pathName = usePathname()

  const isCurrentPage = (link: string, path: string) => {
    const splitPath = path.split('/')
    const splitLink = link.split('/')
    if (splitPath[splitPath.length - 1] === '') {
      return false
    }
    return splitLink[splitLink.length - 1].includes(splitPath[splitPath.length - 1])
  }
  return (
    <Link href={`${HOST_URL}/${link}`}>
      <FlexContainer
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        className={cn(
          `${isCurrentPage(link, pathName) ? 'bg-accent2' : ''} relative p-2 pr-3 transition-colors duration-200 rounded-xl hover:bg-accent2-lighter gap-2`,
          props.className,
        )}
      >
        {icon}
        <div>
          <Text size="base" variant="emphasis">
            {label}
          </Text>
        </div>
      </FlexContainer>
    </Link>
  )
}

export function NavigationMenuTriggerItem(
  props: PropsWithChildrenAndClassName<INavigationMenuTriggerItem>,
) {
  const { label, icon } = props
  const [onHover, setOnHover] = useState(false)
  const hoverTimeout = useRef<NodeJS.Timeout>(null)
  return (
    <FlexContainer
      direction={FlexDirection.ROW}
      className={cn(
        `cursor-pointer gap-2 relative p-2 transition-colors duration-200 rounded-xl hover:bg-accent2-lighter hover:rounded-xl`,
        props.className,
      )}
      align={AlignItems.CENTER}
      onMouseEnter={() => {
        clearTimeout(hoverTimeout.current!)
        setOnHover(true)
      }}
      onMouseLeave={() => {
        hoverTimeout.current = setTimeout(() => setOnHover(false), 300)
      }}
    >
      {icon}
      <div
        // className={cn(
        //   `cursor-pointer relative p-2 transition-colors duration-200 rounded-xl hover:bg-accent2-lighter hover:rounded-xl`,
        //   props.className,
        // )}
        onClick={() => setOnHover(!onHover)}
      >
        <Text size="base" variant="emphasis">
          {label}
        </Text>
        {onHover && (
          <div onMouseEnter={() => clearTimeout(hoverTimeout.current!)}>{props.children}</div>
        )}
      </div>
    </FlexContainer>
  )
}

export function NavigationMenuTriggerContent(props: PropsWithChildrenAndClassName) {
  return (
    <div
      className={cn(
        'absolute top-full mx-2 my-4 border-black border-2 bg-white shadow-lg rounded-lg opacity-100 transition-opacity',
        'max-w-[calc(100vw-1rem)]', // Prevents overflow
        'left-1/2 transform -translate-x-1/2', // Center horizontally
        // 'right-0 left-auto', // Default: Align right
        // 'lg:left-0 lg:right-auto', // Fallback: Align left on larger screens if needed
        'overflow-hidden', // Ensures content stays within bounds
        props.className,
        Spacing.ContentPadding,
      )}
    >
      {props.children}
    </div>
  )
}

interface INavigationMainMenuCard {
  title?: string
  icon?: ReactNode
  subtitle?: string
  image?: string
}

export function NavigationMainMenuCard(
  props: PropsWithChildrenAndClassName<INavigationMainMenuCard>,
) {
  const { title, icon, subtitle, className } = props
  return (
    <FlexContainer
      direction={FlexDirection.COL}
      className={cn(
        'border-black border-2 w-full gap-2 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-accent2-lighter hover:bg-accent2 p-2',
        className,
      )}
    >
      <FlexContainer direction={FlexDirection.ROW} className="gap-2" align={AlignItems.CENTER}>
        {icon}
        <Text variant="emphasis" size="lg">
          {title}
        </Text>
      </FlexContainer>
      <Text variant="body" size="sm">
        {subtitle}
      </Text>
    </FlexContainer>
  )
}

interface INavigationMenuCard {
  title?: string
  icon?: ReactNode
  subtitle?: string
}

export function NavigationMenuCard(props: PropsWithChildrenAndClassName<INavigationMenuCard>) {
  const { title, icon, subtitle } = props
  return (
    <FlexContainer
      direction={FlexDirection.COL}
      className="hover:border-black hover:bg-accent border-transparent  border-2 w-full gap-2 rounded-sm bg-white p-2"
    >
      <FlexContainer direction={FlexDirection.ROW} className="gap-2" align={AlignItems.CENTER}>
        {icon}
        <Text variant="emphasis" size="base">
          {title}
        </Text>
      </FlexContainer>
      <Text variant="description" size="sm">
        {subtitle}
      </Text>
    </FlexContainer>
  )
}

export default NavigationMenu
