import NextImage from 'next/image'
import { cn } from '@/lib/utils'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import Text from '../Text'
import Title from '../Title'

interface Props {
  title?: string
  description?: string
  className?: string
}

function NoDataIllustratedMessage(props: Props) {
  const { title = 'No data', description = 'No data', className } = props
  return (
    <FlexContainer
      direction={FlexDirection.COL}
      className={cn('gap-4 p-4', className)}
      justify={JustifyContent.CENTER}
      align={AlignItems.CENTER}
    >
      <div className="w-full relative aspect-square">
        <div className="absolute inset-0 z-0">
          <NextImage
            src="/media/static/blobIllustration/redBlob.svg"
            alt=""
            fill
            className="object-contain"
            draggable={false}
          />
        </div>
        <div className="absolute inset-0 z-10">
          <NextImage
            src="/media/static/illustration/noData.svg"
            alt="No data"
            fill
            className="object-contain"
            draggable={false}
          />
        </div>
      </div>
      <FlexContainer
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
        justify={JustifyContent.CENTER}
        className="gap-2 w-full"
      >
        <Title className="text-center">{title}</Title>
        <Text className="text-center">{description}</Text>
      </FlexContainer>
    </FlexContainer>
  )
}

export default NoDataIllustratedMessage
