import { PropsWithChildren } from 'react'
import Flex<PERSON>ontainer, { FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import { cn } from '@/lib/utils'
import Text from '../../Text'

interface Props {
  title?: string
  className?: string
}

function CardSolo(props: PropsWithChildren<Props>) {
  const { title, children, className } = props
  return (
    <div
      className={cn(
        `w-full border-black border-2 rounded-sm shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-accent2-lighter p-4`,
        className,
      )}
    >
      <FlexContainer direction={FlexDirection.COL}>
        <Text variant="emphasis" size="lg">
          {title}
        </Text>
        {children}
      </FlexContainer>
    </div>
  )
}

export default CardSolo
