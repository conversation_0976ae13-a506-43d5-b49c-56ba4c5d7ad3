'use client'

import { ClassValue } from 'clsx'
import { PropsWithChildren, ReactNode, useEffect, useRef, useState } from 'react'

import { cn } from '@/lib/utils'
import Title, { HeaderLevel } from '../../Title'

type Props = {
  className?: ClassValue
  titleClassName?: ClassValue
  title?: ReactNode
  subtitle?: string
  children?: ReactNode
  footer?: ReactNode
}

export default function Card(props: PropsWithChildren<Props>) {
  const { className, titleClassName, title, children } = props

  return (
    <div
      className={cn(
        'w-full border-black border-2 rounded-md shadow-[3px_3px_0px_rgba(0,0,0,1)] bg-white',
        className,
      )}
    >
      <div className="block">
        <div className={cn(`flex bg-accent2-lighter w-full p-4`, titleClassName)}>{title}</div>
        <article className="w-full">
          <figure className="w-full h-1/2 border-black border-b-2"></figure>
          <div className="px-8 py-8 text-left">{children}</div>
        </article>
      </div>
    </div>
  )
}
