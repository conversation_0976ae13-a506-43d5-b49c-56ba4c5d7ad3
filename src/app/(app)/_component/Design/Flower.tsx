import React from 'react'

const NeobrutalistFlower = () => {
  const [isAnimated, setIsAnimated] = React.useState(false)
  const [currentColor, setCurrentColor] = React.useState('#FF6B35')

  const colors = {
    orange: '#FF6B35',
    yellow: '#F7CB15',
    pink: '#FF5E5B',
    teal: '#3BCEAC',
    green: '#0EAD69',
  }

  const petalPositions = [
    { rotation: 0, top: 50, left: 100 },
    { rotation: 45, top: 70, left: 150 },
    { rotation: 90, top: 120, left: 170 },
    { rotation: 135, top: 170, left: 150 },
    { rotation: 180, top: 190, left: 100 },
    { rotation: 225, top: 170, left: 50 },
    { rotation: 270, top: 120, left: 30 },
    { rotation: 315, top: 70, left: 50 },
  ]

  const animateFlower = () => {
    setIsAnimated((prev) => !prev)
  }

  const changeColor = (color: string) => {
    setCurrentColor(color)
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="relative w-full h-[400px]">
        {/* Flower petals */}
        {petalPositions.map((position, index) => (
          <div
            key={index}
            className="absolute w-[100px] h-[100px] border-3 border-black transition-all duration-300"
            style={{
              backgroundColor: currentColor,
              borderRadius: '50% 50% 0 50%',
              transform: `rotate(${position.rotation}deg) scale(${isAnimated ? 1.2 : 1})`,
              top: `${position.top}px`,
              left: `${position.left}px`,
              boxShadow: isAnimated
                ? '10px 10px 0px rgba(0, 0, 0, 0.8)'
                : '8px 8px 0px rgba(0, 0, 0, 0.8)',
              transformOrigin: 'bottom right',
            }}
          ></div>
        ))}

        {/* Flower center */}
        <div
          className="absolute w-[80px] h-[80px] rounded-full border-3 border-black flex items-center justify-center text-2xl z-10"
          style={{
            backgroundColor: currentColor,
            top: '110px',
            left: '110px',
            boxShadow: '5px 5px 0px rgba(0, 0, 0, 0.8)',
            transform: `rotate(${isAnimated ? '5deg' : '-5deg'}) scale(${isAnimated ? 1.2 : 1})`,
            transition: 'transform 0.3s ease',
          }}
        >
          ✿
        </div>

        {/* Stem and leaf */}
        <div
          className="absolute w-[30px] h-[180px] border-3 border-black -z-10"
          style={{
            backgroundColor: '#4CB944',
            top: '190px',
            left: '135px',
            boxShadow: '5px 5px 0px rgba(0, 0, 0, 0.8)',
          }}
        ></div>
        <div
          className="absolute w-[80px] h-[60px] border-3 border-black -z-10"
          style={{
            backgroundColor: '#4CB944',
            borderRadius: '50% 50% 50% 0',
            transform: 'rotate(45deg)',
            top: '250px',
            left: '160px',
            boxShadow: '5px 5px 0px rgba(0, 0, 0, 0.8)',
          }}
        ></div>
      </div>

      {/* Controls */}
      <div className="mt-64 flex flex-col items-center gap-4">
        <button
          onClick={animateFlower}
          className="px-6 py-3 bg-white border-3 border-black font-bold text-base cursor-pointer transition-all duration-200 hover:-translate-y-0.5 active:translate-y-0.5"
          style={{
            transform: 'rotate(-2deg)',
            boxShadow: '5px 5px 0px rgba(0, 0, 0, 0.8)',
          }}
        >
          {isAnimated ? 'BLOOM AGAIN' : 'MAKE IT BLOOM'}
        </button>

        <div className="font-bold mt-4 mb-2">CHOOSE COLOR</div>
        <div className="flex gap-2">
          {Object.entries(colors).map(([name, color]) => (
            <div
              key={name}
              onClick={() => changeColor(color)}
              className="w-10 h-10 border-3 border-black cursor-pointer hover:scale-110 transition-transform"
              style={{
                backgroundColor: color,
                boxShadow: '3px 3px 0px rgba(0, 0, 0, 0.8)',
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default NeobrutalistFlower
