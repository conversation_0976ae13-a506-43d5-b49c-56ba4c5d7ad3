const WaveBackgroundV2 = ({
  gradientStart = 'hsl(161.6deg 100% 41.57%)',
  gradientEnd = 'hsl(93.91deg 93.24% 70.98%)',
  inverse = false,
  className = '',
  style = {},
}) => {
  return (
    <svg
      className={className}
      style={{
        width: '110%',
        height: '100%',
        transform: `${inverse ? `rotate(180deg)` : ''}`,
        ...style,
      }}
      viewBox="0 0 1440 320"
      preserveAspectRatio="xMinYMax slice"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="sw-gradient-1" x1="0" x2="0" y1="1" y2="0">
          <stop stopColor={gradientStart} offset="0%" />
          <stop stopColor={gradientEnd} offset="100%" />
        </linearGradient>
      </defs>
      <path
        fill="url(#sw-gradient-1)"
        d="M0,256L48,245.3C96,235,192,213,288,192C384,171,480,149,576,149.3C672,149,768,171,864,181.3C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
      />
    </svg>
  )
}

export function WaveBackgroundV2SVG() {
  return `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320">
      <defs>
        <linearGradient id="sw-gradient-1" x1="0" x2="0" y1="1" y2="0">
          <stop stop-color="hsl(161.6deg 100% 41.57%)" offset="0%" />
          <stop stop-color="hsl(93.91deg 93.24% 70.98%)" offset="100%" />
        </linearGradient>
      </defs>
      <path fill="url(#sw-gradient-1)" d="M0,256L48,245.3C96,235,192,213,288,192C384,171,480,149,576,149.3C672,149,768,171,864,181.3C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"/>
    </svg>
  `
    .replace(/\n/g, '')
    .trim()
}

export default WaveBackgroundV2
