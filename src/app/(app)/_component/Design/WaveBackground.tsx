const WaveBackground = ({
  gradientStart = 'rgba(243, 106, 62, 1)',
  gradientEnd = 'rgba(255, 179, 11, 1)',
  inverse = false,
  className = '',
  style = {},
}) => {
  return (
    <svg
      className={className}
      style={{
        width: '100%',
        height: 'auto',
        transform: `${inverse ? `rotate(180deg)` : ''}`,
        ...style,
      }}
      viewBox="0 0 1440 300"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="sw-gradient-0" x1="0" x2="0" y1="1" y2="0">
          <stop stopColor={gradientStart} offset="0%" />
          <stop stopColor={gradientEnd} offset="100%" />
        </linearGradient>
      </defs>
      <path
        fill="url(#sw-gradient-0)"
        d="M0,111L34.3,98.7C68.6,86,137,62,206,67.8C274.3,74,343,111,411,135.7C480,160,549,173,617,166.5C685.7,160,754,136,823,154.2C891.4,173,960,234,1029,215.8C1097.1,197,1166,99,1234,80.2C1302.9,62,1371,123,1440,129.5C1508.6,136,1577,86,1646,111C1714.3,136,1783,234,1851,265.2C1920,296,1989,259,2057,246.7C2125.7,234,2194,247,2263,234.3C2331.4,222,2400,185,2469,185C2537.1,185,2606,222,2674,240.5C2742.9,259,2811,259,2880,271.3C2948.6,284,3017,308,3086,314.5C3154.3,321,3223,308,3291,308.3C3360,308,3429,321,3497,271.3C3565.7,222,3634,111,3703,74C3771.4,37,3840,74,3909,129.5C3977.1,185,4046,259,4114,271.3C4182.9,284,4251,234,4320,209.7C4388.6,185,4457,185,4526,191.2C4594.3,197,4663,210,4731,191.2C4800,173,4869,123,4903,98.7L4937.1,74L4937.1,370L4902.9,370C4868.6,370,4800,370,4731,370C4662.9,370,4594,370,4526,370C4457.1,370,4389,370,4320,370C4251.4,370,4183,370,4114,370C4045.7,370,3977,370,3909,370C3840,370,3771,370,3703,370C3634.3,370,3566,370,3497,370C3428.6,370,3360,370,3291,370C3222.9,370,3154,370,3086,370C3017.1,370,2949,370,2880,370C2811.4,370,2743,370,2674,370C2605.7,370,2537,370,2469,370C2400,370,2331,370,2263,370C2194.3,370,2126,370,2057,370C1988.6,370,1920,370,1851,370C1782.9,370,1714,370,1646,370C1577.1,370,1509,370,1440,370C1371.4,370,1303,370,1234,370C1165.7,370,1097,370,1029,370C960,370,891,370,823,370C754.3,370,686,370,617,370C548.6,370,480,370,411,370C342.9,370,274,370,206,370C137.1,370,69,370,34,370L0,370Z"
      />
    </svg>
  )
}

export default WaveBackground
