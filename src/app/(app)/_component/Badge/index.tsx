import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { ComponentPropsWithoutRef, ElementType } from 'react'
import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground',
      },
      type: {
        default: '',
        button: 'cursor-pointer',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

type BadgeOwnProps<T extends ElementType> = {
  as?: T
} & VariantProps<typeof badgeVariants> &
  ComponentPropsWithoutRef<T>

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge<T extends ElementType = 'div'>({
  as,
  className,
  variant,
  type,
  ...props
}: BadgeOwnProps<T>) {
  const isButton = type === 'button'
  const Component = as ?? (type === 'button' ? 'button' : 'div')

  return (
    <Component
      className={cn(
        badgeVariants({ variant, type }),
        'flex flex-row gap-2 items-center',
        className,
      )}
      {...(isButton ? { type: 'button' } : {})} // ✅ conditionally spread "type"
      {...props}
    />
  )
}

export { Badge, badgeVariants }
