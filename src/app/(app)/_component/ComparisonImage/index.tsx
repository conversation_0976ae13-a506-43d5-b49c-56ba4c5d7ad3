'use client'

import Image from 'next/image'
import { useState, useRef, useEffect } from 'react'

interface ImageComparisonProps {
  original: string
  modified: string
  alt?: string
  aspectRatio?: string
}

export default function ImageComparison({
  original,
  modified,
  alt = 'Image Comparison',
  aspectRatio = '16/9',
}: ImageComparisonProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [position, setPosition] = useState(50)
  const [dragging, setDragging] = useState(false)

  const startDrag = () => setDragging(true)
  const stopDrag = () => setDragging(false)

  const onMove = (e: MouseEvent | TouchEvent) => {
    if (!dragging || !containerRef.current) return
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const rect = containerRef.current.getBoundingClientRect()
    const newX = ((clientX - rect.left) / rect.width) * 100
    setPosition(Math.max(0, Math.min(100, newX)))
  }

  useEffect(() => {
    window.addEventListener('mousemove', onMove)
    window.addEventListener('touchmove', onMove)
    window.addEventListener('mouseup', stopDrag)
    window.addEventListener('touchend', stopDrag)
    return () => {
      window.removeEventListener('mousemove', onMove)
      window.removeEventListener('touchmove', onMove)
      window.removeEventListener('mouseup', stopDrag)
      window.removeEventListener('touchend', stopDrag)
    }
  }, [dragging])

  return (
    <div
      ref={containerRef}
      className="relative w-full overflow-hidden rounded-xl group"
      style={{ aspectRatio }}
      role="region"
      aria-label="Image Comparison"
    >
      {/* Bottom (Original) Image */}
      <Image
        src={original}
        alt={`${alt} - Original`}
        fill
        className="object-contain select-none"
        draggable={false}
        priority
      />

      {/* Top (Modified) Image, clipped */}
      <div className="absolute inset-0">
        <div
          className="relative w-full h-full transition-all duration-300 ease-in-out"
          style={{
            clipPath: `polygon(0 0, ${position}% 0, ${position}% 100%, 0 100%)`,
            WebkitClipPath: `polygon(0 0, ${position}% 0, ${position}% 100%, 0 100%)`,
          }}
        >
          <Image
            src={modified}
            alt={`${alt} - Modified`}
            fill
            className="object-contain select-none"
            draggable={false}
            priority
          />
        </div>
      </div>

      <span
        className="absolute top-2 left-2 z-30 bg-white/80 text-black text-xs px-2 py-1 rounded border border-black shadow-sm"
        aria-hidden="true"
      >
        Modified
      </span>

      <span
        className="absolute top-2 right-2 z-30 bg-white/80 text-black text-xs px-2 py-1 rounded border border-black shadow-sm"
        aria-hidden="true"
      >
        Original
      </span>

      {/* Handle Line */}
      <div
        className="absolute top-0 bottom-0 flex items-center justify-center z-20 transition-colors duration-300"
        style={{ left: `${position}%`, transform: 'translateX(-50%)' }}
      >
        <div className="w-1 bg-white h-full shadow-md group-hover:bg-white group-hover:border-black" />
      </div>

      {/* Handle Button */}
      <div
        role="slider"
        tabIndex={0}
        aria-label="Image comparison slider"
        aria-valuemin={0}
        aria-valuemax={100}
        aria-valuenow={Math.round(position)}
        aria-orientation="horizontal"
        onMouseDown={startDrag}
        onTouchStart={startDrag}
        className="absolute top-1/2 left-1/2 z-30 flex items-center justify-center cursor-col-resize"
        style={{ left: `${position}%`, transform: 'translate(-50%, -50%)' }}
      >
        <div className="w-8 h-8 rounded-full bg-white border border-black flex items-center justify-center transition-colors duration-300">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 text-black"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 text-black"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth="2"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  )
}
