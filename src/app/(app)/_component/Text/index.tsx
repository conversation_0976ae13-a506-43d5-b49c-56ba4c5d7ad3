import clsx from 'clsx'
import React, { JSX } from 'react'

interface TextProps {
  variant?:
    | 'body'
    | 'description'
    | 'subtitle'
    | 'emphasis'
    | 'button'
    | 'small'
    | 'mono'
    | 'caption'
    | 'overline'
    | 'quote'
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl'
  as?: keyof JSX.IntrinsicElements // Allows using <span>, <div>, etc.
  children: React.ReactNode
  className?: string
}

const Text: React.FC<TextProps> = ({
  variant = 'body',
  size = 'base',
  as: Component = 'span',
  children,
  className,
}) => {
  const baseStyles = 'dark:text-white'

  const variantStyles = {
    body: 'tracking-tight text-black',
    description: 'text-gray-800 dark:text-gray-400',
    subtitle: 'uppercase tracking-widest text-gray-800 dark:text-gray-300',
    emphasis: 'font-bold text-black decoration-dashed decoration-black dark:decoration-white',
    button:
      'uppercase bg-black text-white px-3 py-1 rounded-md shadow-md hover:bg-white hover:text-black transition-colors',
    small: 'opacity-75',
    mono: 'font-mono tracking-widest',
    caption: 'text-gray-300 text-sm',
    overline: 'uppercase text-xs tracking-widest opacity-60',
    quote: 'border-l-4 border-black dark:border-white pl-4 italic',
  }

  const sizeStyles = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
  }

  return (
    <Component className={clsx(baseStyles, variantStyles[variant], sizeStyles[size], className)}>
      {children}
    </Component>
  )
}

export default Text
