export type YouTubeEmbedType = {
  blockType: 'youtubeEmbed'
  blockName?: string | null
  youtubeUrl?: string | null
  className?: string
}

function extractYouTubeVideoId(url: string): string | null {
  try {
    const parsed = new URL(url)
    if (parsed.hostname === 'youtu.be') {
      return parsed.pathname.slice(1)
    }

    if (parsed.hostname.includes('youtube.com')) {
      const id = parsed.searchParams.get('v')
      if (id) return id

      // Handle /embed/VIDEO_ID or /shorts/VIDEO_ID
      const pathMatch = parsed.pathname.match(/\/(embed|shorts)\/([a-zA-Z0-9_-]+)/)
      if (pathMatch) return pathMatch[2]
    }

    return null
  } catch {
    return null
  }
}

function YouTubeEmbed(props: YouTubeEmbedType) {
  const { youtubeUrl, className } = props
  if (!youtubeUrl) return null

  const videoId = extractYouTubeVideoId(youtubeUrl)
  if (!videoId) return null

  const embedUrl = `https://www.youtube.com/embed/${videoId}`

  return (
    <div className={`${className}`}>
      <iframe
        width="560"
        height="315"
        src={embedUrl}
        title="YouTube video player"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerPolicy="strict-origin-when-cross-origin"
        allowFullScreen
      />
    </div>
  )
}

export default YouTubeEmbed
