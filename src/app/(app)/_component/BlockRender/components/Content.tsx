import { Serialize } from '@/utilities/local/blog/ReactSerializer'
import { SerializedLexicalEditorState } from '@/utilities/local/blog/types'

export type ContentType = {
  blockType: 'content'
  blockName?: string | null
  richText?: SerializedLexicalEditorState | null
}

function Content(props: ContentType) {
  const { richText } = props
  if (!richText || !richText.root || !richText.root.children) {
    return null
  }
  return <Serialize nodes={richText.root.children} />
}

export default Content
