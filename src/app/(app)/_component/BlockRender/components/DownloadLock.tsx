import { Download, DownloadCloud, Lock } from 'lucide-react'
import { cookies } from 'next/headers'
import Link from 'next/link'
import { _verifyJWT } from '@/app/(app)/_backend/common/utils/auth'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import GridContainer from '@/app/(app)/_cssComp/GridContainer'
import { Button } from '../../Button'
import Card from '../../Card/CardSolo'
import Text from '../../Text'
import Title, { HeaderLevel } from '../../Title'

export type DownloadLockType = {
  blockType: 'downloadLock'
  blockName?: string | null
  title?: string | null
  description?: string | null
  downloadUrl?: string | null
}

async function DownloadLock(props: DownloadLockType) {
  const {
    title = 'Thank you for your support!',
    description = 'Download now!',
    downloadUrl,
  } = props
  const cookieStore = await cookies()
  const token = cookieStore.get('la-token')?.value

  try {
    await _verifyJWT(token)
    return (
      <Card className="bg-green-100">
        <FlexContainer
          direction={FlexDirection.COL}
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
          className="w-full h-full gap-2"
        >
          <Title level={HeaderLevel.H4}>
            <FlexContainer className="gap-2" direction={FlexDirection.ROW}>
              <DownloadCloud /> {title} <DownloadCloud />
            </FlexContainer>
          </Title>
          <Text variant="description">{description}</Text>
          {downloadUrl && (
            <Link href={downloadUrl} target="_blank" rel="noopener noreferrer">
              <Button variant="positive">
                <Download /> Download Now!
              </Button>
            </Link>
          )}
        </FlexContainer>
      </Card>
    )
  } catch (error) {
    return (
      <Card className="bg-red-100">
        <FlexContainer
          direction={FlexDirection.COL}
          align={AlignItems.CENTER}
          justify={JustifyContent.CENTER}
          className="w-full h-full gap-2"
        >
          <Title level={HeaderLevel.H4} className="">
            <FlexContainer className="gap-2" direction={FlexDirection.ROW}>
              <Lock /> {`Looks like you're not logged in!`} <Lock />
            </FlexContainer>
          </Title>
          <Text variant="description" className="mb-2">
            {`Log in or create an account to access this content.`}
          </Text>
          <FlexContainer
            className="w-full gap-2"
            direction={FlexDirection.ROW}
            justify={JustifyContent.CENTER}
            align={AlignItems.CENTER}
          >
            <Link href="/login">
              <Button variant="secondary">Login</Button>
            </Link>
            <Link href="/register">
              <Button variant="emphasis">Sign Up</Button>
            </Link>
          </FlexContainer>
        </FlexContainer>
      </Card>
      // <FlexContainer
      //   direction={FlexDirection.COL}
      //   align={AlignItems.CENTER}
      //   justify={JustifyContent.CENTER}
      //   className="bg-red-100 p-4 rounded-md shadow-md"
      // >
      //   <Title level={HeaderLevel.H5} className="text-red-600 text-center">
      //     {`Looks like you're not logged in!`}
      //   </Title>
      //   <Text variant="description" className="text-red-600">
      //     You must be logged in to access this content.
      //   </Text>
      // </FlexContainer>
    )
  }
}

export default DownloadLock
