import { ClassValue } from 'clsx'
import { Layout } from '@/collections/Posts'
import { cn } from '@/lib/utils'
import { components } from './components'

interface Props {
  layout: Layout[]
  className?: ClassValue
}

const BlockRender = ({ layout, className }: Props) => {
  return (
    <div className={cn('', className)}>
      {layout.map((block, i) => {
        const isLast = i === layout.length - 1
        const blockType = block.blockType as keyof typeof components
        console.log('BlockRender', blockType, block)
        const Block: React.FC<any> | undefined = components[blockType]

        if (Block) {
          return (
            <section key={i} className={isLast ? '' : 'mb-4'}>
              <Block {...block} />
            </section>
          )
        }

        return null
      })}
    </div>
  )
}

export default BlockRender
