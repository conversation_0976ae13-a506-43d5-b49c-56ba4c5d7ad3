'use client'

import NextImage from 'next/image'
import React, { useEffect, useState } from 'react'
import <PERSON>lex<PERSON>ontainer, { FlexDirection } from '@/app/(app)/_cssComp/FlexContainer'
import { cn } from '@/lib/utils'
import Text from '../../Text'

export const InfiniteMovingImages = ({
  items,
  direction = 'left',
  speed = 'fast',
  pauseOnHover = true,
  className,
}: {
  items: {
    imageUrl: string
    caption: string
  }[]
  direction?: 'left' | 'right'
  speed?: 'fast' | 'normal' | 'slow'
  pauseOnHover?: boolean
  className?: string
}) => {
  const containerRef = React.useRef<HTMLDivElement>(null)
  const scrollerRef = React.useRef<HTMLUListElement>(null)

  useEffect(() => {
    addAnimation()
  }, [])
  const [start, setStart] = useState(false)
  useEffect(() => {
    if (!start) {
      addAnimation()
    }
  }, [start])
  function addAnimation() {
    if (containerRef.current && scrollerRef.current) {
      const scrollerContent = Array.from(scrollerRef.current.children)

      scrollerContent.forEach((item) => {
        const duplicatedItem = item.cloneNode(true)
        if (scrollerRef.current) {
          scrollerRef.current.appendChild(duplicatedItem)
        }
      })

      getDirection()
      getSpeed()
      setStart(true)
    }
  }
  const getDirection = () => {
    if (containerRef.current) {
      if (direction === 'left') {
        containerRef.current.style.setProperty('--animation-direction', 'forwards')
      } else {
        containerRef.current.style.setProperty('--animation-direction', 'reverse')
      }
    }
  }
  const getSpeed = () => {
    if (containerRef.current) {
      if (speed === 'fast') {
        containerRef.current.style.setProperty('--animation-duration', '20s')
      } else if (speed === 'normal') {
        containerRef.current.style.setProperty('--animation-duration', '40s')
      } else {
        containerRef.current.style.setProperty('--animation-duration', '80s')
      }
    }
  }
  return (
    <div
      ref={containerRef}
      className={cn(
        'scroller relative z-20 w-full will-change-transform overflow-hidden mask-[linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]',
        className,
      )}
    >
      <ul
        ref={scrollerRef}
        className={cn(
          'flex w-max min-w-full shrink-0 flex-nowrap gap-4 py-4 will-change-transform',
          start && 'animate-scroll',
          pauseOnHover && 'hover:paused',
        )}
      >
        {items.map((item, idx) => (
          <li
            className="relative aspect-2/3 w-[256px] h-[450px] max-w-full shrink-0 rounded-2xl border border-b-0 dark:bg-[linear-gradient(180deg,#27272a,#18181b)]"
            key={item.imageUrl}
          >
            <FlexContainer direction={FlexDirection.COL} className="gap-4 h-full">
              <NextImage
                src={item.imageUrl}
                width={256}
                height={384}
                alt={item.caption}
                className="object-contain rounded-2xl"
                loading="eager"
              />
              <Text className="text-white text-center flex-wrap">{item.caption}</Text>
            </FlexContainer>
          </li>
        ))}
      </ul>
    </div>
  )
}
