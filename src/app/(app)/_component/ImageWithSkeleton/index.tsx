'use client'

import Image, { ImageProps } from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'
import { Skeleton } from '../Skeleton'

type ImageWithSkeletonProps = ImageProps & {
  skeletonClassName?: string
}

export default function ImageWithSkeleton({ skeletonClassName, ...props }: ImageWithSkeletonProps) {
  const [isLoading, setIsLoading] = useState(true)

  return (
    <div className={cn(`relative w-full h-full`, skeletonClassName)}>
      {isLoading && <Skeleton className="aspect-square w-full h-full bg-gray-500" />}
      <Image
        {...props}
        onLoad={() => {
          setIsLoading(false)
        }}
        className={cn(
          'transition-opacity duration-500 ease-in-out',
          props.className,
          !isLoading ? 'opacity-100' : 'opacity-0 absolute inset-0',
        )}
      />
    </div>
  )
}
