import { useQuery } from '@tanstack/react-query'
import { GeneratedImage } from '@/payload-types'
import { GeneratedImageStatus } from '@/types/GeneratedImageStatus'
import { fetchUserImages } from '../_localApi/imageGeneration'
import { ImageMetadata } from '../_types/ImageMetadata'

interface UseImagesPaginationOptions {
  page?: number
  pageSize?: number
  enabled?: boolean
  refetchInterval?: number | false
}

interface UseImagesPaginationResult {
  images: ImageMetadata[]
  pagination: {
    totalCount: number
    currentPage: number
    totalPages: number
    pageSize: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  isLoading: boolean
  error: Error | null
  refetch: () => void
}

export function useImagesPagination(
  options: UseImagesPaginationOptions = {},
): UseImagesPaginationResult {
  const { page = 1, pageSize = 5, enabled = true, refetchInterval = false } = options

  const { data, error, refetch } = useQuery({
    queryKey: ['images-pagination', page, pageSize],
    queryFn: async () => {
      console.log('query func called')
      const response = await fetchUserImages(page, pageSize)
      const generatedImages: GeneratedImage[] = response.data.images
      const pagination = response.data.pagination

      // Transform GeneratedImage[] to ImageMetadata[]
      const imagePromises = generatedImages.map((image: GeneratedImage) => {
        return {
          id: image.id,
          src: image.url,
          width: image.width as number,
          height: image.height as number,
          prompt: image.prompt as string,
          actionType: image.taskType as string,
          status: image.status as GeneratedImageStatus,
        }
      })

      const imagesMetadata = await Promise.all(imagePromises)
      const completedImages = imagesMetadata.filter(
        (image) => image !== null && image.status === GeneratedImageStatus.COMPLETED,
      ) as ImageMetadata[]
      const pendingImages = imagesMetadata.filter(
        (image) => image !== null && image.status === GeneratedImageStatus.GENERATING,
      )
      const isLoading = pendingImages.length > 0

      return {
        images: completedImages,
        pagination: pagination,
        isLoading,
      }
    },
    enabled,
    refetchInterval,
  })

  const defaultPagination = {
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    pageSize,
    hasNextPage: false,
    hasPrevPage: false,
  }

  return {
    images: data?.images || [],
    pagination: data?.pagination || defaultPagination,
    isLoading: data?.isLoading || false,
    error: error as Error | null,
    refetch,
  }
}
