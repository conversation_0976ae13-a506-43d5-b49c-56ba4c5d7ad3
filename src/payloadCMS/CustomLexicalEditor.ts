import { lexicalEditor, FixedToolbarFeature, BlocksFeature } from '@payloadcms/richtext-lexical'
import { DownloadLock } from '@/collections/Posts/blocks/DownloadLock'
import { YouTubeEmbed } from '@/collections/Posts/blocks/YouTubeEmbed'

export const CustomLexicalEditor = lexicalEditor({
  features: ({ defaultFeatures, rootFeatures }) => [
    ...defaultFeatures,
    FixedToolbarFeature(),
    BlocksFeature({
      blocks: [YouTubeEmbed, DownloadLock],
    }),
  ],
})

export default CustomLexicalEditor
