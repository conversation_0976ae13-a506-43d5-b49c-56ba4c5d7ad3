import formData from 'form-data'
import { createReadStream } from 'fs'

interface SendEmailOptions {
  to: string | string[]
  from: string
  subject: string
  text?: string
  html?: string
  attachments?: Array<{
    filename: string
    path: string
  }>
}

export class MailgunClient {
  private readonly domain: string
  private readonly apiKey: string

  constructor(domain: string, apiKey: string) {
    this.domain = domain
    this.apiKey = apiKey
  }

  async sendEmail(options: SendEmailOptions): Promise<void> {
    const form = new formData()

    // Required fields
    form.append('from', options.from)
    form.append('to', Array.isArray(options.to) ? options.to.join(', ') : options.to)
    form.append('subject', options.subject)

    // Content
    if (options.text) form.append('text', options.text)
    if (options.html) form.append('html', options.html)

    // Attachments
    if (options.attachments) {
      options.attachments.forEach((attachment) => {
        form.append('attachment', createReadStream(attachment.path), {
          filename: attachment.filename,
        })
      })
    }

    try {
      const response = await fetch(`https://api.mailgun.net/v3/${this.domain}/messages`, {
        method: 'POST',
        headers: {
          ...form.getHeaders(),
          Authorization: `Basic ${Buffer.from(`api:${this.apiKey}`).toString('base64')}`,
        },
        body: form.getBuffer(),
      })

      if (!response.ok) {
        const errorBody = await response.text()
        throw new Error(
          `Mailgun API error: ${response.status} ${response.statusText} - ${errorBody}`,
        )
      }
    } catch (error) {
      throw new Error(
        `Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`,
      )
    }
  }
}

// Create a default instance using environment variables
export const mailgun = new MailgunClient(process.env.MAILGUN_DOMAIN!, process.env.MAILGUN_API_KEY!)
